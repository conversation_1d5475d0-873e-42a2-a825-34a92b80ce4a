// 修复控制台错误的脚本

(function() {
    'use strict';
    
    console.log('🔧 开始修复控制台错误...');
    
    // 等待DataManager加载完成
    function waitForDataManager() {
        return new Promise((resolve) => {
            const checkDataManager = () => {
                if (window.dataManager) {
                    resolve(window.dataManager);
                } else {
                    setTimeout(checkDataManager, 100);
                }
            };
            checkDataManager();
        });
    }
    
    // 修复控制台错误
    async function fixConsoleErrors() {
        try {
            const dataManager = await waitForDataManager();
            console.log('✅ DataManager已加载');
            
            // 1. 修复Firebase相关错误
            console.log('🔧 修复Firebase相关错误...');
            
            // 检查Firebase是否禁用
            const isFirebaseDisabled = localStorage.getItem('disableFirebase') === 'true';
            if (isFirebaseDisabled) {
                console.log('ℹ️ Firebase已被用户禁用，跳过Firebase相关修复');
            } else {
                // 修复syncToCloud方法
                const originalSyncToCloud = dataManager.syncToCloud;
                dataManager.syncToCloud = async function() {
                    try {
                        if (!window.firebaseSync || !window.firebaseSync.isConnected()) {
                            console.log('⚠️ Firebase未连接，跳过云端同步');
                            return;
                        }
                        
                        // 调用原始方法，但添加错误处理
                        if (originalSyncToCloud) {
                            await originalSyncToCloud.call(this);
                        }
                    } catch (error) {
                        console.warn('⚠️ 云端同步失败，但不影响本地功能:', error.message);
                        // 不抛出错误，避免影响其他功能
                    }
                };
            }
            
            // 2. 修复Promise相关错误
            console.log('🔧 修复Promise相关错误...');
            
            // 修复forceRefreshShipped方法
            if (typeof dataManager.forceRefreshShipped === 'function') {
                const originalForceRefreshShipped = dataManager.forceRefreshShipped;
                dataManager.forceRefreshShipped = function() {
                    try {
                        return originalForceRefreshShipped.call(this);
                    } catch (error) {
                        console.error('❌ forceRefreshShipped执行出错:', error);
                        // 提供备用实现
                        this.updateStats();
                    }
                };
            }
            
            // 3. 修复数据加载错误
            console.log('🔧 修复数据加载错误...');
            
            // 确保所有必要的数组都已初始化
            if (!Array.isArray(dataManager.data)) {
                dataManager.data = [];
            }
            if (!Array.isArray(dataManager.shippingHistory)) {
                dataManager.shippingHistory = [];
            }
            if (!Array.isArray(dataManager.materialPurchases)) {
                dataManager.materialPurchases = [];
            }
            if (!Array.isArray(dataManager.operationLogs)) {
                dataManager.operationLogs = [];
            }
            
            // 4. 修复未捕获的Promise错误
            console.log('🔧 添加全局错误处理...');
            
            // 添加全局Promise错误处理
            window.addEventListener('unhandledrejection', function(event) {
                console.warn('⚠️ 未处理的Promise拒绝:', event.reason);
                
                // 如果是Firebase相关错误，静默处理
                if (event.reason && typeof event.reason === 'object') {
                    const errorMessage = event.reason.message || event.reason.toString();
                    if (errorMessage.includes('Firebase') || 
                        errorMessage.includes('firestore') || 
                        errorMessage.includes('syncToCloud')) {
                        console.log('ℹ️ Firebase相关错误已静默处理');
                        event.preventDefault(); // 阻止错误显示在控制台
                        return;
                    }
                }
                
                // 其他错误记录但不阻止
                console.log('ℹ️ 其他Promise错误已记录');
            });
            
            // 5. 修复数据同步相关错误
            console.log('🔧 修复数据同步相关错误...');
            
            // 修复performManualSync方法
            if (typeof dataManager.performManualSync === 'function') {
                const originalPerformManualSync = dataManager.performManualSync;
                dataManager.performManualSync = async function() {
                    try {
                        if (originalPerformManualSync) {
                            await originalPerformManualSync.call(this);
                        }
                    } catch (error) {
                        console.warn('⚠️ 手动同步失败:', error.message);
                        this.showNotification('同步失败，但本地功能正常', 'warning');
                        
                        // 确保恢复状态
                        this.isManualSyncing = false;
                        if (window.firebaseSync) {
                            try {
                                window.firebaseSync.resumeRealtimeSync();
                            } catch (e) {
                                console.warn('恢复实时同步失败:', e.message);
                            }
                        }
                    }
                };
            }
            
            // 6. 修复saveToLocalStorage方法
            console.log('🔧 增强saveToLocalStorage方法...');
            
            const originalSaveToLocalStorage = dataManager.saveToLocalStorage;
            dataManager.saveToLocalStorage = function() {
                try {
                    // 调用原始方法
                    if (originalSaveToLocalStorage) {
                        originalSaveToLocalStorage.call(this);
                    }
                    
                    // 确保数据完整性
                    if (this.data && this.data.length > 0) {
                        localStorage.setItem('productionData', JSON.stringify(this.data));
                    }
                    if (this.shippingHistory && this.shippingHistory.length > 0) {
                        localStorage.setItem('shippingHistory', JSON.stringify(this.shippingHistory));
                    }
                    if (this.materialPurchases && this.materialPurchases.length > 0) {
                        localStorage.setItem('materialPurchases', JSON.stringify(this.materialPurchases));
                    }
                    if (this.operationLogs && this.operationLogs.length > 0) {
                        localStorage.setItem('operationLogs', JSON.stringify(this.operationLogs));
                    }
                    
                } catch (error) {
                    console.error('❌ 保存到本地存储失败:', error);
                    this.showNotification('数据保存失败，请检查浏览器存储空间', 'error');
                }
            };
            
            // 7. 修复updateStats方法
            console.log('🔧 增强updateStats方法...');
            
            const originalUpdateStats = dataManager.updateStats;
            dataManager.updateStats = function() {
                try {
                    if (originalUpdateStats) {
                        originalUpdateStats.call(this);
                    }
                    
                    // 确保仪表板更新
                    if (window.dashboard) {
                        try {
                            window.dashboard.updateMetricsFromDataManager();
                            window.dashboard.updateMetrics();
                        } catch (error) {
                            console.warn('⚠️ 仪表板更新失败:', error.message);
                        }
                    }
                    
                } catch (error) {
                    console.error('❌ 统计更新失败:', error);
                }
            };
            
            // 8. 添加错误恢复机制
            console.log('🔧 添加错误恢复机制...');
            
            dataManager.recoverFromErrors = function() {
                console.log('🔄 开始错误恢复...');
                
                try {
                    // 重新加载本地数据
                    this.loadFromLocalStorage();
                    
                    // 重新渲染界面
                    this.renderTable();
                    this.updateStats();
                    this.renderAreaStats();
                    this.renderUnproducedStats();
                    
                    console.log('✅ 错误恢复完成');
                    this.showNotification('系统已自动恢复', 'success');
                    
                } catch (error) {
                    console.error('❌ 错误恢复失败:', error);
                    this.showNotification('系统恢复失败，请刷新页面', 'error');
                }
            };
            
            // 9. 清理控制台
            console.log('🧹 清理控制台...');
            
            // 添加清理控制台的方法
            window.clearConsoleErrors = function() {
                console.clear();
                console.log('🧹 控制台已清理');
                console.log('✅ 系统运行正常');
            };
            
            // 10. 添加系统健康检查
            dataManager.healthCheck = function() {
                console.log('🏥 开始系统健康检查...');
                
                const health = {
                    dataManager: !!window.dataManager,
                    dashboard: !!window.dashboard,
                    firebase: !!window.firebaseSync,
                    dataCount: this.data ? this.data.length : 0,
                    shippingCount: this.shippingHistory ? this.shippingHistory.length : 0,
                    materialCount: this.materialPurchases ? this.materialPurchases.length : 0,
                    localStorage: !!localStorage.getItem('productionData'),
                    errors: []
                };
                
                // 检查常见问题
                if (health.dataCount === 0) {
                    health.errors.push('没有生产数据');
                }
                
                if (!health.dashboard) {
                    health.errors.push('仪表板未加载');
                }
                
                if (!health.localStorage) {
                    health.errors.push('本地存储为空');
                }
                
                console.log('🏥 健康检查结果:', health);
                
                if (health.errors.length === 0) {
                    console.log('✅ 系统健康状态良好');
                } else {
                    console.log('⚠️ 发现以下问题:', health.errors);
                }
                
                return health;
            };
            
            console.log('✅ 控制台错误修复完成');
            
            // 立即执行一次健康检查
            setTimeout(() => {
                dataManager.healthCheck();
            }, 1000);
            
            return dataManager;
            
        } catch (error) {
            console.error('❌ 修复控制台错误时出错:', error);
            throw error;
        }
    }
    
    // 页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixConsoleErrors);
    } else {
        fixConsoleErrors();
    }
    
    // 导出到全局作用域，方便调试
    window.fixConsoleErrors = fixConsoleErrors;
    
})();

// 添加全局调试函数
window.healthCheck = function() {
    if (window.dataManager && window.dataManager.healthCheck) {
        return window.dataManager.healthCheck();
    } else {
        console.log('DataManager或健康检查方法不存在');
        return null;
    }
};

window.recoverFromErrors = function() {
    if (window.dataManager && window.dataManager.recoverFromErrors) {
        window.dataManager.recoverFromErrors();
    } else {
        console.log('DataManager或错误恢复方法不存在');
    }
};

console.log('🚀 控制台错误修复脚本已加载');
console.log('💡 可用的调试命令:');
console.log('  - healthCheck() - 系统健康检查');
console.log('  - recoverFromErrors() - 错误恢复');
console.log('  - clearConsoleErrors() - 清理控制台');
