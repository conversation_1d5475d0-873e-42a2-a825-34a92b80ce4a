// 终极修复方案 - 直接修改数据源和所有相关脚本
// 彻底解决3831顽固显示问题

(function() {
    'use strict';
    
    console.log('🚀 启动终极修复方案...');
    
    function ultimateFix() {
        if (!window.dataManager || !window.dashboard) {
            console.log('⏳ 等待组件加载...');
            setTimeout(ultimateFix, 500);
            return;
        }
        
        const dm = window.dataManager;
        const dashboard = window.dashboard;
        
        // 1. 获取正确的客户统计数据
        console.log('📊 获取正确的客户统计数据...');
        const customerStats = dm.calculateCustomerStats();
        const correctValue = customerStats.reduce((sum, customer) => {
            return sum + (customer.totalMeters || 0);
        }, 0);
        
        console.log(`✅ 正确的已发货量: ${correctValue.toFixed(1)}米`);
        
        // 2. 停止所有可能导致3831的脚本
        console.log('🛑 停止所有可能导致3831的脚本...');
        
        // 查找并停止所有包含3831的脚本
        const scripts = document.querySelectorAll('script[src*="force-shipped-3831"], script[src*="3831"]');
        scripts.forEach(script => {
            script.remove();
            console.log('🗑️ 移除了可疑脚本:', script.src);
        });
        
        // 3. 重写所有可能的更新方法
        console.log('🔧 重写所有更新方法...');
        
        // 重写dashboard的所有相关方法
        const originalMethods = {};
        
        ['updateMetrics', 'updateMetricsFromDataManager', 'animateNumber'].forEach(methodName => {
            if (typeof dashboard[methodName] === 'function') {
                originalMethods[methodName] = dashboard[methodName];
                
                dashboard[methodName] = function(...args) {
                    // 调用原方法
                    const result = originalMethods[methodName].apply(this, args);
                    
                    // 强制修正已发货量
                    if (this.data) {
                        this.data.shippedMeters = correctValue;
                        this.data.unshippedMeters = Math.max(0, (this.data.producedMeters || 0) - correctValue);
                    }
                    
                    // 强制修正DOM
                    const shippedEl = document.querySelector('.metric-card.shipped .metric-value');
                    if (shippedEl && shippedEl.textContent !== correctValue.toFixed(1)) {
                        shippedEl.textContent = correctValue.toFixed(1);
                        console.log(`🔧 ${methodName}中修正DOM: ${correctValue.toFixed(1)}米`);
                    }
                    
                    return result;
                };
                
                console.log(`✅ 已重写 dashboard.${methodName}`);
            }
        });
        
        // 4. 重写dataManager的相关方法
        if (typeof dm.updateStats === 'function') {
            const originalUpdateStats = dm.updateStats;
            dm.updateStats = function(...args) {
                const result = originalUpdateStats.apply(this, args);
                
                // 确保dashboard数据正确
                if (window.dashboard && window.dashboard.data) {
                    window.dashboard.data.shippedMeters = correctValue;
                    window.dashboard.data.unshippedMeters = Math.max(0, (window.dashboard.data.producedMeters || 0) - correctValue);
                }
                
                return result;
            };
            console.log('✅ 已重写 dataManager.updateStats');
        }
        
        // 5. 拦截所有可能设置3831的操作
        console.log('🛡️ 设置数据拦截器...');
        
        // 拦截Object.defineProperty
        const originalDefineProperty = Object.defineProperty;
        Object.defineProperty = function(obj, prop, descriptor) {
            if (prop === 'shippedMeters' && descriptor.value === 3831) {
                console.log('🛡️ 拦截了设置shippedMeters为3831的操作');
                descriptor.value = correctValue;
            }
            return originalDefineProperty.call(this, obj, prop, descriptor);
        };
        
        // 6. 强制更新所有数据源
        console.log('💪 强制更新所有数据源...');
        
        // 更新dashboard数据
        if (dashboard.data) {
            dashboard.data.shippedMeters = correctValue;
            dashboard.data.unshippedMeters = Math.max(0, (dashboard.data.producedMeters || 0) - correctValue);
            console.log('✅ 已更新dashboard.data');
        }
        
        // 更新localStorage中的相关数据
        try {
            const dashboardData = JSON.parse(localStorage.getItem('dashboardData') || '{}');
            if (dashboardData.shippedMeters) {
                dashboardData.shippedMeters = correctValue;
                localStorage.setItem('dashboardData', JSON.stringify(dashboardData));
                console.log('✅ 已更新localStorage中的dashboardData');
            }
        } catch (error) {
            console.log('⚠️ localStorage更新失败:', error);
        }
        
        // 7. 创建超级DOM监控器
        console.log('🔒 创建超级DOM监控器...');
        
        let isUpdating = false;
        
        const superObserver = new MutationObserver((mutations) => {
            if (isUpdating) return;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    const target = mutation.target;
                    
                    // 检查所有可能的已发货量元素
                    const checkElements = [
                        document.querySelector('.metric-card.shipped .metric-value'),
                        document.querySelector('#totalShipped'),
                        ...document.querySelectorAll('[class*="shipped"] .metric-value')
                    ].filter(el => el);
                    
                    checkElements.forEach(el => {
                        const currentText = el.textContent.trim();
                        if (currentText.includes('3831') || parseFloat(currentText) === 3831) {
                            isUpdating = true;
                            el.textContent = correctValue.toFixed(1);
                            console.log(`🔒 超级监控器修正: ${currentText} -> ${correctValue.toFixed(1)}`);
                            setTimeout(() => { isUpdating = false; }, 100);
                        }
                    });
                }
            });
        });
        
        superObserver.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true,
            attributes: true,
            attributeOldValue: true
        });
        
        // 8. 立即强制更新DOM
        console.log('🎨 立即强制更新DOM...');
        
        const updateDOM = () => {
            const elements = [
                document.querySelector('.metric-card.shipped .metric-value'),
                document.querySelector('#totalShipped'),
                ...document.querySelectorAll('[class*="shipped"] .metric-value'),
                ...document.querySelectorAll('*')
            ].filter(el => el && el.textContent && (el.textContent.includes('3831') || parseFloat(el.textContent) === 3831));
            
            elements.forEach(el => {
                el.textContent = correctValue.toFixed(1);
                el.style.cssText = `
                    background: linear-gradient(45deg, #059669, #10b981) !important;
                    color: white !important;
                    font-weight: bold !important;
                    padding: 8px 12px !important;
                    border-radius: 8px !important;
                    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.6) !important;
                    border: 2px solid #34d399 !important;
                `;
            });
            
            console.log(`🎨 更新了 ${elements.length} 个DOM元素`);
        };
        
        updateDOM();
        
        // 9. 每100毫秒强制检查一次
        const superInterval = setInterval(() => {
            updateDOM();
            
            // 确保数据对象正确
            if (dashboard.data && dashboard.data.shippedMeters !== correctValue) {
                dashboard.data.shippedMeters = correctValue;
                dashboard.data.unshippedMeters = Math.max(0, (dashboard.data.producedMeters || 0) - correctValue);
            }
        }, 100);
        
        // 10. 30秒后停止超级监控
        setTimeout(() => {
            clearInterval(superInterval);
            superObserver.disconnect();
            
            // 恢复样式
            document.querySelectorAll('.metric-card.shipped .metric-value').forEach(el => {
                el.style.cssText = '';
            });
            
            console.log('🎉 终极修复完成！');
            
            // 最终验证
            const finalValue = document.querySelector('.metric-card.shipped .metric-value')?.textContent || '0';
            console.log(`🔍 最终显示值: ${finalValue}`);
            
            if (parseFloat(finalValue) === correctValue) {
                console.log('🎉 终极修复成功！');
                if (dm.showNotification) {
                    dm.showNotification(
                        `🎉 终极修复成功！已发货量: ${correctValue.toFixed(1)}米`, 
                        'success'
                    );
                }
            } else {
                console.log('⚠️ 可能需要刷新页面以完全生效');
                if (dm.showNotification) {
                    dm.showNotification(
                        '⚠️ 修复完成，如仍显示错误请刷新页面', 
                        'warning'
                    );
                }
            }
        }, 30000);
        
        console.log('🚀 终极修复方案已启动！');
        console.log(`🎯 目标值: ${correctValue.toFixed(1)}米`);
        console.log('⏰ 将持续监控30秒...');
    }
    
    // 立即执行
    ultimateFix();
    
    console.log('✅ 终极修复脚本已启动');
    
})();
