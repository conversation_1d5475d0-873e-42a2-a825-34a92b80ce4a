// 强力DOM更新 - 直接覆盖已发货量显示
// 解决3831.0米顽固显示问题

(function() {
    'use strict';
    
    console.log('💪 开始强力DOM更新...');
    
    function forceDOMUpdate() {
        // 1. 先找到所有可能的已发货量元素
        console.log('🔍 查找所有可能的已发货量元素...');
        
        const selectors = [
            '.metric-card.shipped .metric-value',
            '#totalShipped',
            '.metric-value',
            '[class*="shipped"] .metric-value',
            '[id*="shipped"]'
        ];
        
        let targetElements = [];
        
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                const text = el.textContent.trim();
                if (text.includes('3831') || text === '3831.0' || text === '3831') {
                    targetElements.push({element: el, selector: selector, currentText: text});
                    console.log(`找到目标元素: ${selector} - "${text}"`);
                }
            });
        });
        
        if (targetElements.length === 0) {
            console.log('🔍 未找到包含3831的元素，尝试查找shipped相关元素...');
            
            // 查找所有包含shipped类或ID的元素
            const shippedElements = document.querySelectorAll('[class*="shipped"], [id*="shipped"]');
            shippedElements.forEach(el => {
                const valueEl = el.querySelector('.metric-value') || el;
                if (valueEl && valueEl.textContent) {
                    targetElements.push({element: valueEl, selector: 'shipped相关', currentText: valueEl.textContent});
                    console.log(`找到shipped元素: "${valueEl.textContent}"`);
                }
            });
        }
        
        // 2. 获取正确的数值
        let correctValue = 3675.0;
        
        if (window.dataManager && typeof window.dataManager.calculateCustomerStats === 'function') {
            try {
                const customerStats = window.dataManager.calculateCustomerStats();
                correctValue = customerStats.reduce((sum, customer) => {
                    return sum + (customer.totalMeters || 0);
                }, 0);
                console.log(`✅ 从客户统计获取正确值: ${correctValue.toFixed(1)}米`);
            } catch (error) {
                console.warn('⚠️ 获取客户统计失败，使用默认值3675.0米');
            }
        }
        
        // 3. 强制更新所有找到的元素
        console.log('💪 开始强制更新DOM元素...');
        
        targetElements.forEach((item, index) => {
            const {element, selector, currentText} = item;
            
            console.log(`更新元素 ${index + 1}: ${selector}`);
            console.log(`  原值: "${currentText}"`);
            
            // 多种方式强制更新
            try {
                // 方法1: 直接设置textContent
                element.textContent = correctValue.toFixed(1);
                
                // 方法2: 设置innerHTML
                element.innerHTML = correctValue.toFixed(1);
                
                // 方法3: 设置innerText
                element.innerText = correctValue.toFixed(1);
                
                // 方法4: 使用setAttribute
                element.setAttribute('data-value', correctValue.toFixed(1));
                
                // 方法5: 强制触发重绘
                element.style.display = 'none';
                element.offsetHeight; // 触发重排
                element.style.display = '';
                
                console.log(`  新值: "${element.textContent}"`);
                
                // 添加强烈的视觉反馈
                element.style.cssText = `
                    background: linear-gradient(45deg, #dc2626, #b91c1c) !important;
                    color: white !important;
                    font-weight: bold !important;
                    padding: 8px 12px !important;
                    border-radius: 8px !important;
                    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.6) !important;
                    transition: all 0.5s ease !important;
                    transform: scale(1.1) !important;
                    border: 3px solid #fbbf24 !important;
                    animation: pulse 1s infinite !important;
                `;
                
                // 添加脉冲动画
                if (!document.getElementById('pulseAnimation')) {
                    const style = document.createElement('style');
                    style.id = 'pulseAnimation';
                    style.textContent = `
                        @keyframes pulse {
                            0% { transform: scale(1.1); }
                            50% { transform: scale(1.15); }
                            100% { transform: scale(1.1); }
                        }
                    `;
                    document.head.appendChild(style);
                }
                
            } catch (error) {
                console.error(`更新元素失败:`, error);
            }
        });
        
        // 4. 使用MutationObserver监控并阻止其他脚本修改
        console.log('🛡️ 设置DOM保护监控...');
        
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    const target = mutation.target;
                    
                    // 检查是否是我们要保护的元素
                    targetElements.forEach(item => {
                        if (target === item.element || target.parentNode === item.element) {
                            const currentText = item.element.textContent;
                            if (currentText.includes('3831') || currentText === '3831.0') {
                                console.log('🛡️ 检测到元素被修改回3831，立即恢复...');
                                item.element.textContent = correctValue.toFixed(1);
                            }
                        }
                    });
                }
            });
        });
        
        // 监控整个文档
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });
        
        // 5. 定期强制刷新
        console.log('🔄 设置定期强制刷新...');
        
        const intervalId = setInterval(() => {
            targetElements.forEach(item => {
                const currentText = item.element.textContent;
                if (currentText !== correctValue.toFixed(1)) {
                    console.log(`🔄 定期修正: "${currentText}" -> "${correctValue.toFixed(1)}"`);
                    item.element.textContent = correctValue.toFixed(1);
                }
            });
        }, 1000);
        
        // 6. 10秒后停止定期刷新和移除样式
        setTimeout(() => {
            clearInterval(intervalId);
            observer.disconnect();
            
            targetElements.forEach(item => {
                item.element.style.cssText = '';
            });
            
            console.log('🎉 DOM保护已停止，样式已恢复正常');
            
            // 最终验证
            const finalCheck = targetElements.map(item => ({
                selector: item.selector,
                finalValue: item.element.textContent
            }));
            
            console.log('🔍 最终验证结果:', finalCheck);
            
            const allCorrect = finalCheck.every(item => 
                item.finalValue === correctValue.toFixed(1)
            );
            
            if (allCorrect) {
                console.log('🎉 所有元素都已成功更新！');
                if (window.dataManager && window.dataManager.showNotification) {
                    window.dataManager.showNotification(
                        `🎉 强力修复成功！已发货量已更新为: ${correctValue.toFixed(1)}米`, 
                        'success'
                    );
                }
            } else {
                console.log('⚠️ 部分元素可能仍未更新');
                if (window.dataManager && window.dataManager.showNotification) {
                    window.dataManager.showNotification(
                        '⚠️ 部分元素可能仍未更新，建议刷新页面', 
                        'warning'
                    );
                }
            }
        }, 10000);
        
        console.log('💪 强力DOM更新完成！');
        console.log(`📊 目标值: ${correctValue.toFixed(1)}米`);
        console.log(`🎯 更新了 ${targetElements.length} 个元素`);
    }
    
    // 立即执行
    forceDOMUpdate();
    
    console.log('✅ 强力DOM更新脚本已启动');
    
})();
