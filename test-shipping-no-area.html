<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发货功能测试 - 无区域限制</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            color: #059669;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-info {
            background: #eff6ff;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .test-info h3 {
            color: #3b82f6;
            margin: 0 0 15px 0;
        }
        
        .test-info ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .test-info li {
            margin: 8px 0;
        }
        
        .big-button {
            background: #059669;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            width: calc(50% - 20px);
            box-sizing: border-box;
        }
        
        .big-button:hover {
            background: #047857;
        }
        
        .big-button.secondary {
            background: #3b82f6;
        }
        
        .big-button.secondary:hover {
            background: #2563eb;
        }
        
        .result {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .success {
            background: #ecfdf5;
            border: 2px solid #059669;
            color: #059669;
        }
        
        .info {
            background: #eff6ff;
            border: 2px solid #3b82f6;
            color: #3b82f6;
        }
        
        .warning {
            background: #fffbeb;
            border: 2px solid #f59e0b;
            color: #f59e0b;
        }
        
        .error {
            background: #fef2f2;
            border: 2px solid #dc2626;
            color: #dc2626;
        }
        
        .inventory-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .inventory-table th,
        .inventory-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .inventory-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }
        
        .inventory-table tr:hover {
            background: #f9fafb;
        }
        
        .spec-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .spec-name {
            font-weight: 600;
            color: #1f2937;
        }
        
        .spec-areas {
            font-size: 12px;
            color: #6b7280;
            font-style: italic;
        }
        
        .number-cell {
            text-align: right;
            font-weight: 500;
        }
        
        .highlight {
            background: #fef3c7 !important;
            border-left: 4px solid #f59e0b;
        }
        
        .data-summary {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .summary-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        
        .summary-label {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 5px;
        }
        
        .summary-value {
            font-size: 18px;
            font-weight: bold;
            color: #059669;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚚 发货功能测试 - 无区域限制</h1>
        
        <div class="test-info">
            <h3>🎯 发货功能改进说明</h3>
            <ul>
                <li><strong>移除区域限制</strong>：不再需要选择工地区域</li>
                <li><strong>总库存发货</strong>：直接从所有库存中选择规格发货</li>
                <li><strong>规格合并</strong>：相同规格的库存自动合并显示</li>
                <li><strong>智能分配</strong>：发货时自动从多个区域的库存中分配</li>
                <li><strong>区域显示</strong>：在规格下方显示涉及的区域信息</li>
            </ul>
        </div>
        
        <div style="display: flex; gap: 20px; flex-wrap: wrap;">
            <button class="big-button" onclick="loadInventoryData()">
                📊 加载库存数据
            </button>
            
            <button class="big-button secondary" onclick="openShippingModal()">
                🚚 打开发货界面
            </button>
        </div>
        
        <div class="data-summary" id="inventorySummary" style="display: none;">
            <h4>📋 库存统计</h4>
            <div class="summary-grid" id="summaryGrid">
                <!-- 统计数据将在这里显示 -->
            </div>
        </div>
        
        <div id="inventoryTable" style="display: none;">
            <h4>📦 可发货库存明细</h4>
            <table class="inventory-table">
                <thead>
                    <tr>
                        <th>规格型号</th>
                        <th>可发货数量</th>
                        <th>涉及区域</th>
                        <th>合计米数</th>
                    </tr>
                </thead>
                <tbody id="inventoryTableBody">
                    <!-- 库存数据将在这里显示 -->
                </tbody>
            </table>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const result = document.createElement('div');
            result.className = 'result ' + type;
            result.innerHTML = message;
            resultsDiv.appendChild(result);
        }
        
        function loadInventoryData() {
            addResult('🔍 加载库存数据...', 'info');
            
            const productionData = localStorage.getItem('productionData');
            if (!productionData) {
                addResult('❌ 没有找到生产数据，使用模拟数据', 'warning');
                generateMockInventory();
                return;
            }
            
            try {
                const data = JSON.parse(productionData);
                processInventoryData(data);
                addResult(`✅ 成功加载库存数据`, 'success');
            } catch (error) {
                addResult(`❌ 数据加载失败：${error.message}`, 'error');
                generateMockInventory();
            }
        }
        
        function processInventoryData(data) {
            // 筛选出可发货的项目
            const availableItems = data.filter(item =>
                item.produced > item.shipped &&
                item.status !== 'planned'
            );
            
            if (availableItems.length === 0) {
                addResult('📦 暂无可发货项目', 'info');
                return;
            }
            
            // 按规格合并库存
            const mergedInventory = mergeInventoryBySpec(availableItems);
            
            // 显示统计信息
            showInventorySummary(mergedInventory, availableItems);
            
            // 显示库存表格
            showInventoryTable(mergedInventory);
            
            addResult(`📊 共 ${mergedInventory.length} 种规格可发货`, 'success');
        }
        
        function mergeInventoryBySpec(items) {
            const specMap = new Map();

            items.forEach(item => {
                const spec = item.spec;
                const available = item.produced - item.shipped;

                if (available > 0) {
                    if (specMap.has(spec)) {
                        const existing = specMap.get(spec);
                        existing.totalAvailable += available;
                        existing.areas.add(item.area);
                        existing.items.push(item);
                    } else {
                        specMap.set(spec, {
                            spec: spec,
                            totalAvailable: available,
                            areas: new Set([item.area]),
                            items: [item]
                        });
                    }
                }
            });

            // 转换为数组并排序
            return Array.from(specMap.values()).sort((a, b) => {
                // 按型号排序（H80, H100等）
                const typeA = a.spec.match(/^(H\\d+)/)?.[1] || 'Z';
                const typeB = b.spec.match(/^(H\\d+)/)?.[1] || 'Z';
                
                if (typeA !== typeB) {
                    return typeA.localeCompare(typeB);
                }
                
                // 同型号内按规格名称排序
                return a.spec.localeCompare(b.spec);
            });
        }
        
        function showInventorySummary(mergedInventory, originalItems) {
            const summaryDiv = document.getElementById('inventorySummary');
            const summaryGrid = document.getElementById('summaryGrid');
            
            const totalSpecs = mergedInventory.length;
            const totalQuantity = mergedInventory.reduce((sum, item) => sum + item.totalAvailable, 0);
            const totalAreas = new Set(originalItems.map(item => item.area)).size;
            
            // 按型号统计
            const typeStats = {};
            mergedInventory.forEach(item => {
                const typeMatch = item.spec.match(/^(H\\d+)/);
                const type = typeMatch ? typeMatch[1] : '其他';
                
                if (!typeStats[type]) {
                    typeStats[type] = { specs: 0, quantity: 0 };
                }
                typeStats[type].specs++;
                typeStats[type].quantity += item.totalAvailable;
            });
            
            let summaryHTML = `
                <div class="summary-item">
                    <div class="summary-label">可发货规格</div>
                    <div class="summary-value">${totalSpecs} 种</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">可发货总量</div>
                    <div class="summary-value">${totalQuantity.toLocaleString()} 根</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">涉及区域</div>
                    <div class="summary-value">${totalAreas} 个</div>
                </div>
            `;
            
            // 添加型号统计
            Object.keys(typeStats).sort().forEach(type => {
                const stats = typeStats[type];
                summaryHTML += `
                    <div class="summary-item">
                        <div class="summary-label">${type} 型号</div>
                        <div class="summary-value">${stats.specs}种 / ${stats.quantity.toLocaleString()}根</div>
                    </div>
                `;
            });
            
            summaryGrid.innerHTML = summaryHTML;
            summaryDiv.style.display = 'block';
        }
        
        function showInventoryTable(mergedInventory) {
            const tableDiv = document.getElementById('inventoryTable');
            const tableBody = document.getElementById('inventoryTableBody');
            
            let tableHTML = '';
            mergedInventory.forEach(item => {
                const areasText = Array.from(item.areas).join(', ');
                const meters = calculateMeters(item.spec, item.totalAvailable);
                
                tableHTML += `
                    <tr>
                        <td>
                            <div class="spec-info">
                                <div class="spec-name">${item.spec}</div>
                                <div class="spec-areas">库存分布: ${areasText}</div>
                            </div>
                        </td>
                        <td class="number-cell">${item.totalAvailable.toLocaleString()} 根</td>
                        <td>${areasText}</td>
                        <td class="number-cell">${meters.toFixed(1)} m</td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = tableHTML;
            tableDiv.style.display = 'block';
        }
        
        function calculateMeters(spec, quantity) {
            const match = spec.match(/^(H\\d+)-(\\d+)mm$/);
            if (match) {
                const length = parseInt(match[2]) / 1000; // 转换为米
                return quantity * length;
            }
            return 0;
        }
        
        function generateMockInventory() {
            const mockData = [
                { spec: 'H80-1200mm', totalAvailable: 150, areas: new Set(['C1', 'C2']) },
                { spec: 'H80-1400mm', totalAvailable: 200, areas: new Set(['C1', 'E3']) },
                { spec: 'H100-1200mm', totalAvailable: 120, areas: new Set(['D6']) },
                { spec: 'H100-1400mm', totalAvailable: 180, areas: new Set(['A14', 'E3']) },
                { spec: 'H100-1600mm', totalAvailable: 90, areas: new Set(['C2', 'D6']) }
            ];
            
            showInventorySummary(mockData, []);
            showInventoryTable(mockData);
            addResult('📊 使用模拟库存数据', 'info');
        }
        
        function openShippingModal() {
            addResult('🚚 在主系统中打开发货界面...', 'info');
            window.open('index.html', '_blank');
            addResult('💡 提示：在主系统中点击"发货"按钮，现在无需选择区域即可直接加载所有可发货项目', 'info');
        }
        
        // 页面加载时自动加载数据
        window.onload = function() {
            setTimeout(loadInventoryData, 500);
        };
    </script>
</body>
</html>
