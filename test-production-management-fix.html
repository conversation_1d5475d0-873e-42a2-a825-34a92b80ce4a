<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产数据管理功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        button:hover {
            background: #0056b3;
        }
        button.success {
            background: #28a745;
        }
        button.success:hover {
            background: #1e7e34;
        }
        button.warning {
            background: #ffc107;
            color: #212529;
        }
        button.warning:hover {
            background: #e0a800;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            border-left: 4px solid #17a2b8;
        }
        .success-result {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error-result {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
    <!-- 加载必要的脚本 -->
    <script src="scripts/data-management.js"></script>
</head>
<body>
    <div class="container">
        <h1>🔧 生产数据管理功能测试</h1>
        
        <div class="info-box">
            <h4>📋 测试说明</h4>
            <p>这个页面用于测试生产数据管理功能的修复情况。请按顺序执行测试步骤。</p>
        </div>

        <div class="test-section">
            <h3>1️⃣ 基础环境检查</h3>
            <div class="button-group">
                <button onclick="checkEnvironment()">检查环境</button>
                <button onclick="checkDataManager()">检查数据管理器</button>
                <button onclick="checkModalElements()">检查模态框元素</button>
            </div>
            <div id="environmentResults"></div>
        </div>

        <div class="test-section">
            <h3>2️⃣ 按钮功能测试</h3>
            <div class="button-group">
                <button onclick="testProductionManagementButton()">测试生产数据管理按钮</button>
                <button onclick="testProducedCardClick()">测试已生产量卡片点击</button>
                <button onclick="simulateButtonClick()">模拟按钮点击</button>
            </div>
            <div id="buttonResults"></div>
        </div>

        <div class="test-section">
            <h3>3️⃣ 数据提取测试</h3>
            <div class="button-group">
                <button onclick="testDataExtraction()">测试数据提取</button>
                <button onclick="testStatisticsCalculation()">测试统计计算</button>
                <button onclick="addTestData()">添加测试数据</button>
            </div>
            <div id="dataResults"></div>
        </div>

        <div class="test-section">
            <h3>4️⃣ 模态框功能测试</h3>
            <div class="button-group">
                <button onclick="openProductionModal()">打开生产数据管理</button>
                <button onclick="testModalInitialization()">测试模态框初始化</button>
                <button onclick="closeAllModals()">关闭所有模态框</button>
            </div>
            <div id="modalResults"></div>
        </div>

        <div class="test-section">
            <h3>5️⃣ 综合测试</h3>
            <div class="button-group">
                <button class="success" onclick="runFullTest()">运行完整测试</button>
                <button class="warning" onclick="resetTestEnvironment()">重置测试环境</button>
            </div>
            <div id="fullTestResults"></div>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info', containerId = 'environmentResults') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type === 'success' ? 'success-result' : type === 'error' ? 'error-result' : ''}`;
            
            const statusIcon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            div.textContent = `${statusIcon} ${message}`;
            container.appendChild(div);
        }

        function checkEnvironment() {
            const container = document.getElementById('environmentResults');
            container.innerHTML = '';
            
            addResult('检查基础环境...', 'info', 'environmentResults');
            
            // 检查必要的全局对象
            const checks = [
                { name: 'window.dataManager', exists: !!window.dataManager },
                { name: 'localStorage', exists: !!window.localStorage },
                { name: 'Chart.js', exists: !!window.Chart },
                { name: 'document.getElementById', exists: !!document.getElementById }
            ];
            
            checks.forEach(check => {
                addResult(`${check.name}: ${check.exists ? '存在' : '不存在'}`, 
                         check.exists ? 'success' : 'error', 'environmentResults');
            });
        }

        function checkDataManager() {
            const container = document.getElementById('environmentResults');
            
            if (!window.dataManager) {
                addResult('dataManager 不存在，尝试创建...', 'error', 'environmentResults');
                
                // 尝试加载脚本
                const script = document.createElement('script');
                script.src = 'scripts/data-management.js';
                script.onload = () => {
                    addResult('data-management.js 脚本已加载', 'success', 'environmentResults');
                    
                    if (typeof DataManager !== 'undefined') {
                        window.dataManager = new DataManager();
                        addResult('DataManager 实例已创建', 'success', 'environmentResults');
                    }
                };
                document.head.appendChild(script);
                return;
            }
            
            addResult('dataManager 存在', 'success', 'environmentResults');
            
            // 检查关键方法
            const methods = [
                'openProductionManagementModal',
                'initProductionManagement', 
                'extractProductionRecords',
                'updateProductionStats'
            ];
            
            methods.forEach(method => {
                const exists = typeof window.dataManager[method] === 'function';
                addResult(`方法 ${method}: ${exists ? '存在' : '不存在'}`, 
                         exists ? 'success' : 'error', 'environmentResults');
            });
        }

        function checkModalElements() {
            const container = document.getElementById('environmentResults');
            
            const elements = [
                'productionManagementModal',
                'productionManagementBtn',
                'totalProductionRecords',
                'totalProducedQuantity',
                'totalProductionSpecs',
                'totalProductionAreas'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                addResult(`元素 ${id}: ${element ? '存在' : '不存在'}`, 
                         element ? 'success' : 'error', 'environmentResults');
            });
        }

        function testProductionManagementButton() {
            const container = document.getElementById('buttonResults');
            container.innerHTML = '';
            
            const button = document.getElementById('productionManagementBtn');
            if (!button) {
                addResult('生产数据管理按钮不存在', 'error', 'buttonResults');
                return;
            }
            
            addResult('找到生产数据管理按钮', 'success', 'buttonResults');
            
            // 检查事件监听器
            const hasClickListener = button.onclick || button.addEventListener;
            addResult(`按钮事件监听器: ${hasClickListener ? '已绑定' : '未绑定'}`, 
                     hasClickListener ? 'success' : 'error', 'buttonResults');
        }

        function simulateButtonClick() {
            const container = document.getElementById('buttonResults');
            
            if (!window.dataManager) {
                addResult('dataManager 不存在，无法模拟点击', 'error', 'buttonResults');
                return;
            }
            
            try {
                addResult('模拟点击生产数据管理按钮...', 'info', 'buttonResults');
                window.dataManager.openProductionManagementModal();
                addResult('模拟点击成功', 'success', 'buttonResults');
            } catch (error) {
                addResult(`模拟点击失败: ${error.message}`, 'error', 'buttonResults');
            }
        }

        function testDataExtraction() {
            const container = document.getElementById('dataResults');
            container.innerHTML = '';
            
            if (!window.dataManager) {
                addResult('dataManager 不存在', 'error', 'dataResults');
                return;
            }
            
            try {
                const records = window.dataManager.extractProductionRecords();
                addResult(`提取生产记录成功，共 ${records.length} 条`, 'success', 'dataResults');
                
                if (records.length > 0) {
                    addResult(`记录样本: ${JSON.stringify(records[0], null, 2)}`, 'info', 'dataResults');
                }
            } catch (error) {
                addResult(`数据提取失败: ${error.message}`, 'error', 'dataResults');
            }
        }

        function addTestData() {
            const container = document.getElementById('dataResults');
            
            if (!window.dataManager) {
                addResult('dataManager 不存在', 'error', 'dataResults');
                return;
            }
            
            // 添加测试数据
            const testData = {
                id: Date.now(),
                spec: 'H100-1400mm',
                area: 'C1',
                planned: 10,
                produced: 2,
                shipped: 0,
                status: 'producing',
                deadline: '2025-06-25',
                remarks: '测试数据'
            };
            
            window.dataManager.data.push(testData);
            window.dataManager.saveToLocalStorage();
            
            addResult('测试数据已添加', 'success', 'dataResults');
        }

        function openProductionModal() {
            const container = document.getElementById('modalResults');
            container.innerHTML = '';
            
            if (!window.dataManager) {
                addResult('dataManager 不存在', 'error', 'modalResults');
                return;
            }
            
            try {
                window.dataManager.openProductionManagementModal();
                addResult('生产数据管理模态框已打开', 'success', 'modalResults');
            } catch (error) {
                addResult(`打开模态框失败: ${error.message}`, 'error', 'modalResults');
            }
        }

        function runFullTest() {
            const container = document.getElementById('fullTestResults');
            container.innerHTML = '';
            
            addResult('开始运行完整测试...', 'info', 'fullTestResults');
            
            // 依次执行所有测试
            setTimeout(() => {
                checkEnvironment();
                addResult('环境检查完成', 'success', 'fullTestResults');
            }, 100);
            
            setTimeout(() => {
                checkDataManager();
                addResult('数据管理器检查完成', 'success', 'fullTestResults');
            }, 500);
            
            setTimeout(() => {
                testDataExtraction();
                addResult('数据提取测试完成', 'success', 'fullTestResults');
            }, 1000);
            
            setTimeout(() => {
                openProductionModal();
                addResult('模态框测试完成', 'success', 'fullTestResults');
            }, 1500);
            
            setTimeout(() => {
                addResult('🎉 完整测试执行完毕！请检查各项结果。', 'success', 'fullTestResults');
            }, 2000);
        }

        // 页面加载时自动检查环境
        window.addEventListener('load', () => {
            // 初始化 dataManager
            if (typeof DataManager !== 'undefined' && !window.dataManager) {
                console.log('初始化 DataManager...');
                window.dataManager = new DataManager();

                // 加载测试数据
                const testData = [
                    {
                        id: 1,
                        spec: 'H100-1400mm',
                        area: 'C1',
                        planned: 10,
                        produced: 2,
                        shipped: 0,
                        status: 'producing',
                        deadline: '2025-06-25',
                        remarks: '测试数据1'
                    },
                    {
                        id: 2,
                        spec: 'H80-1200mm',
                        area: 'E1',
                        planned: 15,
                        produced: 5,
                        shipped: 0,
                        status: 'producing',
                        deadline: '2025-06-26',
                        remarks: '测试数据2'
                    }
                ];

                window.dataManager.data = testData;
                console.log('测试数据已加载:', testData.length, '条');
            }

            setTimeout(checkEnvironment, 500);
        });
    </script>
</body>
</html>
