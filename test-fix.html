<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            color: #059669;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-result {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .success {
            background: #ecfdf5;
            border: 2px solid #059669;
            color: #059669;
        }
        
        .error {
            background: #fef2f2;
            border: 2px solid #dc2626;
            color: #dc2626;
        }
        
        .info {
            background: #eff6ff;
            border: 2px solid #3b82f6;
            color: #3b82f6;
        }
        
        .big-button {
            background: #059669;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            width: 100%;
            box-sizing: border-box;
        }
        
        .big-button:hover {
            background: #047857;
        }
        
        .big-button.danger {
            background: #dc2626;
        }
        
        .big-button.danger:hover {
            background: #b91c1c;
        }
        
        .data-info {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 数据修复测试</h1>
        
        <div id="testResults"></div>
        
        <button class="big-button" onclick="runTests()">
            🧪 运行数据检测
        </button>
        
        <button class="big-button" onclick="fixData()" id="fixBtn" style="display: none;">
            🚀 修复数据问题
        </button>
        
        <button class="big-button" onclick="openMainPage()">
            📊 打开主页面
        </button>
        
        <button class="big-button danger" onclick="clearAllData()">
            🗑️ 清空所有数据（危险操作）
        </button>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const result = document.createElement('div');
            result.className = 'test-result ' + type;
            result.innerHTML = message;
            resultsDiv.appendChild(result);
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }
        
        function runTests() {
            clearResults();
            addResult('🔍 开始检测数据状态...', 'info');
            
            // 检查localStorage数据
            const productionData = localStorage.getItem('productionData');
            const operationLogs = localStorage.getItem('operationLogs');
            const materialPurchases = localStorage.getItem('materialPurchases');
            
            if (productionData) {
                try {
                    const data = JSON.parse(productionData);
                    addResult(`✅ 找到生产数据：${data.length} 条记录`, 'success');
                    
                    // 显示数据详情
                    const dataInfo = document.createElement('div');
                    dataInfo.className = 'data-info';
                    dataInfo.innerHTML = `
                        <strong>数据详情：</strong><br>
                        - 总记录数：${data.length}<br>
                        - 第一条记录：${JSON.stringify(data[0], null, 2)}<br>
                        - 最后一条记录：${JSON.stringify(data[data.length - 1], null, 2)}
                    `;
                    document.getElementById('testResults').appendChild(dataInfo);
                    
                } catch (error) {
                    addResult(`❌ 生产数据格式错误：${error.message}`, 'error');
                }
            } else {
                addResult('❌ 没有找到生产数据', 'error');
            }
            
            if (operationLogs) {
                try {
                    const logs = JSON.parse(operationLogs);
                    addResult(`✅ 找到操作日志：${logs.length} 条`, 'success');
                } catch (error) {
                    addResult(`❌ 操作日志格式错误：${error.message}`, 'error');
                }
            } else {
                addResult('⚠️ 没有找到操作日志', 'info');
            }
            
            if (materialPurchases) {
                try {
                    const materials = JSON.parse(materialPurchases);
                    addResult(`✅ 找到原材料记录：${materials.length} 条`, 'success');
                } catch (error) {
                    addResult(`❌ 原材料记录格式错误：${error.message}`, 'error');
                }
            } else {
                addResult('⚠️ 没有找到原材料记录', 'info');
            }
            
            // 检查DataManager类是否存在
            if (typeof DataManager !== 'undefined') {
                addResult('✅ DataManager类已加载', 'success');
            } else {
                addResult('❌ DataManager类未加载', 'error');
            }
            
            // 检查window.dataManager实例
            if (window.dataManager) {
                addResult('✅ window.dataManager实例存在', 'success');
                addResult(`📊 实例数据条数：${window.dataManager.data ? window.dataManager.data.length : '未知'}`, 'info');
            } else {
                addResult('❌ window.dataManager实例不存在', 'error');
                document.getElementById('fixBtn').style.display = 'block';
            }
        }
        
        function fixData() {
            addResult('🔧 开始修复数据...', 'info');
            
            try {
                // 尝试创建DataManager实例
                if (typeof DataManager !== 'undefined') {
                    window.dataManager = new DataManager();
                    addResult('✅ DataManager实例创建成功', 'success');
                    
                    // 检查数据加载
                    if (window.dataManager.data && window.dataManager.data.length > 0) {
                        addResult(`✅ 数据加载成功：${window.dataManager.data.length} 条记录`, 'success');
                    } else {
                        addResult('⚠️ 数据为空，尝试重新加载...', 'info');
                        window.dataManager.loadFromLocalStorage();
                        
                        if (window.dataManager.data && window.dataManager.data.length > 0) {
                            addResult(`✅ 重新加载成功：${window.dataManager.data.length} 条记录`, 'success');
                        } else {
                            addResult('❌ 重新加载失败', 'error');
                        }
                    }
                    
                    document.getElementById('fixBtn').style.display = 'none';
                } else {
                    addResult('❌ DataManager类不存在，无法修复', 'error');
                }
            } catch (error) {
                addResult(`❌ 修复失败：${error.message}`, 'error');
            }
        }
        
        function openMainPage() {
            window.open('index.html', '_blank');
        }
        
        function clearAllData() {
            if (confirm('⚠️ 警告：这将删除所有数据！\n\n确定要继续吗？')) {
                localStorage.removeItem('productionData');
                localStorage.removeItem('operationLogs');
                localStorage.removeItem('materialPurchases');
                localStorage.removeItem('customAreas');
                
                addResult('🗑️ 所有数据已清空', 'error');
                
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        }
        
        // 页面加载时自动运行测试
        window.onload = function() {
            setTimeout(runTests, 500);
        };
    </script>
</body>
</html>
