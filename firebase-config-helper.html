<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase配置助手</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1f2937;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .step h3 {
            color: #3b82f6;
            margin-top: 0;
        }
        .config-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
        }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn:hover {
            background: #2563eb;
        }
        .btn-success {
            background: #10b981;
        }
        .btn-success:hover {
            background: #059669;
        }
        .btn-danger {
            background: #ef4444;
        }
        .btn-danger:hover {
            background: #dc2626;
        }
        .result {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 13px;
        }
        .result.success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        .result.error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        .result.info {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            color: #1e40af;
        }
        .current-config {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 15px;
            font-family: monospace;
            font-size: 13px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Firebase配置助手</h1>
        
        <div class="step">
            <h3>步骤1：检查当前配置</h3>
            <p>当前系统中的Firebase配置：</p>
            <div class="current-config" id="currentConfig">正在加载...</div>
            <button class="btn" onclick="checkCurrentConfig()">刷新配置</button>
            <button class="btn btn-success" onclick="testConnection()">测试连接</button>
        </div>

        <div class="step">
            <h3>步骤2：更新Firebase配置</h3>
            <p>如果当前配置有问题，请从Firebase控制台复制新的配置：</p>
            <textarea class="config-input" id="newConfig" placeholder="请粘贴完整的Firebase配置对象，例如：
{
  apiKey: &quot;your-api-key&quot;,
  authDomain: &quot;your-project.firebaseapp.com&quot;,
  projectId: &quot;your-project-id&quot;,
  storageBucket: &quot;your-project.firebasestorage.app&quot;,
  messagingSenderId: &quot;123456789&quot;,
  appId: &quot;1:123456789:web:abcdef123456&quot;
}" rows="8"></textarea>
            <button class="btn" onclick="validateConfig()">验证配置</button>
            <button class="btn btn-success" onclick="applyConfig()">应用配置</button>
        </div>

        <div class="step">
            <h3>步骤3：生成修复代码</h3>
            <p>点击下面的按钮生成修复后的index.html代码：</p>
            <button class="btn" onclick="generateFixedCode()">生成修复代码</button>
            <button class="btn btn-danger" onclick="downloadFixedFile()">下载修复文件</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        let currentFirebaseConfig = null;
        let newFirebaseConfig = null;

        function showResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            results.appendChild(div);
            
            // 自动滚动到结果
            div.scrollIntoView({ behavior: 'smooth' });
        }

        function checkCurrentConfig() {
            // 模拟当前配置（实际应该从系统中读取）
            currentFirebaseConfig = {
                apiKey: "AIzaSyDAtk4_1580AfAQYh0aGeykavDYfnflbKc",
                authDomain: "zhlscglxt.firebaseapp.com",
                projectId: "zhlscglxt",
                storageBucket: "zhlscglxt.firebasestorage.app",
                messagingSenderId: "36495989654",
                appId: "1:36495989654:web:3ad7266c9832ff25569185"
            };

            document.getElementById('currentConfig').textContent = JSON.stringify(currentFirebaseConfig, null, 2);
            showResult('✅ 当前配置已加载', 'success');
        }

        async function testConnection() {
            if (!currentFirebaseConfig) {
                showResult('❌ 请先加载当前配置', 'error');
                return;
            }

            showResult('🔄 正在测试Firebase连接...', 'info');

            try {
                // 这里应该实际测试Firebase连接
                // 由于这是独立页面，我们模拟测试结果
                const testResult = await simulateFirebaseTest(currentFirebaseConfig);
                
                if (testResult.success) {
                    showResult('✅ Firebase连接测试成功！', 'success');
                } else {
                    showResult(`❌ Firebase连接失败: ${testResult.error}`, 'error');
                    
                    // 提供具体的解决建议
                    if (testResult.error.includes('api-key-not-valid')) {
                        showResult('💡 建议：API密钥无效，请重新生成API密钥', 'info');
                    } else if (testResult.error.includes('auth/domain-not-authorized')) {
                        showResult('💡 建议：域名未授权，请在Firebase控制台添加当前域名到授权域名列表', 'info');
                    }
                }
            } catch (error) {
                showResult(`❌ 测试过程中出现错误: ${error.message}`, 'error');
            }
        }

        function simulateFirebaseTest(config) {
            return new Promise((resolve) => {
                setTimeout(() => {
                    // 模拟API密钥验证失败
                    resolve({
                        success: false,
                        error: 'auth/api-key-not-valid - API密钥无效或已过期'
                    });
                }, 2000);
            });
        }

        function validateConfig() {
            const configText = document.getElementById('newConfig').value.trim();
            
            if (!configText) {
                showResult('❌ 请输入Firebase配置', 'error');
                return;
            }

            try {
                // 尝试解析配置
                let config;
                if (configText.startsWith('{')) {
                    config = JSON.parse(configText);
                } else {
                    // 尝试解析JavaScript对象格式
                    config = eval('(' + configText + ')');
                }

                // 验证必需的字段
                const requiredFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
                const missingFields = requiredFields.filter(field => !config[field]);

                if (missingFields.length > 0) {
                    showResult(`❌ 配置缺少必需字段: ${missingFields.join(', ')}`, 'error');
                    return;
                }

                newFirebaseConfig = config;
                showResult('✅ 配置验证成功！', 'success');
                showResult(`项目ID: ${config.projectId}`, 'info');
                showResult(`应用ID: ${config.appId}`, 'info');

            } catch (error) {
                showResult(`❌ 配置格式错误: ${error.message}`, 'error');
            }
        }

        function applyConfig() {
            if (!newFirebaseConfig) {
                showResult('❌ 请先验证新配置', 'error');
                return;
            }

            currentFirebaseConfig = { ...newFirebaseConfig };
            document.getElementById('currentConfig').textContent = JSON.stringify(currentFirebaseConfig, null, 2);
            showResult('✅ 配置已应用，请测试连接', 'success');
        }

        function generateFixedCode() {
            if (!currentFirebaseConfig) {
                showResult('❌ 请先加载或设置Firebase配置', 'error');
                return;
            }

            const configCode = `const firebaseConfig = ${JSON.stringify(currentFirebaseConfig, null, 4)};`;
            
            showResult('✅ 修复代码已生成：', 'success');
            showResult(configCode, 'info');
            
            // 复制到剪贴板
            navigator.clipboard.writeText(configCode).then(() => {
                showResult('📋 配置代码已复制到剪贴板', 'success');
            });
        }

        function downloadFixedFile() {
            if (!currentFirebaseConfig) {
                showResult('❌ 请先加载或设置Firebase配置', 'error');
                return;
            }

            // 这里应该生成完整的修复后的index.html文件
            const fixedConfig = JSON.stringify(currentFirebaseConfig, null, 12);
            const instructions = `
// 请将以下配置替换到index.html文件中的firebaseConfig部分：

const firebaseConfig = ${fixedConfig};

// 替换位置大约在第1672-1679行
            `;

            const blob = new Blob([instructions], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'firebase-config-fix.txt';
            a.click();
            URL.revokeObjectURL(url);

            showResult('📁 修复说明文件已下载', 'success');
        }

        // 页面加载时自动检查当前配置
        window.addEventListener('load', () => {
            checkCurrentConfig();
        });
    </script>
</body>
</html>
