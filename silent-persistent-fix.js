// 静默且持久的发货量修复脚本

(function() {
    'use strict';
    
    console.log('🔧 静默持久修复脚本已加载');
    
    function silentPersistentFix() {
        if (!window.dataManager) {
            console.log('⏳ 等待DataManager加载...');
            setTimeout(silentPersistentFix, 1000);
            return;
        }
        
        const dm = window.dataManager;
        
        // 1. 计算正确的发货量（从客户统计）
        const customerStats = dm.calculateCustomerStats();
        let correctShippedMeters = 0;
        
        customerStats.forEach(customer => {
            if (customer.totalMeters > 0) {
                correctShippedMeters += customer.totalMeters;
            }
        });
        
        console.log(`📊 计算得出正确发货量: ${correctShippedMeters.toFixed(1)}米`);
        
        // 2. 检查当前显示是否正确
        const currentDisplay = document.querySelector('.metric-card.shipped .metric-value')?.textContent;
        const currentValue = parseFloat(currentDisplay) || 0;
        
        if (Math.abs(currentValue - correctShippedMeters) < 0.1) {
            console.log('✅ 发货量显示已正确，无需修复');
            return;
        }
        
        console.log(`🔧 需要修复: ${currentValue} -> ${correctShippedMeters.toFixed(1)}`);
        
        // 3. 静默更新仪表板数据（持久化到数据源）
        if (window.dashboard && window.dashboard.data) {
            window.dashboard.data.shippedMeters = correctShippedMeters;
            window.dashboard.data.unshippedMeters = Math.max(0, (window.dashboard.data.producedMeters || 0) - correctShippedMeters);
        }
        
        // 4. 静默更新DOM显示（无动画，无弹窗）
        const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
        if (shippedElement) {
            shippedElement.textContent = correctShippedMeters.toFixed(1);
            console.log('✅ 已发货量显示已更新');
        }
        
        const unshippedElement = document.querySelector('.metric-card.unshipped .metric-value');
        if (unshippedElement && window.dashboard && window.dashboard.data) {
            unshippedElement.textContent = window.dashboard.data.unshippedMeters.toFixed(1);
            console.log('✅ 未发货量显示已更新');
        }
        
        // 5. 保存到本地存储
        dm.saveToLocalStorage();
        
        // 6. 修改DataManager的计算方法，确保持久化
        if (dm.calculateMetrics) {
            const originalCalculateMetrics = dm.calculateMetrics;
            dm.calculateMetrics = function() {
                const result = originalCalculateMetrics.call(this);
                
                // 强制使用客户统计的发货量
                const customerStats = this.calculateCustomerStats();
                let totalShipped = 0;
                customerStats.forEach(customer => {
                    if (customer.totalMeters > 0) {
                        totalShipped += customer.totalMeters;
                    }
                });
                
                result.shippedMeters = totalShipped;
                result.unshippedMeters = Math.max(0, result.producedMeters - totalShipped);
                
                return result;
            };
            console.log('✅ DataManager计算方法已修正');
        }
        
        // 7. 修改Dashboard的更新方法
        if (window.dashboard && window.dashboard.updateMetrics) {
            const originalUpdateMetrics = window.dashboard.updateMetrics;
            window.dashboard.updateMetrics = function() {
                // 先调用原方法
                originalUpdateMetrics.call(this);
                
                // 然后强制使用正确的发货量
                if (this.data && window.dataManager) {
                    const customerStats = window.dataManager.calculateCustomerStats();
                    let totalShipped = 0;
                    customerStats.forEach(customer => {
                        if (customer.totalMeters > 0) {
                            totalShipped += customer.totalMeters;
                        }
                    });
                    
                    this.data.shippedMeters = totalShipped;
                    this.data.unshippedMeters = Math.max(0, this.data.producedMeters - totalShipped);
                    
                    // 更新DOM
                    const shippedEl = document.querySelector('.metric-card.shipped .metric-value');
                    const unshippedEl = document.querySelector('.metric-card.unshipped .metric-value');
                    
                    if (shippedEl) shippedEl.textContent = totalShipped.toFixed(1);
                    if (unshippedEl) unshippedEl.textContent = this.data.unshippedMeters.toFixed(1);
                }
            };
            console.log('✅ Dashboard更新方法已修正');
        }
        
        console.log('🎉 静默持久修复完成');
    }
    
    // 页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(silentPersistentFix, 2000);
        });
    } else {
        setTimeout(silentPersistentFix, 2000);
    }
    
    // 添加全局方法（用于手动修复）
    window.silentPersistentFix = silentPersistentFix;
    
})();
