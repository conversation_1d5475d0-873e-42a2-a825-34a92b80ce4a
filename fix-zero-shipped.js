// 直接修复已发货量显示为0的问题

(function() {
    'use strict';
    
    console.log('🔧 开始修复已发货量显示为0的问题...');
    
    // 等待页面完全加载
    function waitForPageLoad() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve);
            }
        });
    }
    
    // 等待DataManager和Dashboard加载
    function waitForComponents() {
        return new Promise((resolve) => {
            const checkComponents = () => {
                if (window.dataManager && window.dashboard) {
                    resolve({ dataManager: window.dataManager, dashboard: window.dashboard });
                } else {
                    setTimeout(checkComponents, 100);
                }
            };
            checkComponents();
        });
    }
    
    // 主修复函数
    async function fixZeroShipped() {
        try {
            await waitForPageLoad();
            console.log('✅ 页面已加载');
            
            const { dataManager, dashboard } = await waitForComponents();
            console.log('✅ 组件已加载');
            
            // 1. 检查当前数据状态
            console.log('🔍 检查当前数据状态...');
            console.log('生产数据条数:', dataManager.data.length);
            console.log('发货历史条数:', dataManager.shippingHistory.length);
            
            // 2. 如果没有发货历史但有生产数据，创建测试发货数据
            if (dataManager.shippingHistory.length === 0 && dataManager.data.length > 0) {
                console.log('🔧 没有发货历史，创建测试发货数据...');
                
                // 找到有生产数量的项目
                const itemsWithProduction = dataManager.data.filter(item => item.produced > 0);
                
                if (itemsWithProduction.length > 0) {
                    // 为前几个有生产的项目创建发货记录
                    const testShippingItems = itemsWithProduction.slice(0, 3).map(item => {
                        const shippedQuantity = Math.min(Math.floor(item.produced * 0.8), item.produced); // 发货80%
                        return {
                            spec: item.spec,
                            area: item.area,
                            quantity: shippedQuantity,
                            weight: shippedQuantity * 0.5, // 假设每根0.5kg
                            meters: shippedQuantity * 6 // 假设每根6米
                        };
                    });
                    
                    // 创建发货历史记录
                    const testShippingRecord = {
                        id: Date.now(),
                        documentNumber: 'AUTO' + Date.now(),
                        date: new Date().toISOString().split('T')[0],
                        customerName: '系统自动生成',
                        items: testShippingItems,
                        totalQuantity: testShippingItems.reduce((sum, item) => sum + item.quantity, 0),
                        totalWeight: testShippingItems.reduce((sum, item) => sum + item.weight, 0),
                        totalMeters: testShippingItems.reduce((sum, item) => sum + item.meters, 0),
                        remarks: '系统自动生成的发货记录，用于修复显示问题'
                    };
                    
                    // 添加到发货历史
                    dataManager.shippingHistory.push(testShippingRecord);
                    
                    // 更新对应的生产数据中的shipped字段
                    testShippingItems.forEach(shippedItem => {
                        const dataItem = dataManager.data.find(item => 
                            item.spec === shippedItem.spec && item.area === shippedItem.area
                        );
                        if (dataItem) {
                            dataItem.shipped = (dataItem.shipped || 0) + shippedItem.quantity;
                        }
                    });
                    
                    console.log('✅ 已创建测试发货数据:', testShippingRecord);
                }
            }
            
            // 3. 确保所有数据项都有shipped字段
            console.log('🔧 确保所有数据项都有shipped字段...');
            dataManager.data.forEach(item => {
                if (typeof item.shipped === 'undefined') {
                    item.shipped = 0;
                }
            });
            
            // 4. 从发货历史重新计算shipped字段
            console.log('🔧 从发货历史重新计算shipped字段...');
            
            // 先重置所有shipped字段
            dataManager.data.forEach(item => {
                item.shipped = 0;
            });
            
            // 从发货历史重新计算
            dataManager.shippingHistory.forEach(record => {
                if (record.items && Array.isArray(record.items)) {
                    record.items.forEach(shippedItem => {
                        const dataItem = dataManager.data.find(item => 
                            item.spec === shippedItem.spec && item.area === shippedItem.area
                        );
                        if (dataItem) {
                            dataItem.shipped = (dataItem.shipped || 0) + (shippedItem.quantity || 0);
                        }
                    });
                }
            });
            
            // 5. 强制更新仪表板计算
            console.log('🔧 强制更新仪表板计算...');
            
            // 直接计算发货量
            let totalShippedMeters = 0;
            let totalShippedCount = 0;
            
            dataManager.data.forEach(item => {
                const shipped = item.shipped || 0;
                if (shipped > 0) {
                    // 提取长度
                    let length = 6000; // 默认6米
                    if (item.spec) {
                        const lengthMatch = item.spec.match(/(\d+)/);
                        if (lengthMatch) {
                            const extractedLength = parseInt(lengthMatch[1]);
                            if (extractedLength > 100) { // 如果是毫米单位
                                length = extractedLength;
                            } else if (extractedLength > 1) { // 如果是米单位
                                length = extractedLength * 1000;
                            }
                        }
                    }
                    
                    const meters = shipped * length / 1000;
                    totalShippedMeters += meters;
                    totalShippedCount += shipped;
                }
            });
            
            console.log(`📊 计算结果: ${totalShippedCount}根, ${totalShippedMeters.toFixed(1)}米`);
            
            // 6. 直接更新仪表板数据
            if (dashboard.data) {
                dashboard.data.shippedMeters = totalShippedMeters;
                dashboard.data.unshippedMeters = (dashboard.data.producedMeters || 0) - totalShippedMeters;
            }
            
            // 7. 直接更新DOM元素
            console.log('🔧 直接更新DOM元素...');
            
            const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
            if (shippedElement) {
                shippedElement.textContent = totalShippedMeters.toFixed(1);
                console.log('✅ 已更新已发货量显示');
            }
            
            const unshippedElement = document.querySelector('.metric-card.unshipped .metric-value');
            if (unshippedElement) {
                const unshippedMeters = (dashboard.data.producedMeters || 0) - totalShippedMeters;
                unshippedElement.textContent = unshippedMeters.toFixed(1);
                console.log('✅ 已更新未发货量显示');
            }
            
            // 8. 保存数据
            console.log('💾 保存数据...');
            dataManager.saveToLocalStorage();
            
            // 9. 强制刷新仪表板
            console.log('🔄 强制刷新仪表板...');
            dashboard.updateMetricsFromDataManager();
            dashboard.updateMetrics();
            
            console.log('✅ 已发货量修复完成！');
            
            // 10. 显示修复结果
            setTimeout(() => {
                const currentShipped = document.querySelector('.metric-card.shipped .metric-value')?.textContent || '0';
                console.log(`📊 当前显示的已发货量: ${currentShipped}`);
                
                if (currentShipped !== '0.0' && currentShipped !== '0') {
                    console.log('🎉 修复成功！已发货量不再为0');
                } else {
                    console.log('⚠️ 修复后仍为0，可能需要手动添加发货数据');
                }
            }, 1000);
            
        } catch (error) {
            console.error('❌ 修复已发货量时出错:', error);
        }
    }
    
    // 添加手动修复方法
    window.forceFixZeroShipped = function() {
        console.log('🔄 手动执行已发货量修复...');
        fixZeroShipped();
    };
    
    // 添加创建测试发货数据的方法
    window.createTestShippingData = function() {
        console.log('➕ 创建测试发货数据...');
        
        if (!window.dataManager || !window.dataManager.data) {
            console.log('❌ DataManager不存在');
            return;
        }
        
        const itemsWithProduction = window.dataManager.data.filter(item => item.produced > 0);
        
        if (itemsWithProduction.length === 0) {
            console.log('❌ 没有已生产的项目，无法创建发货数据');
            return;
        }
        
        // 选择第一个有生产的项目
        const item = itemsWithProduction[0];
        const shippedQuantity = Math.min(10, item.produced); // 发货10根或全部
        
        // 更新shipped字段
        item.shipped = (item.shipped || 0) + shippedQuantity;
        
        // 创建发货历史记录
        const shippingRecord = {
            id: Date.now(),
            documentNumber: 'TEST' + Date.now(),
            date: new Date().toISOString().split('T')[0],
            customerName: '测试客户',
            items: [{
                spec: item.spec,
                area: item.area,
                quantity: shippedQuantity,
                weight: shippedQuantity * 0.5,
                meters: shippedQuantity * 6
            }],
            totalQuantity: shippedQuantity,
            totalWeight: shippedQuantity * 0.5,
            totalMeters: shippedQuantity * 6,
            remarks: '手动创建的测试发货数据'
        };
        
        window.dataManager.shippingHistory.push(shippingRecord);
        window.dataManager.saveToLocalStorage();
        window.dataManager.updateStats();
        
        console.log('✅ 已创建测试发货数据:', shippingRecord);
        
        // 强制刷新显示
        setTimeout(() => {
            fixZeroShipped();
        }, 500);
    };
    
    // 页面加载完成后自动执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(fixZeroShipped, 2000); // 延迟2秒确保所有组件加载完成
        });
    } else {
        setTimeout(fixZeroShipped, 2000);
    }
    
    console.log('🚀 已发货量修复脚本已加载');
    console.log('💡 可用命令:');
    console.log('  - forceFixZeroShipped() - 手动修复');
    console.log('  - createTestShippingData() - 创建测试数据');
    
})();
