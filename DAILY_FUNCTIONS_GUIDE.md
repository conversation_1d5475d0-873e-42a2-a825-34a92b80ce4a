# 日常使用功能指南

## 🎯 新增功能概览

系统现已支持完整的生产数量管理和发货功能，满足日常使用需求：

### ✅ 核心功能
- **生产数量管理** - 新增、编辑、批量操作生产数据
- **发货管理系统** - 发货状态选择、物流跟踪
- **数据表格组件** - 可编辑表格，支持排序、筛选、分页
- **操作日志系统** - 完整的操作历史记录
- **数据持久化** - 本地存储和导入导出功能

## 📊 生产数量管理

### 新增生产数据
1. 点击 **"新增生产"** 按钮
2. 填写必填信息：
   - 规格型号（H100-6m、H100-8m等）
   - 工地区域（C1、C2、C3、E1、E3、D6、A14）
   - 计划数量
3. 可选填写：
   - 已生产数量
   - 生产状态
   - 交付日期
   - 备注信息
4. 点击 **"保存"** 完成添加

### 编辑生产数据
1. 在数据表格中点击 **"编辑"** 按钮
2. 修改需要更新的字段
3. 系统会自动验证数据合理性
4. 保存后自动记录操作日志

### 增加生产数量
- **单项操作**：编辑记录，直接修改已生产数量
- **批量操作**：选择多项记录，使用批量增加功能

## 🚚 发货管理

### 单项发货
1. 在数据表格中找到已完成生产的项目
2. 点击 **"发货"** 按钮
3. 填写发货信息：
   - 发货数量（不能超过可发货数量）
   - 发货日期
   - 运输公司
   - 运单号
   - 发货备注
4. 确认发货，系统自动更新状态

### 发货记录跟踪
- 每次发货都会生成详细记录
- 包含运输公司、运单号等物流信息
- 支持多次分批发货
- 自动计算剩余可发货数量

## 📋 数据表格功能

### 搜索和筛选
- **搜索框**：支持按规格型号、区域、备注搜索
- **状态筛选**：计划中、生产中、已完成、已发货
- **区域筛选**：按工地区域筛选数据

### 排序功能
- 点击表头可按该列排序
- 支持升序/降序切换
- 可按规格、区域、数量、状态、日期排序

### 分页浏览
- 每页显示10条记录
- 支持上一页/下一页导航
- 显示总记录数和当前页信息

### 批量操作
1. 勾选需要操作的记录
2. 点击 **"批量编辑"** 按钮
3. 选择操作类型：
   - **批量更新状态**：统一修改生产状态
   - **批量增加生产数量**：为多个项目增加产量
   - **批量发货**：同时处理多个发货
   - **批量删除**：删除多条记录

## 📝 操作日志

### 查看操作历史
1. 点击 **"操作日志"** 按钮
2. 查看所有操作记录，包括：
   - 新增、编辑、删除操作
   - 发货记录
   - 批量操作
   - 操作时间和用户

### 日志筛选
- **按操作类型筛选**：新增、更新、删除、发货、批量操作
- **按日期筛选**：查看特定日期的操作记录
- **导出日志**：将筛选后的日志导出为JSON文件

### 日志管理
- **清空日志**：删除所有历史记录
- **自动清理**：系统自动保留最近1000条记录
- **详细信息**：每条日志包含完整的操作详情

## 💾 数据管理

### 数据导出
1. 点击 **"导出数据"** 按钮
2. 系统生成包含所有生产数据的JSON文件
3. 文件包含导出时间戳和完整数据结构
4. 可用于备份或数据迁移

### 数据导入
1. 点击 **"导入数据"** 按钮
2. 选择之前导出的JSON文件
3. 系统验证数据格式
4. 确认后覆盖现有数据

### 本地存储
- 所有数据自动保存到浏览器本地存储
- 页面刷新后数据不会丢失
- 支持离线使用

## 🎮 操作流程示例

### 典型工作流程

#### 1. 新项目启动
```
新增生产数据 → 设置计划数量 → 选择生产状态为"计划中"
```

#### 2. 生产过程管理
```
编辑记录 → 更新已生产数量 → 状态改为"生产中"
```

#### 3. 生产完成
```
更新最终产量 → 状态改为"已完成" → 准备发货
```

#### 4. 发货处理
```
点击发货 → 填写物流信息 → 确认发货 → 状态自动更新为"已发货"
```

#### 5. 批量处理
```
选择多个项目 → 批量增加产量 → 批量更新状态 → 批量发货
```

## 📊 数据统计

### 实时更新
- 总需求量、已生产量、完成率自动计算
- 图表数据实时同步更新
- 进度环显示最新完成率

### 关键指标
- **总需求量**：所有项目计划数量总和
- **已生产量**：所有项目已完成生产总和
- **完成率**：已生产量占总需求量的百分比
- **剩余数量**：待生产数量
- **可发货数量**：已生产但未发货的数量

## 🔧 高级功能

### 数据验证
- 已生产数量不能超过计划数量
- 发货数量不能超过可发货数量
- 必填字段验证
- 数据类型验证

### 状态自动更新
- 发货完成后自动更新为"已发货"状态
- 批量操作后自动重新计算统计数据
- 实时同步主界面显示

### 用户体验优化
- 操作成功/失败提示
- 确认对话框防止误操作
- 加载状态指示
- 响应式设计适配各种屏幕

## 🚀 快速上手

### 第一次使用
1. 系统已预置示例数据
2. 可以直接体验各项功能
3. 建议先查看操作日志了解系统记录方式
4. 尝试新增一条测试数据

### 日常操作建议
1. **定期备份**：使用导出功能备份重要数据
2. **及时更新**：生产进度发生变化时及时更新系统
3. **查看日志**：定期检查操作日志确保数据准确性
4. **批量处理**：对于相似操作使用批量功能提高效率

## 📱 移动端使用

### 响应式支持
- 完美适配手机和平板设备
- 触摸友好的操作界面
- 自动调整布局和字体大小

### 移动端优化
- 简化的操作流程
- 大按钮设计便于触摸
- 垂直布局适合小屏幕

## 🔍 故障排除

### 常见问题
1. **数据不显示**：检查浏览器是否支持localStorage
2. **操作无响应**：刷新页面重新加载
3. **数据丢失**：检查是否误删或使用导入功能恢复

### 技术支持
- 所有操作都有详细的日志记录
- 可通过操作日志追踪问题
- 支持数据导出进行问题分析

---

**系统版本**：v2.0.0  
**更新日期**：2024年6月17日  
**功能状态**：生产就绪 ✅
