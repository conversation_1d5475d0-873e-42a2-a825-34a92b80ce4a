# 系统数据来源说明

## 📊 当前系统中的长度数据来源

### 🎯 **数据来源层级**

系统按以下优先级加载数据：

1. **本地存储数据**（优先级最高）
   - 位置：浏览器 localStorage
   - 键名：`productionData`
   - 内容：用户之前添加、编辑的所有生产数据

2. **示例数据**（备用数据）
   - 位置：`scripts/data-management.js` 文件中的 `loadSampleData()` 方法
   - 用途：仅在本地存储为空时使用
   - 目的：为新用户提供演示数据

### 📋 **当前示例数据详情**

系统预置了6条示例数据，展示不同的规格型号：

```javascript
[
    { id: 1, spec: 'H100-1000mm', area: 'C3', planned: 5000, produced: 3200 },
    { id: 2, spec: 'H100-1200mm', area: 'E3', planned: 3500, produced: 3500 },
    { id: 3, spec: 'H80-1000mm', area: 'D6', planned: 2800, produced: 1500 },
    { id: 4, spec: 'H80-1400mm', area: 'A14', planned: 4200, produced: 4200 },
    { id: 5, spec: 'H100-1600mm', area: 'C1', planned: 6000, produced: 2400 },
    { id: 6, spec: 'H80-800mm', area: 'E1', planned: 3200, produced: 2800 }
]
```

### 🔄 **数据加载流程**

```
系统启动
    ↓
检查本地存储
    ↓
有数据？ ——是——→ 加载本地数据
    ↓
   否
    ↓
加载示例数据 ——→ 保存到本地存储
    ↓
显示数据表格
```

## 🛠️ **数据管理机制**

### 💾 **本地存储**
- **自动保存**：每次新增、编辑、删除操作都会自动保存到本地存储
- **持久化**：数据在浏览器中持久保存，刷新页面不会丢失
- **隔离性**：不同浏览器、不同用户的数据相互独立

### 📤 **数据导出**
- **格式**：JSON格式
- **内容**：包含所有生产数据和导出时间戳
- **用途**：数据备份、迁移、分析

### 📥 **数据导入**
- **来源**：之前导出的JSON文件
- **效果**：完全替换当前数据
- **安全**：导入前会提示确认

## 🎮 **用户操作对数据的影响**

### ✅ **会保存到本地存储的操作**
- 新增生产数据
- 编辑现有数据
- 删除数据
- 批量操作（更新状态、增加产量、批量删除）
- 发货操作

### 📊 **不会影响本地存储的操作**
- 搜索和筛选（仅影响显示）
- 排序（仅影响显示顺序）
- 分页浏览（仅影响显示范围）

## 🔍 **如何查看当前数据来源**

### 方法1：检查浏览器开发者工具
1. 按F12打开开发者工具
2. 切换到"Application"或"存储"标签
3. 查看"Local Storage" → 当前域名
4. 查找键名为"productionData"的项目

### 方法2：查看操作日志
1. 点击系统中的"操作日志"按钮
2. 查看是否有历史操作记录
3. 如果有记录，说明使用的是本地存储数据
4. 如果没有记录，说明使用的是示例数据

### 方法3：数据导出验证
1. 点击"导出数据"按钮
2. 查看导出的JSON文件
3. 检查数据的ID、时间戳等信息

## 🚀 **数据初始化建议**

### 🆕 **新用户（首次使用）**
- 系统会自动加载示例数据
- 可以直接体验各项功能
- 建议先熟悉界面，然后清空示例数据开始实际使用

### 🔄 **现有用户（已有数据）**
- 系统会自动加载之前保存的数据
- 所有历史操作记录都会保留
- 可以继续在现有数据基础上操作

### 🧹 **清空数据重新开始**
如果想清空所有数据重新开始：
1. 方法1：在浏览器开发者工具中删除localStorage中的"productionData"
2. 方法2：使用批量删除功能删除所有记录
3. 方法3：清除浏览器数据（会同时清除其他网站数据）

## 📈 **数据统计来源**

### 主界面统计数据
- **总需求量**：所有记录的planned字段之和
- **已生产量**：所有记录的produced字段之和
- **完成率**：已生产量 ÷ 总需求量 × 100%
- **剩余数量**：总需求量 - 已生产量

### 图表数据
- **生产进度分布**：基于当前数据计算的完成/进行/待开始比例
- **规格型号分布**：模拟数据（用于演示）
- **工地区域分布**：模拟数据（用于演示）

## 🔧 **技术实现细节**

### 数据结构
```javascript
{
    id: 唯一标识符,
    spec: '规格型号（如H100-1000mm）',
    area: '工地区域',
    planned: 计划数量,
    produced: 已生产数量,
    status: '状态（planned/producing/completed/shipped）',
    deadline: '交付日期',
    remarks: '备注',
    shipped: 已发货数量,
    shippingRecords: [发货记录数组]
}
```

### 存储机制
- **存储位置**：`localStorage.productionData`
- **存储格式**：JSON字符串
- **更新时机**：每次数据变更后立即保存
- **容量限制**：通常5-10MB（浏览器限制）

## ⚠️ **注意事项**

### 数据安全
- 数据仅保存在本地浏览器中
- 清除浏览器数据会导致数据丢失
- 建议定期使用导出功能备份数据

### 浏览器兼容性
- 需要支持localStorage的现代浏览器
- 隐私模式可能限制数据保存
- 不同浏览器的数据不会同步

### 数据迁移
- 更换浏览器需要手动导出/导入数据
- 更换设备需要手动迁移数据
- 建议保存导出的JSON文件作为备份

---

**总结**：系统中的长度数据主要来自用户的实际操作和输入，示例数据仅用于演示目的。系统会智能地优先使用用户的真实数据，确保数据的连续性和准确性。
