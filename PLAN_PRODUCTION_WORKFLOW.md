# 计划与生产管理工作流程

## 🔄 数据关联修复

### ✅ **问题已解决**
- **主界面统计数据**：现在完全基于实际输入的数据计算
- **实时更新**：任何数据变更都会立即反映在主界面
- **数据一致性**：确保所有显示的数据都来自同一数据源

### 📊 **统计数据来源**
- **总需求量**：所有计划记录的planned字段之和
- **已生产量**：所有记录的produced字段之和
- **未生产量**：总需求量 - 已生产量
- **完成率**：已生产量 ÷ 总需求量 × 100%

## 🆕 新增计划功能

### 🎯 **业务流程设计**

```
新增计划 → 录入生产进度 → 管理发货
    ↓           ↓              ↓
  计划阶段    生产阶段        发货阶段
```

### 📋 **功能对比**

| 功能 | 新增计划 | 新增生产 |
|------|----------|----------|
| 用途 | 录入生产需求计划 | 更新生产进度 |
| 初始状态 | 计划中 | 可选择状态 |
| 已生产数量 | 固定为0 | 可输入实际数量 |
| 重复检查 | 检查并可合并 | 直接添加 |
| 使用场景 | 项目启动时 | 生产过程中 |

## 🎮 **操作流程**

### 1. **新增计划**（项目启动）

**步骤：**
1. 点击绿色"新增计划"按钮
2. 选择型号（H100/H80）
3. 选择长度（800-11800mm）
4. 选择工地区域
5. 输入计划数量
6. 设置交付日期（可选）
7. 添加计划备注（可选）
8. 保存计划

**特殊功能：**
- **重复检查**：如果已存在相同规格+区域的计划，系统会询问是否合并数量
- **自动状态**：新计划自动设为"计划中"状态
- **零生产量**：新计划的已生产数量自动设为0

### 2. **更新生产进度**（生产过程中）

**方式一：编辑现有计划**
1. 在数据表格中点击"编辑"按钮
2. 修改已生产数量
3. 更新生产状态
4. 保存修改

**方式二：新增生产记录**
1. 点击蓝色"新增生产"按钮
2. 输入具体的生产数据
3. 可以设置不同的状态和进度

**方式三：批量操作**
1. 选择多个项目
2. 使用"批量增加生产数量"
3. 统一更新多个项目的进度

### 3. **发货管理**（完成后）

1. 生产完成后，点击"发货"按钮
2. 填写发货信息和物流详情
3. 系统自动更新发货状态

## 📊 **数据流转示例**

### 完整项目周期

```
第1天：新增计划
├─ H100-2000mm (C2区域)
├─ 计划数量：100根
├─ 已生产：0根
└─ 状态：计划中

第5天：开始生产
├─ 编辑记录
├─ 已生产：30根
└─ 状态：生产中

第10天：继续生产
├─ 批量增加数量
├─ 已生产：70根
└─ 状态：生产中

第15天：完成生产
├─ 编辑记录
├─ 已生产：100根
└─ 状态：已完成

第16天：安排发货
├─ 点击发货
├─ 发货：100根
└─ 状态：已发货
```

## 🎯 **使用建议**

### 📅 **项目启动阶段**
1. **使用"新增计划"**录入所有生产需求
2. **批量录入**相同规格不同区域的计划
3. **设置交付日期**便于进度管理

### 🏭 **生产执行阶段**
1. **定期更新**已生产数量
2. **及时调整**生产状态
3. **使用批量操作**提高效率

### 📦 **发货管理阶段**
1. **完成生产**后及时更新状态
2. **安排发货**并记录物流信息
3. **跟踪发货**进度和状态

## 🔍 **数据验证**

### 自动检查
- **规格格式**：确保符合H100/H80-长度mm格式
- **数量合理性**：已生产不能超过计划数量
- **重复计划**：相同规格+区域的计划可选择合并

### 业务规则
- **计划优先**：建议先建立计划，再更新生产进度
- **状态流转**：计划中 → 生产中 → 已完成 → 已发货
- **数据一致性**：所有统计数据实时同步

## 📈 **统计数据说明**

### 主界面指标
- **总需求量**：所有计划的总和，实时计算
- **已生产量**：所有项目的生产进度总和
- **完成率**：基于实际数据的完成百分比
- **生产效率**：可根据实际情况手动调整

### 图表数据
- **进度分布**：基于实际状态统计
- **规格分布**：基于实际计划数据
- **区域分布**：基于实际项目分布

## 🚀 **立即体验**

### 测试流程
1. **清空现有数据**：点击"清空所有数据"
2. **新增第一个计划**：使用"新增计划"功能
3. **观察数据变化**：查看主界面统计数据更新
4. **更新生产进度**：编辑计划或新增生产数据
5. **验证数据同步**：确认所有数据实时更新

### 推荐操作
1. 先使用"新增计划"建立基础数据
2. 然后通过编辑或批量操作更新进度
3. 最后使用发货功能完成整个流程

---

**重要改进**：
✅ 主界面数据现在完全基于实际输入  
✅ 新增了专门的计划管理功能  
✅ 建立了清晰的业务流程  
✅ 实现了数据实时同步更新  

**下一步**：建议先清空现有数据，然后使用新的"新增计划"功能重新建立准确的生产计划！
