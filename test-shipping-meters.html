<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发货管理重量改为合计米数测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            color: #1e40af;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            color: #059669;
            margin-top: 0;
        }
        
        .change-description {
            background: #ecfdf5;
            border: 1px solid #a7f3d0;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .change-title {
            color: #059669;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .before, .after {
            padding: 15px;
            border-radius: 8px;
        }
        
        .before {
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .after {
            background: #ecfdf5;
            border: 1px solid #a7f3d0;
        }
        
        .before h4 {
            color: #dc2626;
            margin-top: 0;
        }
        
        .after h4 {
            color: #059669;
            margin-top: 0;
        }
        
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .highlight {
            background: #fbbf24;
            color: #1f2937;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            border: 1px solid #e5e7eb;
        }
        
        .demo-table th,
        .demo-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .demo-table th {
            background: #1e3a8a;
            color: white;
            font-weight: bold;
        }
        
        .demo-table .number-cell {
            text-align: right;
        }
        
        .demo-table tr:last-child td {
            border-bottom: none;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #e5e7eb;
            position: relative;
            padding-left: 30px;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: '✅';
            position: absolute;
            left: 0;
            top: 10px;
            font-size: 16px;
        }
        
        .calculation-example {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .calculation-title {
            color: #0369a1;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .formula {
            font-family: monospace;
            background: #e0f2fe;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 8px 0;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 发货管理重量改为合计米数</h1>
        
        <div class="test-section">
            <h3>📋 修改需求</h3>
            <div class="change-description">
                <div class="change-title">用户需求</div>
                <p>在发货管理界面中，将"重量(kg)"列改为"合计米数"列，以便更直观地显示发货的总米数。</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🔄 修改对比</h3>
            <div class="before-after">
                <div class="before">
                    <h4>修改前</h4>
                    <table class="demo-table">
                        <thead>
                            <tr>
                                <th>规格型号</th>
                                <th>可发货数量</th>
                                <th>发货数量</th>
                                <th>重量(kg)</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>H100-3200mm</td>
                                <td>176</td>
                                <td>176</td>
                                <td class="number-cell">1408.0</td>
                                <td>全部</td>
                            </tr>
                            <tr>
                                <td>H80-4000mm</td>
                                <td>124</td>
                                <td>124</td>
                                <td class="number-cell">992.0</td>
                                <td>全部</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="after">
                    <h4>修改后</h4>
                    <table class="demo-table">
                        <thead>
                            <tr>
                                <th>规格型号</th>
                                <th>可发货数量</th>
                                <th>发货数量</th>
                                <th>合计米数</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>H100-3200mm</td>
                                <td>176</td>
                                <td>176</td>
                                <td class="number-cell">563.2</td>
                                <td>全部</td>
                            </tr>
                            <tr>
                                <td>H80-4000mm</td>
                                <td>124</td>
                                <td>124</td>
                                <td class="number-cell">496.0</td>
                                <td>全部</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧮 计算示例</h3>
            <div class="calculation-example">
                <div class="calculation-title">合计米数计算方法</div>
                <p>合计米数 = 发货数量 × 单根长度（米）</p>
                
                <p><strong>示例1：H100-3200mm</strong></p>
                <div class="formula">合计米数 = 176根 × 3.2米 = 563.2米</div>
                
                <p><strong>示例2：H80-4000mm</strong></p>
                <div class="formula">合计米数 = 124根 × 4.0米 = 496.0米</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 技术实现</h3>
            
            <h4>1. HTML表头修改</h4>
            <div class="code-block">
<span class="highlight">// 修改前</span>
&lt;th&gt;重量(kg)&lt;/th&gt;

<span class="highlight">// 修改后</span>
&lt;th&gt;合计米数&lt;/th&gt;
            </div>

            <h4>2. JavaScript计算方法</h4>
            <div class="code-block">
<span class="highlight">// 新增计算米数的方法</span>
calculateMeters(spec, quantity) {
    const match = spec.match(/^(H\d+)-(\d+)mm$/);
    if (!match) return 0;
    
    const [, type, length] = match;
    const lengthM = parseInt(length) / 1000;
    
    return quantity * lengthM;
}
            </div>

            <h4>3. 表格数据显示修改</h4>
            <div class="code-block">
<span class="highlight">// 修改前</span>
&lt;td class="weight-cell"&gt;${this.calculateWeight(item.spec, available).toFixed(1)}&lt;/td&gt;

<span class="highlight">// 修改后</span>
&lt;td class="meters-cell"&gt;${this.calculateMeters(item.spec, available).toFixed(1)}&lt;/td&gt;
            </div>

            <h4>4. 动态更新逻辑修改</h4>
            <div class="code-block">
<span class="highlight">// 修改前</span>
const weightCell = row.querySelector('.weight-cell');
if (weightCell && item) {
    weightCell.textContent = this.calculateWeight(item.spec, quantity).toFixed(1);
}

<span class="highlight">// 修改后</span>
const metersCell = row.querySelector('.meters-cell');
if (metersCell && item) {
    metersCell.textContent = this.calculateMeters(item.spec, quantity).toFixed(1);
}
            </div>
        </div>

        <div class="test-section">
            <h3>📊 发货单格式更新</h3>
            <p>发货单中的表格格式也已相应更新：</p>
            
            <table class="demo-table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>规格型号</th>
                        <th>发货数量(根)</th>
                        <th>长度(米)</th>
                        <th>合计米数</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>H100-3200mm</td>
                        <td class="number-cell">176</td>
                        <td class="number-cell">3.2</td>
                        <td class="number-cell">563.2</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>H80-4000mm</td>
                        <td class="number-cell">124</td>
                        <td class="number-cell">4.0</td>
                        <td class="number-cell">496.0</td>
                    </tr>
                    <tr style="background: #f8fafc; font-weight: bold;">
                        <td colspan="2">合计</td>
                        <td class="number-cell">300</td>
                        <td class="number-cell">-</td>
                        <td class="number-cell">1059.2</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>✅ 修改完成的功能</h3>
            <ul class="feature-list">
                <li>批量发货表格中的"重量(kg)"列已改为"合计米数"列</li>
                <li>新增了calculateMeters()方法用于计算总米数</li>
                <li>表格数据显示正确计算并显示合计米数</li>
                <li>动态更新逻辑已修改为更新米数而非重量</li>
                <li>发货单模板中的表头已更新</li>
                <li>发货单中正确区分单根长度和合计米数</li>
                <li>保持了原有的重量计算功能（用于其他用途）</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 测试步骤</h3>
            <p>请按以下步骤测试修改效果：</p>
            
            <ol>
                <li><strong>打开发货管理</strong>：点击任意生产记录的"发货"按钮</li>
                <li><strong>切换到批量发货</strong>：点击"批量发货"按钮</li>
                <li><strong>选择区域和客户</strong>：填写必要的发货信息</li>
                <li><strong>加载可发货项目</strong>：点击"加载可发货项目"按钮</li>
                <li><strong>检查表头</strong>：确认表头显示为"合计米数"而非"重量(kg)"</li>
                <li><strong>检查数据</strong>：确认显示的数值为米数而非重量</li>
                <li><strong>选择发货项目</strong>：勾选要发货的项目</li>
                <li><strong>调整发货数量</strong>：修改发货数量，观察合计米数的变化</li>
                <li><strong>预览发货单</strong>：点击"预览发货单"检查发货单格式</li>
                <li><strong>确认发货</strong>：完成发货流程测试</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>📈 预期效果</h3>
            <div class="change-description">
                <div class="change-title">修改后的预期表现</div>
                <ul>
                    <li>✅ 表头显示"合计米数"而非"重量(kg)"</li>
                    <li>✅ 数据显示为米数（如：563.2）而非重量（如：1408.0）</li>
                    <li>✅ 发货数量变化时，合计米数相应更新</li>
                    <li>✅ 发货单中正确显示单根长度和合计米数</li>
                    <li>✅ 汇总统计中显示正确的总米数</li>
                    <li>✅ 所有相关功能正常工作</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 注意事项</h3>
            <ul>
                <li>原有的重量计算功能仍然保留，以备其他功能使用</li>
                <li>合计米数的计算精度保持一位小数</li>
                <li>发货单中区分了单根长度和合计米数两个概念</li>
                <li>所有相关的CSS类名已从weight-cell改为meters-cell</li>
                <li>数据收集和处理逻辑已相应更新</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('发货管理重量改为合计米数测试页面已加载');
        console.log('请在主系统中测试发货管理功能');
        
        // 模拟计算示例
        function calculateMetersExample(quantity, lengthMm) {
            const lengthM = lengthMm / 1000;
            return quantity * lengthM;
        }
        
        console.log('计算示例:');
        console.log('H100-3200mm, 176根:', calculateMetersExample(176, 3200), '米');
        console.log('H80-4000mm, 124根:', calculateMetersExample(124, 4000), '米');
    </script>
</body>
</html>
