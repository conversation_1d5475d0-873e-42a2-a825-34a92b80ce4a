// 清理3831脚本影响并恢复正确的3675米
// 彻底解决已发货量显示问题

(function() {
    'use strict';
    
    console.log('🧹 开始清理3831脚本影响并恢复正确数据...');
    
    function cleanAndRestore() {
        if (!window.dataManager || !window.dashboard) {
            console.log('⏳ 等待组件加载...');
            setTimeout(cleanAndRestore, 500);
            return;
        }
        
        const dm = window.dataManager;
        const dashboard = window.dashboard;
        
        console.log('='.repeat(60));
        console.log('🧹 清理和恢复操作开始');
        console.log('='.repeat(60));
        
        // 1. 获取正确的客户统计数据
        console.log('📊 1. 获取正确的客户统计数据...');
        const customerStats = dm.calculateCustomerStats();
        const correctValue = customerStats.reduce((sum, customer) => {
            return sum + (customer.totalMeters || 0);
        }, 0);
        
        console.log(`✅ 正确的已发货量: ${correctValue.toFixed(1)}米`);
        
        // 2. 清理所有可能的3831相关监听器和定时器
        console.log('🧹 2. 清理3831相关的监听器和定时器...');
        
        // 清理所有定时器（这会停止force-shipped-3831.js的定期检查）
        let timerId = setTimeout(function(){}, 0);
        for (let i = 1; i <= timerId; i++) {
            clearTimeout(i);
            clearInterval(i);
        }
        console.log(`✅ 已清理 ${timerId} 个定时器`);
        
        // 3. 恢复dashboard的原始方法
        console.log('🔧 3. 恢复dashboard的原始方法...');
        
        // 重新定义updateMetricsFromDataManager方法
        dashboard.updateMetricsFromDataManager = function() {
            console.log('🔄 执行恢复的updateMetricsFromDataManager...');
            
            if (!window.dataManager) {
                console.error('❌ window.dataManager 不存在！');
                return;
            }
            
            const data = window.dataManager.data || [];
            if (data.length === 0) {
                console.warn('⚠️ 数据为空');
                return;
            }
            
            // 使用客户统计作为唯一数据源
            const customerStats = window.dataManager.calculateCustomerStats();
            const shippedMeters = customerStats.reduce((sum, customer) => {
                return sum + (customer.totalMeters || 0);
            }, 0);
            
            // 计算其他数据
            let totalDemandMeters = 0;
            let producedMeters = 0;
            
            data.forEach(item => {
                const length = this.extractLengthFromSpec ? this.extractLengthFromSpec(item.spec) : 6000;
                const planned = item.planned || 0;
                const produced = item.produced || 0;
                
                if (planned > 0) {
                    totalDemandMeters += planned * length / 1000;
                }
                if (produced > 0) {
                    producedMeters += produced * length / 1000;
                }
            });
            
            // 更新数据对象
            this.data = {
                totalDemandMeters: totalDemandMeters,
                producedMeters: producedMeters,
                pendingMeters: totalDemandMeters - producedMeters,
                shippedMeters: shippedMeters,
                unshippedMeters: Math.max(0, producedMeters - shippedMeters),
                completionRate: totalDemandMeters > 0 ? ((producedMeters / totalDemandMeters) * 100).toFixed(1) : 0,
                materialTons: this.calculateActualMaterialPurchased ? this.calculateActualMaterialPurchased() : 0,
                inventoryStatus: this.calculateInventoryStatus ? this.calculateInventoryStatus(Math.max(0, producedMeters - shippedMeters)) : { status: '正常', color: '#10b981' },
                efficiency: 0
            };
            
            console.log(`✅ 数据更新完成: 已发货量 ${shippedMeters.toFixed(1)}米`);
        };
        
        // 重新定义updateMetrics方法
        dashboard.updateMetrics = function() {
            console.log('🎨 执行恢复的updateMetrics...');
            
            if (!this.data) {
                console.warn('⚠️ 数据对象不存在');
                return;
            }
            
            // 更新所有DOM元素
            const updates = [
                { selector: '.metric-card.total .metric-value', value: this.data.totalDemandMeters || 0, decimals: 1 },
                { selector: '.metric-card.produced .metric-value', value: this.data.producedMeters || 0, decimals: 1 },
                { selector: '.metric-card.pending .metric-value', value: this.data.pendingMeters || 0, decimals: 1 },
                { selector: '.metric-card.shipped .metric-value', value: this.data.shippedMeters || 0, decimals: 1 },
                { selector: '.metric-card.unshipped .metric-value', value: this.data.unshippedMeters || 0, decimals: 1 },
                { selector: '.metric-card.efficiency .metric-value', value: this.data.completionRate || 0, decimals: 1, suffix: '%' }
            ];
            
            updates.forEach(update => {
                const element = document.querySelector(update.selector);
                if (element) {
                    const displayValue = update.decimals ? 
                        update.value.toFixed(update.decimals) : 
                        Math.round(update.value);
                    element.textContent = displayValue + (update.suffix || '');
                }
            });
            
            console.log(`✅ DOM更新完成: 已发货量显示 ${(this.data.shippedMeters || 0).toFixed(1)}米`);
        };
        
        console.log('✅ 已恢复dashboard方法');
        
        // 4. 立即执行数据更新
        console.log('🔄 4. 立即执行数据更新...');
        dashboard.updateMetricsFromDataManager();
        dashboard.updateMetrics();
        
        // 5. 强制更新DOM显示
        console.log('🎨 5. 强制更新DOM显示...');
        const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
        if (shippedElement) {
            const oldValue = shippedElement.textContent;
            shippedElement.textContent = correctValue.toFixed(1);
            
            console.log(`✅ DOM强制更新: ${oldValue} -> ${correctValue.toFixed(1)}米`);
            
            // 添加成功的视觉反馈
            shippedElement.style.cssText = `
                background: linear-gradient(45deg, #10b981, #059669) !important;
                color: white !important;
                font-weight: bold !important;
                padding: 8px 12px !important;
                border-radius: 8px !important;
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.6) !important;
                border: 2px solid #34d399 !important;
                transform: scale(1.05) !important;
                transition: all 0.5s ease !important;
            `;
            
            // 3秒后恢复正常样式
            setTimeout(() => {
                shippedElement.style.cssText = '';
            }, 3000);
        }
        
        // 6. 创建保护机制，防止再次被3831脚本影响
        console.log('🛡️ 6. 创建保护机制...');
        
        let isProtecting = false;
        const protectionObserver = new MutationObserver((mutations) => {
            if (isProtecting) return;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    const shippedEl = document.querySelector('.metric-card.shipped .metric-value');
                    if (shippedEl) {
                        const currentText = shippedEl.textContent.trim();
                        const currentValue = parseFloat(currentText);
                        
                        // 如果显示的是3831，立即修正为正确值
                        if (currentValue === 3831 || currentText.includes('3831')) {
                            isProtecting = true;
                            shippedEl.textContent = correctValue.toFixed(1);
                            console.log(`🛡️ 保护机制触发: 阻止3831显示，恢复为${correctValue.toFixed(1)}米`);
                            
                            setTimeout(() => { isProtecting = false; }, 100);
                        }
                    }
                }
            });
        });
        
        protectionObserver.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });
        
        console.log('✅ 保护机制已启动');
        
        // 7. 保存正确的数据
        console.log('💾 7. 保存正确的数据...');
        dm.saveToLocalStorage();
        
        // 8. 最终验证
        setTimeout(() => {
            console.log('🔍 8. 最终验证...');
            
            const finalShippedDisplay = document.querySelector('.metric-card.shipped .metric-value')?.textContent || '0';
            const finalDashboardValue = dashboard.data?.shippedMeters || 0;
            
            console.log('验证结果:');
            console.log(`  DOM显示: ${finalShippedDisplay}`);
            console.log(`  Dashboard数据: ${finalDashboardValue.toFixed(1)}米`);
            console.log(`  期望值: ${correctValue.toFixed(1)}米`);
            
            const domCorrect = Math.abs(parseFloat(finalShippedDisplay) - correctValue) < 0.1;
            const dataCorrect = Math.abs(finalDashboardValue - correctValue) < 0.1;
            
            if (domCorrect && dataCorrect) {
                console.log('🎉 清理和恢复成功！');
                console.log(`✅ 已发货量已正确显示为: ${correctValue.toFixed(1)}米`);
                
                if (dm.showNotification) {
                    dm.showNotification(
                        `🎉 修复成功！已发货量: ${correctValue.toFixed(1)}米`, 
                        'success'
                    );
                }
                
                // 10秒后停止保护机制
                setTimeout(() => {
                    protectionObserver.disconnect();
                    console.log('🛡️ 保护机制已停止');
                }, 10000);
                
            } else {
                console.log('⚠️ 验证失败，可能需要刷新页面');
                
                if (dm.showNotification) {
                    dm.showNotification(
                        '⚠️ 部分修复完成，建议刷新页面', 
                        'warning'
                    );
                }
            }
        }, 2000);
        
        console.log('='.repeat(60));
        console.log('🧹 清理和恢复操作完成');
        console.log('='.repeat(60));
    }
    
    // 立即执行
    cleanAndRestore();
    
    console.log('✅ 清理和恢复脚本已启动');
    
})();
