<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制台助手</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-info { background: #06b6d4; color: white; }
        .btn:hover { opacity: 0.9; transform: translateY(-1px); }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .status-card.healthy { border-color: #28a745; background: #d4edda; }
        .status-card.warning { border-color: #ffc107; background: #fff3cd; }
        .status-card.error { border-color: #dc3545; background: #f8d7da; }
        .status-value {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .status-label {
            font-size: 12px;
            color: #6c757d;
        }
        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 5px 0;
            padding: 2px 0;
        }
        .log-success { color: #4ade80; }
        .log-warning { color: #fbbf24; }
        .log-error { color: #f87171; }
        .log-info { color: #60a5fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ 控制台助手</h1>
        <p>系统状态检查和错误修复工具</p>

        <div style="margin: 20px 0;">
            <button class="btn btn-success" onclick="performHealthCheck()">🏥 健康检查</button>
            <button class="btn btn-info" onclick="clearConsole()">🧹 清理控制台</button>
            <button class="btn btn-warning" onclick="recoverFromErrors()">🔄 错误恢复</button>
            <button class="btn btn-primary" onclick="refreshSystem()">🔄 刷新系统</button>
        </div>

        <div style="margin: 20px 0;">
            <button class="btn btn-info" onclick="checkFirebaseStatus()">☁️ Firebase状态</button>
            <button class="btn btn-success" onclick="checkDataIntegrity()">📊 数据完整性</button>
            <button class="btn btn-warning" onclick="fixShippedQuantity()">📦 修复发货量</button>
            <button class="btn btn-danger" onclick="emergencyReset()">🚨 紧急重置</button>
        </div>

        <div class="status-grid" id="statusGrid">
            <!-- 状态卡片将在这里动态生成 -->
        </div>

        <div class="console-output" id="consoleOutput">
            <div class="log-entry log-info">[系统] 控制台助手已加载</div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            output.appendChild(logEntry);
            output.scrollTop = output.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateStatusGrid(healthData) {
            const statusGrid = document.getElementById('statusGrid');
            
            if (!healthData) {
                statusGrid.innerHTML = `
                    <div class="status-card error">
                        <div class="status-value">N/A</div>
                        <div class="status-label">无法获取状态</div>
                    </div>
                `;
                return;
            }
            
            const getCardClass = (value, threshold = 0) => {
                if (typeof value === 'boolean') {
                    return value ? 'healthy' : 'error';
                }
                if (typeof value === 'number') {
                    return value > threshold ? 'healthy' : 'warning';
                }
                return 'warning';
            };
            
            statusGrid.innerHTML = `
                <div class="status-card ${getCardClass(healthData.dataManager)}">
                    <div class="status-value">${healthData.dataManager ? '✅' : '❌'}</div>
                    <div class="status-label">DataManager</div>
                </div>
                <div class="status-card ${getCardClass(healthData.dashboard)}">
                    <div class="status-value">${healthData.dashboard ? '✅' : '❌'}</div>
                    <div class="status-label">Dashboard</div>
                </div>
                <div class="status-card ${getCardClass(healthData.firebase)}">
                    <div class="status-value">${healthData.firebase ? '☁️' : '📱'}</div>
                    <div class="status-label">${healthData.firebase ? 'Firebase' : '本地模式'}</div>
                </div>
                <div class="status-card ${getCardClass(healthData.dataCount)}">
                    <div class="status-value">${healthData.dataCount}</div>
                    <div class="status-label">生产数据</div>
                </div>
                <div class="status-card ${getCardClass(healthData.shippingCount)}">
                    <div class="status-value">${healthData.shippingCount}</div>
                    <div class="status-label">发货记录</div>
                </div>
                <div class="status-card ${getCardClass(healthData.materialCount)}">
                    <div class="status-value">${healthData.materialCount}</div>
                    <div class="status-label">原材料记录</div>
                </div>
            `;
        }

        function performHealthCheck() {
            log('开始系统健康检查...', 'info');
            
            try {
                if (window.parent && typeof window.parent.healthCheck === 'function') {
                    const health = window.parent.healthCheck();
                    
                    if (health) {
                        log(`健康检查完成: ${health.errors.length === 0 ? '系统正常' : '发现问题'}`, 
                            health.errors.length === 0 ? 'success' : 'warning');
                        
                        if (health.errors.length > 0) {
                            health.errors.forEach(error => {
                                log(`问题: ${error}`, 'warning');
                            });
                        }
                        
                        updateStatusGrid(health);
                    } else {
                        log('健康检查失败: 无法获取状态', 'error');
                    }
                } else {
                    log('健康检查方法不存在', 'error');
                }
            } catch (error) {
                log(`健康检查出错: ${error.message}`, 'error');
            }
        }

        function clearConsole() {
            if (typeof window.parent.clearConsoleErrors === 'function') {
                window.parent.clearConsoleErrors();
                log('控制台已清理', 'success');
            } else {
                console.clear();
                log('控制台已清理', 'success');
            }
        }

        function recoverFromErrors() {
            log('开始错误恢复...', 'warning');
            
            try {
                if (window.parent && typeof window.parent.recoverFromErrors === 'function') {
                    window.parent.recoverFromErrors();
                    log('错误恢复完成', 'success');
                } else {
                    log('错误恢复方法不存在', 'error');
                }
            } catch (error) {
                log(`错误恢复失败: ${error.message}`, 'error');
            }
        }

        function refreshSystem() {
            log('刷新系统...', 'info');
            
            try {
                if (window.parent && window.parent.dataManager) {
                    const dm = window.parent.dataManager;
                    
                    // 重新加载数据
                    dm.loadFromLocalStorage();
                    
                    // 更新界面
                    dm.renderTable();
                    dm.updateStats();
                    dm.renderAreaStats();
                    dm.renderUnproducedStats();
                    
                    // 更新仪表板
                    if (window.parent.dashboard) {
                        window.parent.dashboard.updateMetricsFromDataManager();
                        window.parent.dashboard.updateMetrics();
                    }
                    
                    log('系统刷新完成', 'success');
                } else {
                    log('无法访问系统组件', 'error');
                }
            } catch (error) {
                log(`系统刷新失败: ${error.message}`, 'error');
            }
        }

        function checkFirebaseStatus() {
            log('检查Firebase状态...', 'info');
            
            try {
                const isDisabled = localStorage.getItem('disableFirebase') === 'true';
                
                if (isDisabled) {
                    log('Firebase已被用户禁用，使用本地模式', 'warning');
                } else if (window.parent && window.parent.firebaseSync) {
                    const connected = window.parent.firebaseSync.isConnected();
                    log(`Firebase连接状态: ${connected ? '已连接' : '未连接'}`, 
                        connected ? 'success' : 'warning');
                } else {
                    log('Firebase模块未加载', 'warning');
                }
            } catch (error) {
                log(`检查Firebase状态失败: ${error.message}`, 'error');
            }
        }

        function checkDataIntegrity() {
            log('检查数据完整性...', 'info');
            
            try {
                if (window.parent && window.parent.dataManager) {
                    const dm = window.parent.dataManager;
                    
                    // 检查数据结构
                    const checks = {
                        data: Array.isArray(dm.data),
                        shippingHistory: Array.isArray(dm.shippingHistory),
                        materialPurchases: Array.isArray(dm.materialPurchases),
                        operationLogs: Array.isArray(dm.operationLogs)
                    };
                    
                    let allGood = true;
                    Object.entries(checks).forEach(([key, value]) => {
                        if (value) {
                            log(`✅ ${key}: 正常`, 'success');
                        } else {
                            log(`❌ ${key}: 异常`, 'error');
                            allGood = false;
                        }
                    });
                    
                    if (allGood) {
                        log('数据完整性检查通过', 'success');
                    } else {
                        log('数据完整性检查发现问题', 'warning');
                    }
                } else {
                    log('无法访问DataManager', 'error');
                }
            } catch (error) {
                log(`数据完整性检查失败: ${error.message}`, 'error');
            }
        }

        function fixShippedQuantity() {
            log('修复发货量显示...', 'warning');
            
            try {
                if (window.parent && typeof window.parent.forceRefreshShipped === 'function') {
                    window.parent.forceRefreshShipped();
                    log('发货量修复完成', 'success');
                } else {
                    log('发货量修复方法不存在', 'error');
                }
            } catch (error) {
                log(`发货量修复失败: ${error.message}`, 'error');
            }
        }

        function emergencyReset() {
            if (!confirm('确定要执行紧急重置吗？这将重新加载所有数据。')) {
                return;
            }
            
            log('执行紧急重置...', 'warning');
            
            try {
                // 清理控制台
                console.clear();
                
                // 重新加载页面
                if (window.parent) {
                    window.parent.location.reload();
                    log('页面重新加载中...', 'info');
                } else {
                    window.location.reload();
                }
            } catch (error) {
                log(`紧急重置失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动执行健康检查
        window.addEventListener('DOMContentLoaded', function() {
            log('控制台助手已加载', 'success');
            
            setTimeout(() => {
                performHealthCheck();
            }, 1000);
            
            // 定期更新状态
            setInterval(() => {
                if (window.parent && typeof window.parent.healthCheck === 'function') {
                    const health = window.parent.healthCheck();
                    updateStatusGrid(health);
                }
            }, 10000); // 每10秒更新一次
        });
    </script>
</body>
</html>
