<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发货历史管理功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            color: #059669;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .demo-info {
            background: #eff6ff;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .demo-info h3 {
            color: #3b82f6;
            margin: 0 0 15px 0;
        }
        
        .demo-section {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .demo-section h4 {
            color: #374151;
            margin: 0 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .shipping-record-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            background: white;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .shipping-record-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .shipping-record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .shipping-record-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }
        
        .shipping-record-date {
            font-size: 14px;
            color: #6b7280;
            background: #f3f4f6;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .shipping-record-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .shipping-record-field {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }
        
        .shipping-record-field .label {
            color: #6b7280;
            font-weight: 500;
        }
        
        .shipping-record-field .value {
            color: #1f2937;
        }
        
        .shipping-record-stats {
            display: flex;
            gap: 20px;
            padding: 12px 0;
            border-top: 1px solid #f3f4f6;
        }
        
        .shipping-stat {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        
        .shipping-stat .label {
            font-size: 12px;
            color: #6b7280;
        }
        
        .shipping-stat .value {
            font-size: 14px;
            font-weight: 600;
            color: #059669;
        }
        
        .summary-section {
            background: #ecfdf5;
            border: 1px solid #059669;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
        }
        
        .summary-label {
            color: #047857;
            font-weight: 500;
        }
        
        .summary-value {
            color: #059669;
            font-weight: 600;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            text-decoration: none;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .btn-success {
            background: #059669;
            color: white;
        }
        
        .btn-success:hover {
            background: #047857;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        
        .btn-warning:hover {
            background: #d97706;
        }
        
        .btn-danger {
            background: #dc2626;
            color: white;
        }
        
        .btn-danger:hover {
            background: #b91c1c;
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: end;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .filter-group label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
        }
        
        .filter-group input,
        .filter-group select {
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }
        
        .empty-state i {
            font-size: 48px;
            color: #d1d5db;
            margin-bottom: 16px;
            display: block;
        }
        
        @media (max-width: 768px) {
            .shipping-record-info {
                grid-template-columns: 1fr;
            }
            
            .shipping-record-stats {
                flex-wrap: wrap;
                gap: 10px;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
            }
            
            .filters {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>📋 发货历史管理功能演示</h1>
        
        <div class="demo-info">
            <h3>🎯 新功能特点</h3>
            <ul>
                <li><strong>历史记录查看</strong>：查看所有历史发货记录，支持筛选和搜索</li>
                <li><strong>详细信息编辑</strong>：点击记录可查看详情，支持编辑发货信息</li>
                <li><strong>项目管理</strong>：可以修改发货项目的数量，删除项目</li>
                <li><strong>记录删除</strong>：支持删除整条发货记录</li>
                <li><strong>数据导出</strong>：支持导出发货历史为CSV格式</li>
                <li><strong>统计汇总</strong>：实时显示发货次数、总量等统计信息</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h4>📊 发货历史统计</h4>
            <div class="summary-section">
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">发货次数：</span>
                        <span class="summary-value" id="totalRecords">5</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">总发货量：</span>
                        <span class="summary-value" id="totalQuantity">2,450 根</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">总重量：</span>
                        <span class="summary-value" id="totalWeight">12,250.5 kg</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">总米数：</span>
                        <span class="summary-value" id="totalMeters">2,940.0 m</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h4>🔍 筛选功能</h4>
            <div class="filters">
                <div class="filter-group">
                    <label>开始日期：</label>
                    <input type="date" id="dateFrom" value="2024-01-01">
                </div>
                <div class="filter-group">
                    <label>结束日期：</label>
                    <input type="date" id="dateTo" value="2024-12-31">
                </div>
                <div class="filter-group">
                    <label>客户：</label>
                    <select id="customerFilter">
                        <option value="">全部客户</option>
                        <option value="南通际铨">南通际铨</option>
                        <option value="盐城恒逸明">盐城恒逸明</option>
                        <option value="绍兴精工">绍兴精工</option>
                        <option value="上海福铁龙">上海福铁龙</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button class="btn btn-primary" onclick="filterRecords()">
                        <i class="fas fa-search"></i>
                        筛选
                    </button>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h4>📦 发货历史记录</h4>
            <div id="shippingRecords">
                <!-- 模拟发货记录 -->
                <div class="shipping-record-card" onclick="openDetailModal('record1')">
                    <div class="shipping-record-header">
                        <h4 class="shipping-record-title">南通际铨 - FH20241219143022</h4>
                        <span class="shipping-record-date">2024-12-19</span>
                    </div>
                    <div class="shipping-record-info">
                        <div class="shipping-record-field">
                            <span class="label">运输公司:</span>
                            <span class="value">顺丰物流</span>
                        </div>
                        <div class="shipping-record-field">
                            <span class="label">运单号:</span>
                            <span class="value">SF1234567890</span>
                        </div>
                        <div class="shipping-record-field">
                            <span class="label">收货地址:</span>
                            <span class="value">南通市工业园区</span>
                        </div>
                    </div>
                    <div class="shipping-record-stats">
                        <div class="shipping-stat">
                            <span class="label">规格数</span>
                            <span class="value">3</span>
                        </div>
                        <div class="shipping-stat">
                            <span class="label">总根数</span>
                            <span class="value">450</span>
                        </div>
                        <div class="shipping-stat">
                            <span class="label">总重量</span>
                            <span class="value">2,250.0 kg</span>
                        </div>
                        <div class="shipping-stat">
                            <span class="label">总米数</span>
                            <span class="value">540.0 m</span>
                        </div>
                    </div>
                </div>
                
                <div class="shipping-record-card" onclick="openDetailModal('record2')">
                    <div class="shipping-record-header">
                        <h4 class="shipping-record-title">绍兴精工 - FH20241218102045</h4>
                        <span class="shipping-record-date">2024-12-18</span>
                    </div>
                    <div class="shipping-record-info">
                        <div class="shipping-record-field">
                            <span class="label">运输公司:</span>
                            <span class="value">德邦物流</span>
                        </div>
                        <div class="shipping-record-field">
                            <span class="label">运单号:</span>
                            <span class="value">DB9876543210</span>
                        </div>
                        <div class="shipping-record-field">
                            <span class="label">收货地址:</span>
                            <span class="value">绍兴市越城区</span>
                        </div>
                    </div>
                    <div class="shipping-record-stats">
                        <div class="shipping-stat">
                            <span class="label">规格数</span>
                            <span class="value">5</span>
                        </div>
                        <div class="shipping-stat">
                            <span class="label">总根数</span>
                            <span class="value">680</span>
                        </div>
                        <div class="shipping-stat">
                            <span class="label">总重量</span>
                            <span class="value">3,400.0 kg</span>
                        </div>
                        <div class="shipping-stat">
                            <span class="label">总米数</span>
                            <span class="value">816.0 m</span>
                        </div>
                    </div>
                </div>
                
                <div class="shipping-record-card" onclick="openDetailModal('record3')">
                    <div class="shipping-record-header">
                        <h4 class="shipping-record-title">上海福铁龙 - FH20241217085530</h4>
                        <span class="shipping-record-date">2024-12-17</span>
                    </div>
                    <div class="shipping-record-info">
                        <div class="shipping-record-field">
                            <span class="label">运输公司:</span>
                            <span class="value">中通快运</span>
                        </div>
                        <div class="shipping-record-field">
                            <span class="label">运单号:</span>
                            <span class="value">ZT5555666677</span>
                        </div>
                        <div class="shipping-record-field">
                            <span class="label">收货地址:</span>
                            <span class="value">上海市浦东新区</span>
                        </div>
                    </div>
                    <div class="shipping-record-stats">
                        <div class="shipping-stat">
                            <span class="label">规格数</span>
                            <span class="value">4</span>
                        </div>
                        <div class="shipping-stat">
                            <span class="label">总根数</span>
                            <span class="value">320</span>
                        </div>
                        <div class="shipping-stat">
                            <span class="label">总重量</span>
                            <span class="value">1,600.0 kg</span>
                        </div>
                        <div class="shipping-stat">
                            <span class="label">总米数</span>
                            <span class="value">384.0 m</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="action-buttons">
            <button class="btn btn-primary" onclick="openMainSystem()">
                🚀 在主系统中测试
            </button>
            <button class="btn btn-success" onclick="exportHistory()">
                📥 导出历史
            </button>
            <button class="btn btn-warning" onclick="simulateNewShipping()">
                📦 模拟新发货
            </button>
        </div>
    </div>

    <script>
        function filterRecords() {
            const dateFrom = document.getElementById('dateFrom').value;
            const dateTo = document.getElementById('dateTo').value;
            const customer = document.getElementById('customerFilter').value;
            
            alert(`筛选条件：\n开始日期: ${dateFrom}\n结束日期: ${dateTo}\n客户: ${customer || '全部'}`);
        }
        
        function openDetailModal(recordId) {
            alert(`打开发货详情：${recordId}\n\n在主系统中，这里会显示：\n• 发货基本信息编辑\n• 发货项目列表\n• 数量修改功能\n• 项目删除功能\n• 整单删除功能`);
        }
        
        function exportHistory() {
            alert('导出发货历史为CSV文件\n\n包含字段：\n• 发货日期\n• 客户名称\n• 运输公司\n• 运单号\n• 收货地址\n• 规格数量\n• 总根数\n• 总重量\n• 总米数\n• 备注');
        }
        
        function simulateNewShipping() {
            alert('模拟新发货操作\n\n新发货完成后会自动：\n• 添加到发货历史\n• 更新统计数据\n• 生成发货单号\n• 记录操作日志');
        }
        
        function openMainSystem() {
            window.open('index.html', '_blank');
        }
    </script>
</body>
</html>
