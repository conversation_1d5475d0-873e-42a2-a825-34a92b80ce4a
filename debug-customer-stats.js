// 调试客户发货统计数据的脚本

(function() {
    'use strict';
    
    console.log('🔍 调试客户发货统计数据...');
    
    function debugCustomerStats() {
        if (!window.dataManager) {
            console.log('❌ DataManager不存在');
            return;
        }
        
        const dm = window.dataManager;
        
        console.log('📊 开始详细分析客户发货统计...');
        
        // 1. 获取客户统计数据
        const customerStats = dm.calculateCustomerStats();
        console.log('原始客户统计数据:', customerStats);
        
        // 2. 详细分析每个客户
        let manualTotal = 0;
        console.log('\n🔍 逐个分析客户数据:');
        console.log('='.repeat(50));
        
        customerStats.forEach((customer, index) => {
            console.log(`\n客户 ${index + 1}: ${customer.customerName}`);
            console.log(`  - totalMeters: ${customer.totalMeters} (类型: ${typeof customer.totalMeters})`);
            console.log(`  - totalQuantity: ${customer.totalQuantity}`);
            console.log(`  - isPredefined: ${customer.isPredefined}`);
            console.log(`  - orderCount: ${customer.orderCount}`);
            console.log(`  - specsCount: ${customer.specsCount}`);
            
            // 检查数据有效性
            const meters = parseFloat(customer.totalMeters) || 0;
            if (meters > 0) {
                manualTotal += meters;
                console.log(`  ✅ 有效发货量: ${meters.toFixed(1)}米 (累计: ${manualTotal.toFixed(1)}米)`);
            } else {
                console.log(`  ⚠️ 无发货量或数据无效`);
            }
        });
        
        console.log('\n📈 汇总结果:');
        console.log(`手动计算总发货量: ${manualTotal.toFixed(1)}米`);
        
        // 3. 检查发货历史数据
        console.log('\n📋 检查发货历史数据:');
        console.log('='.repeat(50));
        
        if (dm.shippingHistory && dm.shippingHistory.length > 0) {
            console.log(`发货历史记录数: ${dm.shippingHistory.length}`);
            
            let historyTotal = 0;
            dm.shippingHistory.forEach((record, index) => {
                console.log(`\n发货记录 ${index + 1}:`);
                console.log(`  - 客户: ${record.customerName}`);
                console.log(`  - 日期: ${record.date}`);
                console.log(`  - 总米数: ${record.totalMeters}`);
                console.log(`  - 总根数: ${record.totalQuantity}`);
                console.log(`  - 项目数: ${record.items ? record.items.length : 0}`);
                
                const meters = parseFloat(record.totalMeters) || 0;
                historyTotal += meters;
                
                if (record.items && record.items.length > 0) {
                    console.log(`  - 项目详情:`);
                    record.items.forEach((item, itemIndex) => {
                        console.log(`    ${itemIndex + 1}. ${item.spec} - ${item.quantity}根 - ${item.meters}米`);
                    });
                }
            });
            
            console.log(`\n发货历史总计: ${historyTotal.toFixed(1)}米`);
        } else {
            console.log('❌ 没有发货历史记录');
        }
        
        // 4. 检查生产数据中的shipped字段
        console.log('\n🏭 检查生产数据中的shipped字段:');
        console.log('='.repeat(50));
        
        let productionShippedTotal = 0;
        let shippedItems = 0;
        
        dm.data.forEach((item, index) => {
            if (item.shipped && item.shipped > 0) {
                shippedItems++;
                console.log(`${index + 1}. ${item.spec} (${item.area}) - 已发货: ${item.shipped}根`);
                
                // 计算米数
                const length = dm.extractLengthFromSpec(item.spec);
                const meters = (item.shipped * length) / 1000;
                productionShippedTotal += meters;
                
                console.log(`   长度: ${length}mm, 米数: ${meters.toFixed(1)}米`);
            }
        });
        
        console.log(`\n生产数据中已发货项目数: ${shippedItems}`);
        console.log(`生产数据中已发货总米数: ${productionShippedTotal.toFixed(1)}米`);
        
        // 5. 检查仪表板当前数据
        console.log('\n🎛️ 检查仪表板当前数据:');
        console.log('='.repeat(50));
        
        if (window.dashboard && window.dashboard.data) {
            const dashboardData = window.dashboard.data;
            console.log('仪表板数据:');
            console.log(`  - shippedMeters: ${dashboardData.shippedMeters}`);
            console.log(`  - producedMeters: ${dashboardData.producedMeters}`);
            console.log(`  - unshippedMeters: ${dashboardData.unshippedMeters}`);
        } else {
            console.log('❌ 仪表板数据不可用');
        }
        
        // 6. 检查DOM显示
        console.log('\n🎨 检查DOM显示:');
        console.log('='.repeat(50));
        
        const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
        const unshippedElement = document.querySelector('.metric-card.unshipped .metric-value');
        const customerTotalElement = document.getElementById('totalShippedMeters');
        
        console.log(`DOM已发货量显示: ${shippedElement ? shippedElement.textContent : '未找到'}`);
        console.log(`DOM未发货量显示: ${unshippedElement ? unshippedElement.textContent : '未找到'}`);
        console.log(`客户统计总计显示: ${customerTotalElement ? customerTotalElement.textContent : '未找到'}`);
        
        // 7. 总结和建议
        console.log('\n📋 总结和建议:');
        console.log('='.repeat(50));
        console.log(`客户统计计算总量: ${manualTotal.toFixed(1)}米`);
        console.log(`当前仪表板显示: ${shippedElement ? shippedElement.textContent : '未知'}米`);
        
        if (Math.abs(manualTotal - parseFloat(shippedElement?.textContent || 0)) > 0.1) {
            console.log('❌ 数据不一致！需要强制更新仪表板');
            
            // 立即修复
            console.log('🔧 立即修复仪表板显示...');
            fixDashboardDisplay(manualTotal);
        } else {
            console.log('✅ 数据一致');
        }
        
        return {
            customerStatsTotal: manualTotal,
            historyTotal: historyTotal || 0,
            productionTotal: productionShippedTotal,
            dashboardDisplay: parseFloat(shippedElement?.textContent || 0)
        };
    }
    
    function fixDashboardDisplay(correctTotal) {
        console.log(`🔧 修复仪表板显示为: ${correctTotal.toFixed(1)}米`);
        
        // 更新仪表板数据
        if (window.dashboard && window.dashboard.data) {
            window.dashboard.data.shippedMeters = correctTotal;
            window.dashboard.data.unshippedMeters = Math.max(0, (window.dashboard.data.producedMeters || 0) - correctTotal);
        }
        
        // 更新DOM
        const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
        if (shippedElement) {
            shippedElement.textContent = correctTotal.toFixed(1);
            
            // 添加视觉效果
            shippedElement.style.backgroundColor = '#10b981';
            shippedElement.style.color = 'white';
            shippedElement.style.fontWeight = 'bold';
            shippedElement.style.padding = '4px 8px';
            shippedElement.style.borderRadius = '4px';
            shippedElement.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                shippedElement.style.backgroundColor = '';
                shippedElement.style.color = '';
                shippedElement.style.padding = '';
                shippedElement.style.borderRadius = '';
            }, 3000);
            
            console.log('✅ 仪表板显示已更新');
        }
        
        // 更新未发货量
        const unshippedElement = document.querySelector('.metric-card.unshipped .metric-value');
        if (unshippedElement && window.dashboard && window.dashboard.data) {
            unshippedElement.textContent = window.dashboard.data.unshippedMeters.toFixed(1);
            console.log('✅ 未发货量显示已更新');
        }
        
        // 保存数据
        if (window.dataManager) {
            window.dataManager.saveToLocalStorage();
        }
        
        // 显示成功消息
        if (window.dataManager && window.dataManager.showNotification) {
            window.dataManager.showNotification(
                `✅ 发货量已修复！正确显示: ${correctTotal.toFixed(1)}米`, 
                'success'
            );
        }
    }
    
    // 添加全局方法
    window.debugCustomerStats = debugCustomerStats;
    window.fixDashboardDisplay = fixDashboardDisplay;
    
    // 立即执行调试
    setTimeout(() => {
        console.log('🚀 自动执行客户统计调试...');
        debugCustomerStats();
    }, 3000);
    
    console.log('🚀 客户统计调试脚本已加载');
    console.log('💡 可用命令:');
    console.log('  - debugCustomerStats() - 详细调试客户统计');
    console.log('  - fixDashboardDisplay(数值) - 强制修复仪表板显示');
    
})();
