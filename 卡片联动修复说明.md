# 卡片联动功能修复说明

## 问题描述
仪表板上的几个关键指标卡片（总需求量、已生产量、待生产量、已发货量、未发货量等）之间缺乏有效的联动机制，导致数据更新时卡片显示不同步。

## 修复内容

### 1. 新增模块化数据更新机制
- **文件**: `scripts/main.js`
- **新增方法**: `updateMetricsFromModules()`
- **功能**: 支持新的模块化架构（DataCore、ProductionManager、ShippingManager）
- **特点**: 
  - 优先使用新架构，兼容传统架构
  - 统一的数据计算逻辑
  - 自动计算派生数据（待生产量、未发货量等）

### 2. 卡片联动修复脚本
- **文件**: `fix-dashboard-cards-linkage.js`
- **功能**: 
  - 强制更新所有卡片数据
  - 监听数据变化事件
  - 定期自动更新（每30秒）
  - 页面可见性变化时自动刷新
- **特点**:
  - 兼容新旧架构
  - 智能数据源选择
  - 实时数据同步

### 3. 数据管理器增强
- **文件**: `scripts/data-management.js`
- **新增方法**: `triggerCardLinkageUpdate()`
- **功能**: 
  - 在数据更新时触发自定义事件
  - 确保卡片联动更新
  - 提供详细的更新日志

### 4. 主页面集成
- **文件**: `index.html`
- **新增功能**:
  - 自动加载修复脚本
  - 数据清空事件监听

## 使用方法

### 1. 自动修复
系统会自动加载修复脚本，无需手动操作。修复脚本会：
- 在页面加载时自动检查和修复
- 监听数据变化并自动更新卡片
- 每30秒定期检查数据一致性

### 2. 数据清空联动
1. 使用"清空所有数据"功能时，系统会自动重置所有卡片
2. 确保清空后所有显示都归零

## 技术特点

### 1. 兼容性设计
- 支持新的模块化架构（DataCore、ProductionManager等）
- 兼容传统架构（DataManager）
- 自动检测可用的数据源

### 2. 智能数据计算
- 统一的米制数据计算方法
- 自动提取规格中的长度信息
- 智能的库存状态计算

### 3. 事件驱动更新
- 使用自定义事件`dataUpdated`
- 监听页面可见性变化
- 支持多种触发方式

### 4. 错误处理
- 完善的异常捕获
- 详细的错误日志
- 自动回退机制

## 数据流程

```
数据源 → 计算引擎 → 卡片显示
  ↓         ↓         ↓
DataCore  统一计算   实时更新
DataManager → 米制转换 → 动画效果
  ↓         ↓         ↓
事件触发 → 联动更新 → 状态同步
```

## 监控指标

### 1. 数据一致性
- 总需求量 = 计划数量 × 长度 ÷ 1000
- 待生产量 = 总需求量 - 已生产量
- 未发货量 = 已生产量 - 已发货量
- 完成率 = 已生产量 ÷ 总需求量 × 100%

### 2. 系统状态
- 数据源可用性
- 模块加载状态
- 事件监听状态
- 更新频率

## 故障排除

### 1. 卡片显示为0或异常
- 检查数据源是否正常加载
- 查看浏览器控制台日志
- 执行强制刷新操作

### 2. 数据不同步
- 检查事件监听是否正常
- 查看浏览器控制台日志
- 尝试重新导入数据

### 3. 计算结果错误
- 检查规格长度提取是否正确
- 验证数据格式是否符合要求
- 使用对比工具检查计算逻辑

## 维护建议

1. **定期检查**: 定期检查浏览器控制台日志
2. **数据验证**: 定期对比计算结果和实际数据
3. **日志监控**: 关注浏览器控制台的错误信息
4. **性能优化**: 根据使用情况调整更新频率

## 更新日志

- **2024-12-21**: 初始版本发布
  - 实现基础联动功能
  - 完善数据清空功能
  - 支持新旧架构兼容

---

**注意**: 如果遇到问题，请查看浏览器控制台日志，或联系技术支持。
