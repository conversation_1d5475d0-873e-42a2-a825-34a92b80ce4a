<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>已发货量修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            transition: all 0.2s;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-info { background: #06b6d4; color: white; }
        .btn:hover { opacity: 0.9; transform: translateY(-1px); }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .warning { border-color: #ffc107; background: #fff3cd; }
        .info { border-color: #17a2b8; background: #d1ecf1; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 已发货量修复测试工具</h1>
        <p>测试和修复已发货量显示为0的问题</p>

        <div style="margin: 20px 0;">
            <button class="btn btn-primary" onclick="testShippedData()">🔍 检查发货数据</button>
            <button class="btn btn-info" onclick="debugShippedQuantity()">🐛 调试发货量</button>
            <button class="btn btn-success" onclick="forceRefreshShipped()">🔄 强制刷新</button>
            <button class="btn btn-warning" onclick="addTestShipping()">➕ 添加测试发货</button>
        </div>

        <div style="margin: 20px 0;">
            <button class="btn btn-primary" onclick="openMainSystem()">🏠 打开主系统</button>
            <button class="btn btn-success" onclick="openShippingModal()">📦 打开发货管理</button>
            <button class="btn btn-info" onclick="checkDashboard()">📊 检查仪表板</button>
        </div>

        <div class="stats-grid" id="statsGrid">
            <!-- 统计卡片将在这里动态生成 -->
        </div>

        <div id="testResults"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `[${timestamp}] ${message}`;
            
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateStats() {
            const statsGrid = document.getElementById('statsGrid');
            
            try {
                if (window.parent && window.parent.dataManager) {
                    const dm = window.parent.dataManager;
                    const dashboard = window.parent.dashboard;
                    
                    // 计算统计数据
                    let totalShipped = 0;
                    let recordsWithShipped = 0;
                    
                    dm.data.forEach(item => {
                        if (item.shipped && item.shipped > 0) {
                            recordsWithShipped++;
                            totalShipped += item.shipped;
                        }
                    });
                    
                    const historyRecords = dm.shippingHistory ? dm.shippingHistory.length : 0;
                    const dashboardShipped = dashboard?.data?.shippedMeters || 0;
                    const dashboardUnshipped = dashboard?.data?.unshippedMeters || 0;
                    
                    statsGrid.innerHTML = `
                        <div class="stat-card">
                            <div class="stat-value">${totalShipped}</div>
                            <div class="stat-label">数据中已发货(根)</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${recordsWithShipped}</div>
                            <div class="stat-label">有发货记录数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${historyRecords}</div>
                            <div class="stat-label">发货历史记录</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${dashboardShipped.toFixed(1)}</div>
                            <div class="stat-label">仪表板已发货(米)</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${dashboardUnshipped.toFixed(1)}</div>
                            <div class="stat-label">仪表板未发货(米)</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${dm.data.length}</div>
                            <div class="stat-label">总数据记录数</div>
                        </div>
                    `;
                } else {
                    statsGrid.innerHTML = `
                        <div class="stat-card">
                            <div class="stat-value">N/A</div>
                            <div class="stat-label">无法访问数据</div>
                        </div>
                    `;
                }
            } catch (error) {
                log(`❌ 更新统计时出错: ${error.message}`, 'error');
            }
        }

        function testShippedData() {
            log('🔍 开始检查发货数据...', 'info');
            
            try {
                if (window.parent && window.parent.dataManager) {
                    const dm = window.parent.dataManager;
                    
                    log(`📊 总数据记录: ${dm.data.length} 条`, 'info');
                    
                    // 检查shipped字段
                    let totalShipped = 0;
                    let recordsWithShipped = 0;
                    let recordsWithoutShipped = 0;
                    
                    dm.data.forEach((item, index) => {
                        if (item.shipped && item.shipped > 0) {
                            recordsWithShipped++;
                            totalShipped += item.shipped;
                            if (recordsWithShipped <= 3) {
                                log(`✅ 第${index + 1}条有发货: ${item.spec} (${item.area}) - ${item.shipped}根`, 'success');
                            }
                        } else {
                            recordsWithoutShipped++;
                        }
                    });
                    
                    log(`📦 发货统计: ${recordsWithShipped} 条有发货，${recordsWithoutShipped} 条无发货，总计 ${totalShipped} 根`, 'info');
                    
                    // 检查发货历史
                    if (dm.shippingHistory && dm.shippingHistory.length > 0) {
                        log(`📋 发货历史: ${dm.shippingHistory.length} 条记录`, 'success');
                        
                        let historyTotal = 0;
                        dm.shippingHistory.forEach((record, index) => {
                            const quantity = record.items ? record.items.reduce((sum, item) => sum + (item.quantity || 0), 0) : 0;
                            historyTotal += quantity;
                            if (index < 3) {
                                log(`  ${index + 1}. ${record.customerName} (${record.date}): ${quantity}根`, 'info');
                            }
                        });
                        
                        log(`📋 历史记录总计: ${historyTotal} 根`, 'info');
                        
                        if (totalShipped !== historyTotal) {
                            log(`⚠️ 数据不一致: 数据中${totalShipped}根 vs 历史${historyTotal}根`, 'warning');
                        }
                    } else {
                        log('❌ 没有发货历史记录', 'error');
                    }
                    
                    // 检查仪表板数据
                    if (window.parent.dashboard && window.parent.dashboard.data) {
                        const dashboardShipped = window.parent.dashboard.data.shippedMeters || 0;
                        log(`📊 仪表板显示: ${dashboardShipped.toFixed(1)} 米`, 'info');
                        
                        if (dashboardShipped === 0 && totalShipped > 0) {
                            log('❌ 仪表板显示为0但数据中有发货记录', 'error');
                        }
                    }
                    
                } else {
                    log('❌ 无法访问DataManager', 'error');
                }
                
                updateStats();
                
            } catch (error) {
                log(`❌ 检查发货数据时出错: ${error.message}`, 'error');
            }
        }

        function debugShippedQuantity() {
            log('🐛 开始调试发货量...', 'info');
            
            try {
                if (window.parent && typeof window.parent.debugShipped === 'function') {
                    const result = window.parent.debugShipped();
                    log(`✅ 调试结果: ${JSON.stringify(result)}`, 'success');
                } else {
                    log('❌ 调试方法不存在', 'error');
                }
                
                updateStats();
                
            } catch (error) {
                log(`❌ 调试时出错: ${error.message}`, 'error');
            }
        }

        function forceRefreshShipped() {
            log('🔄 开始强制刷新发货量...', 'warning');
            
            try {
                if (window.parent && typeof window.parent.forceRefreshShipped === 'function') {
                    window.parent.forceRefreshShipped();
                    log('✅ 强制刷新完成', 'success');
                    
                    // 延迟检查结果
                    setTimeout(() => {
                        testShippedData();
                    }, 1000);
                } else {
                    log('❌ 强制刷新方法不存在', 'error');
                }
                
            } catch (error) {
                log(`❌ 强制刷新时出错: ${error.message}`, 'error');
            }
        }

        function addTestShipping() {
            log('➕ 开始添加测试发货数据...', 'warning');
            
            try {
                if (window.parent && typeof window.parent.addTestShipping === 'function') {
                    const result = window.parent.addTestShipping();
                    if (result) {
                        log(`✅ 已添加测试发货: ${result.customerName} - ${result.totalQuantity}根`, 'success');
                        
                        // 延迟检查结果
                        setTimeout(() => {
                            testShippedData();
                        }, 1000);
                    } else {
                        log('⚠️ 无法添加测试发货数据', 'warning');
                    }
                } else {
                    log('❌ 添加测试发货方法不存在', 'error');
                }
                
            } catch (error) {
                log(`❌ 添加测试发货时出错: ${error.message}`, 'error');
            }
        }

        function openMainSystem() {
            window.open('index.html', '_blank');
        }

        function openShippingModal() {
            try {
                const mainWindow = window.open('index.html', '_blank');
                
                setTimeout(() => {
                    if (mainWindow && mainWindow.dataManager) {
                        mainWindow.dataManager.openShippingModal();
                        log('✅ 已打开发货管理模态框', 'success');
                    }
                }, 2000);
                
            } catch (error) {
                log(`❌ 打开发货模态框时出错: ${error.message}`, 'error');
            }
        }

        function checkDashboard() {
            log('📊 检查仪表板状态...', 'info');
            
            try {
                if (window.parent && window.parent.dashboard) {
                    const dashboard = window.parent.dashboard;
                    
                    log('✅ Dashboard存在', 'success');
                    log(`📊 当前数据: ${JSON.stringify(dashboard.data)}`, 'info');
                    
                    // 强制更新仪表板
                    dashboard.updateMetricsFromDataManager();
                    dashboard.updateMetrics();
                    
                    log('🔄 已强制更新仪表板', 'success');
                    
                    setTimeout(() => {
                        updateStats();
                    }, 500);
                    
                } else {
                    log('❌ Dashboard不存在', 'error');
                }
                
            } catch (error) {
                log(`❌ 检查仪表板时出错: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动运行测试
        window.addEventListener('DOMContentLoaded', function() {
            log('🚀 已发货量修复测试工具已加载', 'info');
            
            // 等待一段时间后自动测试
            setTimeout(() => {
                testShippedData();
            }, 1000);
            
            // 定期更新统计
            setInterval(updateStats, 5000);
        });
    </script>
</body>
</html>
