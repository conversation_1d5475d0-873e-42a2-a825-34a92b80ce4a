# 📦 多规格批次发货计划功能说明

## 🎯 功能概述

为客户发货统计模块的每个客户卡片添加了"批次发货计划"功能，支持创建包含多个规格的发货计划，实时检查库存状态，并一键转换为发货单执行。

## ✨ 核心特性

### 📋 多规格计划管理
- **多计划支持**：可为同一客户创建多个独立的发货计划
- **多规格支持**：每个计划可包含多个不同规格的项目
- **计划命名**：支持自定义计划名称，便于管理和识别
- **灵活配置**：可随时添加、删除、修改规格项目
- **型号联动**：支持H80/H100型号自动联动规格选择
- **实时库存**：自动显示每个规格的可发货数量
- **状态检查**：智能判断每个规格的库存是否充足

### 🎨 可视化状态显示
- **绿色状态**：库存充足，可以发货（sufficient）
- **红色状态**：库存不足，显示缺少数量（insufficient）
- **灰色状态**：信息不完整，待完善（pending）
- **智能汇总**：显示计划整体状态和统计信息

### 🚀 一键执行发货
- **自动筛选**：只执行库存充足的规格项目
- **批量转换**：将多个规格转换为标准发货单格式
- **预填数据**：自动填入客户信息和规格数据
- **无缝集成**：与现有发货流程完美集成

## 🎮 使用流程

### 1. 打开发货计划
- 在客户发货统计卡片上点击"批次发货计划"按钮
- 系统打开专用的发货计划模态框

### 2. 创建发货计划
- 点击"新增发货计划"按钮创建新计划
- 为计划输入自定义名称（如"月度发货计划"）
- 系统自动生成计划编号

### 3. 添加规格项目
- 点击计划卡片中的"添加规格"按钮
- 为每个规格项目选择型号类型（H80或H100）
- 根据型号自动加载可用规格列表
- 选择具体规格型号
- 输入该规格的计划发货数量
- 可添加多个不同规格到同一个计划中

### 4. 状态检查和管理
- 系统自动计算每个规格的可发货数量
- 实时显示库存状态：
  - ✅ **绿色**：库存充足，可以发货
  - ⚠️ **红色**：库存不足，显示缺少数量
  - ❓ **灰色**：信息不完整，需要完善
- 查看计划汇总统计信息
- 可随时修改或删除规格项目

### 5. 执行发货
- 点击"执行发货计划"按钮
- 系统自动筛选库存充足的规格项目
- 转换为发货单并预填客户信息
- 进入标准发货流程

## 🔧 技术实现

### 数据结构
```javascript
// 发货计划数据结构（多规格版本）
{
    id: "唯一标识",
    name: "计划名称",
    items: [  // 包含多个规格项目的数组
        {
            id: "规格项目唯一标识",
            modelType: "H80/H100",
            spec: "规格型号",
            quantity: "计划数量",
            availableQuantity: "可发货数量",
            status: "sufficient/insufficient/pending"
        },
        // ... 更多规格项目
    ],
    status: "sufficient/insufficient/pending", // 整个计划的状态
    createdAt: "创建时间"
}
```

### 核心方法
```javascript
// 计划管理
addShippingPlan()                           // 添加新的发货计划
updatePlanName(planId, name)                // 更新计划名称
removeShippingPlan(planId)                  // 删除发货计划

// 规格项目管理
addPlanItem(planId)                         // 添加规格项目
removePlanItem(planId, itemIndex)           // 删除规格项目
updatePlanItemModel(planId, itemIndex, modelType)  // 更新项目型号
updatePlanItemSpec(planId, itemIndex, spec)         // 更新项目规格
updatePlanItemQuantity(planId, itemIndex, quantity) // 更新项目数量

// 状态管理
updatePlanItemStatus(item)                  // 更新项目状态
getPlanOverallStatus(plan)                  // 获取计划整体状态
getPlanSummary(plan)                        // 获取计划汇总信息

// 执行发货
executeShippingPlans()                      // 执行发货计划
convertItemsToShipping(items)               // 转换为发货单
```

### 界面组件
- **计划卡片**：显示计划基本信息和操作按钮
- **规格项目卡片**：显示单个规格的详细信息
- **汇总统计**：显示计划的整体状态和数据
- **操作按钮**：添加、删除、修改等操作

## 🎨 界面设计

### 层级结构
```
发货计划模态框
├── 计划控制区域
│   ├── 新增发货计划按钮
│   └── 执行发货计划按钮
└── 计划列表容器
    ├── 计划卡片 #1
    │   ├── 计划标题和操作
    │   ├── 规格项目列表
    │   │   ├── 规格项目 #1
    │   │   ├── 规格项目 #2
    │   │   └── ...
    │   └── 计划汇总统计
    ├── 计划卡片 #2
    └── ...
```

### 状态可视化
- **计划级别状态**：整个计划卡片的边框颜色
- **项目级别状态**：单个规格项目的背景色和图标
- **汇总统计**：数字徽章显示各状态的项目数量

## 💾 数据存储

### 本地存储结构
```javascript
{
    "客户名称1": [
        {
            id: "plan1",
            name: "月度发货计划",
            items: [
                { spec: "H80-1200mm", quantity: 100, ... },
                { spec: "H100-1500mm", quantity: 50, ... }
            ]
        },
        // ... 更多计划
    ],
    "客户名称2": [...],
    ...
}
```

## 🔄 与现有系统集成

### 库存数据联动
- 实时查询生产数据中的库存信息
- 计算公式：可发货数量 = 已生产数量 - 已发货数量
- 自动更新状态显示

### 发货流程集成
- 计划执行后自动打开批量发货模态框
- 预填客户名称和所有规格项目
- 保持原有发货流程不变

## 📱 响应式支持

- 移动设备优化布局
- 触摸友好的操作界面
- 自适应屏幕尺寸
- 计划卡片堆叠显示

## 🛡️ 数据验证

- 规格选择验证
- 数量范围检查
- 库存充足性验证
- 重复规格检测

## 🎯 使用场景

1. **月度发货计划**：提前规划下月各规格的发货量
2. **紧急订单处理**：快速检查多个规格的库存状态
3. **客户定制需求**：为特定客户组合多种规格
4. **库存清理**：批量处理积压库存的发货

这个多规格版本的批次发货计划功能大大提升了发货管理的灵活性和效率！
