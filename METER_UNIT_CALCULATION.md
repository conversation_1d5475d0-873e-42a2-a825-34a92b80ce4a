# 米制单位换算说明

## 📏 单位换算功能

根据用户要求，主界面的总需求量、已生产量、未生产量现在显示为米制单位（m），而不是根数。

## 🧮 计算方法

### 换算公式
```
米数 = 根数 × 长度（mm） ÷ 1000
```

### 示例计算
```
H100-2000mm × 68根 = 68 × 2000 ÷ 1000 = 136米
H100-4400mm × 263根 = 263 × 4400 ÷ 1000 = 1157.2米
```

## 📊 浦东机场数据换算

### 详细计算表

| 规格型号 | 根数 | 长度(mm) | 米数计算 | 结果(m) |
|----------|------|----------|----------|---------|
| H100-400mm | 2 | 400 | 2×0.4 | 0.8 |
| H100-1400mm | 2 | 1400 | 2×1.4 | 2.8 |
| H100-1600mm | 4 | 1600 | 4×1.6 | 6.4 |
| H100-1800mm | 8 | 1800 | 8×1.8 | 14.4 |
| H100-2000mm | 68 | 2000 | 68×2.0 | 136.0 |
| H100-2200mm | 12 | 2200 | 12×2.2 | 26.4 |
| H100-2400mm | 66 | 2400 | 66×2.4 | 158.4 |
| H100-2800mm | 23 | 2800 | 23×2.8 | 64.4 |
| H100-3000mm | 13 | 3000 | 13×3.0 | 39.0 |
| H100-3200mm | 4 | 3200 | 4×3.2 | 12.8 |
| H100-3400mm | 8 | 3400 | 8×3.4 | 27.2 |
| H100-3600mm | 66 | 3600 | 66×3.6 | 237.6 |
| H100-3800mm | 25 | 3800 | 25×3.8 | 95.0 |
| H100-4000mm | 37 | 4000 | 37×4.0 | 148.0 |
| H100-4200mm | 13 | 4200 | 13×4.2 | 54.6 |
| H100-4400mm | 263 | 4400 | 263×4.4 | 1157.2 |
| H100-4600mm | 90 | 4600 | 90×4.6 | 414.0 |
| H100-4800mm | 342 | 4800 | 342×4.8 | 1641.6 |
| H100-5000mm | 13 | 5000 | 13×5.0 | 65.0 |

### 总计算结果
**总需求量：4301.6米**

## 🔧 系统实现

### JavaScript计算逻辑
```javascript
// 计算总米数（根数 × 长度）
this.data.totalDemandMeters = data.reduce((sum, item) => {
    const length = this.extractLengthFromSpec(item.spec); // 提取长度（mm）
    return sum + (item.planned * length / 1000); // 转换为米
}, 0);

// 从规格型号中提取长度
extractLengthFromSpec(spec) {
    const match = spec.match(/(\d+)mm/);
    return match ? parseInt(match[1]) : 0;
}
```

### 规格解析示例
```javascript
"H100-2000mm" → 提取 "2000" → 转换为数字 2000
"H80-1400mm"  → 提取 "1400" → 转换为数字 1400
```

## 📋 界面显示变化

### 之前显示
```
总需求量: 1,059 根数
已生产量: 0 根数  
未生产量: 1,059 根数
```

### 现在显示
```
总需求量: 4,301.6 m
已生产量: 0.0 m
未生产量: 4,301.6 m
```

## 🎯 优势说明

### 更直观的数量表示
- **米制单位**：更符合工程习惯
- **实际长度**：反映真实的钢筋总长度
- **便于估算**：方便材料成本和重量估算

### 保留完成率计算
- **完成率**：仍然基于根数计算（更准确）
- **状态判断**：基于根数进行状态更新
- **数据一致性**：内部逻辑保持不变

## 🔍 验证方法

### 手动验证
1. 查看数据表格中的具体记录
2. 手动计算：根数 × 长度(mm) ÷ 1000
3. 对比系统显示的米数

### 示例验证
```
H100-4800mm × 342根 = 342 × 4.8 = 1641.6米
H100-4400mm × 263根 = 263 × 4.4 = 1157.2米
这两项合计 = 1641.6 + 1157.2 = 2798.8米
```

## 📊 数据对比

### 根数统计
- **总根数**：1,059根
- **平均长度**：约4.06米/根
- **最短规格**：400mm (0.4米)
- **最长规格**：5000mm (5.0米)

### 米数统计  
- **总米数**：4,301.6米
- **最大单项**：H100-4800mm (1641.6米)
- **最小单项**：H100-400mm (0.8米)
- **占比最大**：4800mm规格占38.2%

## 🎮 使用说明

### 查看米制数据
1. 主界面直接显示米制单位
2. 数据表格仍显示根数（便于管理）
3. 完成率基于根数计算（更准确）

### 数据录入
1. **新增计划/生产**：仍然以根数为单位录入
2. **系统自动换算**：后台自动计算米数
3. **实时更新**：米制显示实时更新

### 导入导出
1. **Excel导入**：以根数导入，自动换算米数
2. **JSON导出**：包含根数和米数两种数据
3. **数据一致性**：确保换算准确

## 🔄 技术细节

### 长度提取正则表达式
```javascript
const match = spec.match(/(\d+)mm/);
```
- 匹配规格中的数字+mm格式
- 提取纯数字部分
- 转换为整数用于计算

### 精度处理
- **显示精度**：保留1位小数
- **计算精度**：使用完整精度计算
- **四舍五入**：最终显示时四舍五入

### 性能优化
- **缓存计算**：避免重复计算
- **批量处理**：使用reduce一次性计算
- **实时更新**：数据变化时自动重算

---

**功能版本**：v2.5.0  
**更新内容**：主界面显示米制单位  
**计算方式**：根数 × 长度(mm) ÷ 1000  
**浦东机场总米数**：4,301.6米  
**状态**：已完成并可用 ✅
