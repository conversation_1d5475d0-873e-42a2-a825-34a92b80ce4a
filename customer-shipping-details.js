// 客户发货详情弹窗功能

(function() {
    'use strict';
    
    console.log('📦 加载客户发货详情功能...');
    
    // 等待页面加载完成
    function initCustomerShippingDetails() {
        const shippedCard = document.querySelector('.metric-card.shipped');
        if (!shippedCard) {
            console.log('⏳ 等待已发货量卡片加载...');
            setTimeout(initCustomerShippingDetails, 500);
            return;
        }
        
        // 添加点击事件
        shippedCard.style.cursor = 'pointer';
        shippedCard.title = '点击查看客户发货详情';
        
        shippedCard.addEventListener('click', showCustomerShippingDetails);
        console.log('✅ 已发货量卡片点击事件已添加');
    }
    
    // 显示客户发货详情弹窗
    function showCustomerShippingDetails() {
        console.log('📊 显示客户发货详情...');
        
        if (!window.dataManager) {
            alert('❌ 数据管理器未加载，无法显示详情');
            return;
        }
        
        // 获取客户发货统计数据
        let customerStats = [];
        try {
            if (typeof window.dataManager.calculateCustomerStats === 'function') {
                customerStats = window.dataManager.calculateCustomerStats();
            }
        } catch (error) {
            console.error('获取客户统计数据失败:', error);
        }
        
        // 如果没有客户统计数据，从发货历史计算
        if (customerStats.length === 0 && window.dataManager.shippingHistory) {
            customerStats = calculateCustomerStatsFromHistory();
        }
        
        // 创建弹窗
        createCustomerDetailsModal(customerStats);
    }
    
    // 从发货历史计算客户统计
    function calculateCustomerStatsFromHistory() {
        const customerMap = new Map();
        
        if (!window.dataManager.shippingHistory) {
            return [];
        }
        
        window.dataManager.shippingHistory.forEach(record => {
            const customerName = record.customerName || '未知客户';
            
            if (!customerMap.has(customerName)) {
                customerMap.set(customerName, {
                    customerName: customerName,
                    totalQuantity: 0,
                    totalMeters: 0,
                    orderCount: 0,
                    lastShippingDate: null
                });
            }
            
            const customer = customerMap.get(customerName);
            
            if (record.items && Array.isArray(record.items)) {
                record.items.forEach(item => {
                    customer.totalQuantity += item.quantity || 0;
                    const meters = (item.quantity || 0) * (item.length || 6000) / 1000;
                    customer.totalMeters += meters;
                });
            }
            
            customer.orderCount++;
            
            const shippingDate = new Date(record.date);
            if (!customer.lastShippingDate || shippingDate > customer.lastShippingDate) {
                customer.lastShippingDate = shippingDate;
            }
        });
        
        return Array.from(customerMap.values()).sort((a, b) => b.totalMeters - a.totalMeters);
    }
    
    // 创建客户详情弹窗
    function createCustomerDetailsModal(customerStats) {
        // 移除已存在的弹窗
        const existingModal = document.getElementById('customerDetailsModal');
        if (existingModal) {
            existingModal.remove();
        }
        
        // 计算总计
        const totalMeters = customerStats.reduce((sum, customer) => sum + customer.totalMeters, 0);
        const totalQuantity = customerStats.reduce((sum, customer) => sum + customer.totalQuantity, 0);
        
        // 创建弹窗HTML
        const modalHTML = `
            <div id="customerDetailsModal" class="modal-overlay" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            ">
                <div class="modal-content" style="
                    background: white;
                    border-radius: 12px;
                    padding: 24px;
                    max-width: 800px;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
                    position: relative;
                ">
                    <div class="modal-header" style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 20px;
                        padding-bottom: 16px;
                        border-bottom: 2px solid #e5e7eb;
                    ">
                        <h2 style="
                            margin: 0;
                            color: #1f2937;
                            font-size: 24px;
                            font-weight: 600;
                        ">
                            📦 客户发货详情
                        </h2>
                        <button id="closeCustomerModal" style="
                            background: none;
                            border: none;
                            font-size: 24px;
                            cursor: pointer;
                            color: #6b7280;
                            padding: 4px;
                        ">×</button>
                    </div>
                    
                    <div class="summary-info" style="
                        background: #f3f4f6;
                        padding: 16px;
                        border-radius: 8px;
                        margin-bottom: 20px;
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 16px;
                    ">
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #059669;">${totalMeters.toFixed(1)}</div>
                            <div style="color: #6b7280; font-size: 14px;">总发货量 (米)</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #0369a1;">${totalQuantity.toLocaleString()}</div>
                            <div style="color: #6b7280; font-size: 14px;">总发货数量 (根)</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #7c3aed;">${customerStats.length}</div>
                            <div style="color: #6b7280; font-size: 14px;">客户数量</div>
                        </div>
                    </div>
                    
                    <div class="customer-list" style="
                        max-height: 400px;
                        overflow-y: auto;
                    ">
                        ${generateCustomerList(customerStats)}
                    </div>
                </div>
            </div>
        `;
        
        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // 添加关闭事件
        const modal = document.getElementById('customerDetailsModal');
        const closeBtn = document.getElementById('closeCustomerModal');
        
        closeBtn.addEventListener('click', () => modal.remove());
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
        
        // ESC键关闭
        document.addEventListener('keydown', function escHandler(e) {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', escHandler);
            }
        });
        
        console.log('✅ 客户发货详情弹窗已显示');
    }
    
    // 生成客户列表HTML
    function generateCustomerList(customerStats) {
        if (customerStats.length === 0) {
            return `
                <div style="
                    text-align: center;
                    padding: 40px;
                    color: #6b7280;
                ">
                    <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                    <div style="font-size: 18px;">暂无发货记录</div>
                    <div style="font-size: 14px; margin-top: 8px;">请先添加客户发货数据</div>
                </div>
            `;
        }
        
        return customerStats.map((customer, index) => {
            const percentage = customerStats.length > 0 ? 
                (customer.totalMeters / customerStats.reduce((sum, c) => sum + c.totalMeters, 0) * 100) : 0;
            
            const lastShippingText = customer.lastShippingDate ? 
                customer.lastShippingDate.toLocaleDateString('zh-CN') : '未知';
            
            return `
                <div class="customer-item" style="
                    border: 1px solid #e5e7eb;
                    border-radius: 8px;
                    padding: 16px;
                    margin-bottom: 12px;
                    background: ${index % 2 === 0 ? '#ffffff' : '#f9fafb'};
                    transition: all 0.2s ease;
                " onmouseover="this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'" 
                   onmouseout="this.style.boxShadow='none'">
                    <div style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 12px;
                    ">
                        <h3 style="
                            margin: 0;
                            color: #1f2937;
                            font-size: 18px;
                            font-weight: 600;
                        ">
                            ${customer.customerName}
                        </h3>
                        <span style="
                            background: #dbeafe;
                            color: #1e40af;
                            padding: 4px 8px;
                            border-radius: 4px;
                            font-size: 12px;
                            font-weight: 500;
                        ">
                            ${percentage.toFixed(1)}%
                        </span>
                    </div>
                    
                    <div style="
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                        gap: 12px;
                        margin-bottom: 12px;
                    ">
                        <div>
                            <div style="color: #6b7280; font-size: 12px;">发货量</div>
                            <div style="font-weight: 600; color: #059669;">${customer.totalMeters.toFixed(1)} 米</div>
                        </div>
                        <div>
                            <div style="color: #6b7280; font-size: 12px;">发货数量</div>
                            <div style="font-weight: 600; color: #0369a1;">${customer.totalQuantity.toLocaleString()} 根</div>
                        </div>
                        <div>
                            <div style="color: #6b7280; font-size: 12px;">订单数</div>
                            <div style="font-weight: 600; color: #7c3aed;">${customer.orderCount} 单</div>
                        </div>
                        <div>
                            <div style="color: #6b7280; font-size: 12px;">最后发货</div>
                            <div style="font-weight: 600; color: #dc2626;">${lastShippingText}</div>
                        </div>
                    </div>
                    
                    <div style="
                        background: #e5e7eb;
                        height: 4px;
                        border-radius: 2px;
                        overflow: hidden;
                    ">
                        <div style="
                            background: linear-gradient(90deg, #10b981, #059669);
                            height: 100%;
                            width: ${percentage}%;
                            transition: width 0.3s ease;
                        "></div>
                    </div>
                </div>
            `;
        }).join('');
    }
    
    // 初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initCustomerShippingDetails);
    } else {
        initCustomerShippingDetails();
    }
    
    // 延迟初始化确保所有组件都加载完成
    setTimeout(initCustomerShippingDetails, 1000);
    
})();
