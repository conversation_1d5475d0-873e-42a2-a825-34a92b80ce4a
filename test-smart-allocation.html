<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能分配和拖拽排序功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            color: #1e40af;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            color: #059669;
            margin-top: 0;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: '✅';
            margin-right: 10px;
        }
        
        .demo-container {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-item {
            padding: 15px;
            border-radius: 8px;
        }
        
        .before {
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .after {
            background: #ecfdf5;
            border: 1px solid #a7f3d0;
        }
        
        .before h4 {
            color: #dc2626;
            margin-top: 0;
        }
        
        .after h4 {
            color: #059669;
            margin-top: 0;
        }
        
        .workflow-step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        
        .workflow-step-number {
            background: #3b82f6;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .workflow-step-content {
            flex: 1;
        }
        
        .workflow-step-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .workflow-step-desc {
            font-size: 14px;
            color: #6b7280;
        }
        
        .drag-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .drag-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            cursor: move;
            transition: all 0.2s;
            position: relative;
        }
        
        .drag-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .drag-card::before {
            content: '⋮⋮';
            position: absolute;
            top: 10px;
            left: 10px;
            color: #6b7280;
            font-size: 12px;
            line-height: 1;
        }
        
        .drag-card-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
            margin-left: 20px;
        }
        
        .drag-card-desc {
            font-size: 12px;
            color: #6b7280;
            margin-left: 20px;
        }
        
        .priority-indicator {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            margin-left: 10px;
        }
        
        .priority-high {
            background: #fee2e2;
            color: #dc2626;
        }
        
        .priority-medium {
            background: #fef3c7;
            color: #d97706;
        }
        
        .priority-low {
            background: #dcfce7;
            color: #16a34a;
        }
        
        .allocation-example {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .allocation-title {
            color: #0369a1;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .allocation-flow {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 10px 0;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .allocation-input {
            background: #dbeafe;
            padding: 8px 12px;
            border-radius: 6px;
            font-weight: 500;
            color: #1e40af;
        }
        
        .allocation-arrow {
            color: #3b82f6;
            font-size: 18px;
        }
        
        .allocation-output {
            background: #dcfce7;
            padding: 8px 12px;
            border-radius: 6px;
            font-weight: 500;
            color: #16a34a;
            text-align: center;
            flex: 1;
            min-width: 150px;
        }
        
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .highlight {
            background: #fbbf24;
            color: #1f2937;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 智能分配和拖拽排序功能</h1>
        
        <div class="test-section">
            <h3>🎯 功能概述</h3>
            <p>新增生产功能已全面升级，现在支持智能分配和区域拖拽排序，让生产管理更加智能化和灵活：</p>
            <ul class="feature-list">
                <li><strong>智能分配</strong>：新增生产时可不选择区域，系统自动分配到紧急区域</li>
                <li><strong>拖拽排序</strong>：区域卡片支持拖拽排序，排序代表紧急程度</li>
                <li><strong>优先级管理</strong>：前面的区域优先完成，后面的区域后续填充</li>
                <li><strong>生产日期记录</strong>：新增生产时记录具体的生产日期</li>
                <li><strong>自动更新统计</strong>：智能分配后自动更新未生产规格统计</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔄 智能分配工作流程</h3>
            <p>当用户选择"智能分配到紧急区域"时，系统按以下流程处理：</p>
            
            <div class="workflow-step">
                <div class="workflow-step-number">1</div>
                <div class="workflow-step-content">
                    <div class="workflow-step-title">获取区域优先级</div>
                    <div class="workflow-step-desc">从拖拽排序中获取当前的区域优先级顺序</div>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="workflow-step-number">2</div>
                <div class="workflow-step-content">
                    <div class="workflow-step-title">查找未完成计划</div>
                    <div class="workflow-step-desc">查找该规格在各区域的未完成生产计划</div>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="workflow-step-number">3</div>
                <div class="workflow-step-content">
                    <div class="workflow-step-title">按优先级分配</div>
                    <div class="workflow-step-desc">优先分配给排序靠前的区域，前面完成后再填充后面</div>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="workflow-step-number">4</div>
                <div class="workflow-step-content">
                    <div class="workflow-step-title">更新生产记录</div>
                    <div class="workflow-step-desc">更新各区域的生产数量和状态，记录生产日期</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 智能分配示例</h3>
            <p>假设有以下场景：</p>
            
            <div class="allocation-example">
                <div class="allocation-title">场景：新增 H100-1200mm 规格 100根</div>
                
                <div class="allocation-flow">
                    <div class="allocation-input">输入：100根</div>
                    <div class="allocation-arrow">→</div>
                    <div class="allocation-output">
                        C1区域：50根 (需要50根)<br>
                        C3区域：30根 (需要80根，分配30根)<br>
                        E1区域：20根 (需要60根，分配20根)
                    </div>
                </div>
                
                <p style="margin-top: 15px; font-size: 14px; color: #6b7280;">
                    系统按区域优先级顺序分配：C1区域最紧急，优先满足；C3区域次之，部分满足；E1区域最后，部分满足。
                </p>
            </div>
        </div>

        <div class="test-section">
            <h3>🎨 区域拖拽排序</h3>
            <p>区域卡片现在支持拖拽排序，排序代表紧急程度：</p>
            
            <div class="demo-container">
                <p style="margin-bottom: 15px; color: #6b7280; font-size: 14px;">
                    <i class="fas fa-arrows-alt" style="color: #3b82f6;"></i>
                    可拖拽排序（排序代表紧急程度）
                </p>
                
                <div class="drag-demo">
                    <div class="drag-card">
                        <div class="drag-card-title">
                            C1区域
                            <span class="priority-indicator priority-high">最紧急</span>
                        </div>
                        <div class="drag-card-desc">完成率: 45.2% | 8个规格</div>
                    </div>
                    
                    <div class="drag-card">
                        <div class="drag-card-title">
                            C3区域
                            <span class="priority-indicator priority-medium">较紧急</span>
                        </div>
                        <div class="drag-card-desc">完成率: 62.8% | 5个规格</div>
                    </div>
                    
                    <div class="drag-card">
                        <div class="drag-card-title">
                            E1区域
                            <span class="priority-indicator priority-low">一般</span>
                        </div>
                        <div class="drag-card-desc">完成率: 78.5% | 3个规格</div>
                    </div>
                </div>
                
                <p style="margin-top: 15px; font-size: 14px; color: #6b7280;">
                    拖拽卡片可以改变区域优先级，智能分配时会按新的排序进行分配。
                </p>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 技术实现</h3>
            <p>智能分配和拖拽排序的核心技术实现：</p>
            
            <div class="code-block">
<span class="highlight">// 智能分配核心算法</span>
smartAllocateProduction(spec, totalQuantity) {
    // 1. 获取区域优先级排序
    const areaOrder = this.getAreaPriorityOrder();
    
    // 2. 查找未完成计划并按优先级排序
    const unfinishedPlans = this.data
        .filter(item => item.spec === spec && item.produced < item.planned)
        .sort((a, b) => {
            const aIndex = areaOrder.indexOf(a.area);
            const bIndex = areaOrder.indexOf(b.area);
            return (aIndex === -1 ? 999 : aIndex) - (bIndex === -1 ? 999 : bIndex);
        });
    
    // 3. 按优先级分配数量
    const allocations = [];
    let remainingQuantity = totalQuantity;
    
    for (const plan of unfinishedPlans) {
        if (remainingQuantity <= 0) break;
        const needed = plan.planned - plan.produced;
        const allocated = Math.min(needed, remainingQuantity);
        
        if (allocated > 0) {
            allocations.push({
                spec: spec,
                area: plan.area,
                quantity: allocated
            });
            remainingQuantity -= allocated;
        }
    }
    
    return allocations;
}
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 使用场景</h3>
            <div class="comparison-grid">
                <div class="comparison-item before">
                    <h4>❌ 原来的方式</h4>
                    <ul style="font-size: 14px; color: #7f1d1d;">
                        <li>必须手动选择区域</li>
                        <li>不知道哪个区域更紧急</li>
                        <li>容易分配不均衡</li>
                        <li>需要人工判断优先级</li>
                    </ul>
                </div>
                <div class="comparison-item after">
                    <h4>✅ 现在的方式</h4>
                    <ul style="font-size: 14px; color: #064e3b;">
                        <li>可选择智能分配</li>
                        <li>拖拽排序设置优先级</li>
                        <li>自动均衡分配</li>
                        <li>系统智能判断</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 新增生产流程</h3>
            <p>现在的新增生产流程更加灵活：</p>
            
            <div class="workflow-step">
                <div class="workflow-step-number">1</div>
                <div class="workflow-step-content">
                    <div class="workflow-step-title">填写基本信息</div>
                    <div class="workflow-step-desc">型号、长度、生产根数、生产日期（必填）</div>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="workflow-step-number">2</div>
                <div class="workflow-step-content">
                    <div class="workflow-step-title">选择分配方式</div>
                    <div class="workflow-step-desc">可选择具体区域，或选择"智能分配到紧急区域"</div>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="workflow-step-number">3</div>
                <div class="workflow-step-content">
                    <div class="workflow-step-title">系统处理</div>
                    <div class="workflow-step-desc">根据选择进行智能分配或直接分配到指定区域</div>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="workflow-step-number">4</div>
                <div class="workflow-step-content">
                    <div class="workflow-step-title">更新统计</div>
                    <div class="workflow-step-desc">自动更新各区域统计和未生产规格统计</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>💡 实际应用优势</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div style="padding: 15px; background: #eff6ff; border: 1px solid #93c5fd; border-radius: 8px;">
                    <h4 style="color: #1d4ed8; margin-top: 0;">🎯 生产效率</h4>
                    <ul style="font-size: 14px; color: #1e3a8a;">
                        <li>优先完成紧急区域</li>
                        <li>减少人工判断时间</li>
                        <li>避免分配错误</li>
                        <li>提高整体效率</li>
                    </ul>
                </div>
                <div style="padding: 15px; background: #ecfdf5; border: 1px solid #a7f3d0; border-radius: 8px;">
                    <h4 style="color: #059669; margin-top: 0;">📊 管理便捷</h4>
                    <ul style="font-size: 14px; color: #064e3b;">
                        <li>拖拽调整优先级</li>
                        <li>智能分配建议</li>
                        <li>实时统计更新</li>
                        <li>历史记录追踪</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🚀 未来扩展</h3>
            <p>基于智能分配架构，未来可以扩展更多功能：</p>
            <ul class="feature-list">
                <li><strong>智能推荐</strong>：基于历史数据推荐最优分配方案</li>
                <li><strong>负载均衡</strong>：考虑各区域的生产能力进行均衡分配</li>
                <li><strong>时间优化</strong>：根据交期要求优化分配顺序</li>
                <li><strong>成本考虑</strong>：结合运输成本等因素进行分配</li>
                <li><strong>预测分析</strong>：预测各区域的完成时间</li>
            </ul>
        </div>
    </div>
</body>
</html>
