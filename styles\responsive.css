/* 响应式设计 - 16:9屏幕优化 */

/* 超大屏幕 (2560x1440 及以上) */
@media (min-width: 2560px) {
    :root {
        --text-xs: 0.875rem;
        --text-sm: 1rem;
        --text-base: 1.125rem;
        --text-lg: 1.25rem;
        --text-xl: 1.5rem;
        --text-2xl: 1.75rem;
        --text-3xl: 2.25rem;
        --text-4xl: 2.75rem;
        
        --spacing-xs: 0.375rem;
        --spacing-sm: 0.75rem;
        --spacing-md: 1.25rem;
        --spacing-lg: 2rem;
        --spacing-xl: 2.5rem;
        --spacing-2xl: 4rem;
    }
    
    .main-content {
        padding: var(--spacing-2xl);
    }
    
    .metrics-container {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .charts-container {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .chart-content {
        height: 400px;
    }
    
    .timeline-container {
        grid-template-columns: 3fr 1fr;
    }
}

/* 标准大屏 (1920x1080) - 主要目标 */
@media (min-width: 1920px) and (max-width: 2559px) {
    .metrics-container {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .charts-container {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .timeline-container {
        grid-template-columns: 2fr 1fr;
    }
    
    .header-container {
        padding: 0 var(--spacing-2xl);
    }
}

/* 中等屏幕 (1366x768 - 1919x1079) */
@media (min-width: 1366px) and (max-width: 1919px) {
    .main-content {
        padding: var(--spacing-lg);
    }
    
    .metrics-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .charts-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .timeline-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .header-container {
        padding: 0 var(--spacing-lg);
    }
    
    .logo-section h1 {
        font-size: var(--text-lg);
    }
    
    .chart-content {
        height: 280px;
    }
}

/* 小屏幕 (1024x768 - 1365x767) */
@media (min-width: 1024px) and (max-width: 1365px) {
    .main-content {
        padding: var(--spacing-md);
    }
    
    .metrics-container {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    .charts-container {
        grid-template-columns: 1fr;
    }
    
    .timeline-container {
        grid-template-columns: 1fr;
    }
    
    .header-container {
        padding: 0 var(--spacing-md);
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }
    
    .header-actions {
        gap: var(--spacing-md);
    }
    
    .control-container {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }
    
    .filter-group {
        justify-content: space-between;
    }
    
    .chart-content {
        height: 250px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
}

/* 平板屏幕 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .main-content {
        padding: var(--spacing-md);
        gap: var(--spacing-md);
    }
    
    .metrics-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .metric-card {
        padding: var(--spacing-md);
    }
    
    .charts-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .timeline-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .header-container {
        padding: 0 var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .logo-section {
        justify-content: center;
    }
    
    .logo-section h1 {
        font-size: var(--text-base);
    }
    
    .header-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .control-container {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .filter-group {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }
    
    .checkbox-group {
        justify-content: space-around;
    }
    
    .chart-content {
        height: 220px;
        padding: var(--spacing-md);
    }
    
    .timeline-item {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
        text-align: center;
    }
    
    .timeline-date {
        min-width: auto;
        align-self: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

/* 手机屏幕 (最大767px) */
@media (max-width: 767px) {
    :root {
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.5rem;
        --spacing-2xl: 2rem;
    }
    
    .header {
        padding: var(--spacing-sm) 0;
    }
    
    .header-container {
        padding: 0 var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .logo-section {
        justify-content: center;
    }
    
    .logo-section h1 {
        font-size: var(--text-sm);
        text-align: center;
    }
    
    .header-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
        width: 100%;
    }
    
    .main-content {
        padding: var(--spacing-md);
        gap: var(--spacing-md);
    }
    
    .metrics-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .metric-card {
        padding: var(--spacing-md);
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }
    
    .metric-icon {
        width: 48px;
        height: 48px;
        font-size: var(--text-xl);
        align-self: center;
    }
    
    .progress-ring {
        align-self: center;
    }
    
    .control-container {
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .filter-group {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .checkbox-group {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .charts-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .chart-card {
        margin: 0;
    }
    
    .chart-header {
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
    
    .chart-content {
        height: 200px;
        padding: var(--spacing-md);
    }
    
    .timeline-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .timeline-content,
    .stats-content {
        padding: var(--spacing-md);
    }
    
    .timeline-item {
        flex-direction: column;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) 0;
        text-align: center;
    }
    
    .timeline-date {
        align-self: center;
        min-width: auto;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .stat-item {
        padding: var(--spacing-md);
    }
    
    .contractor-info {
        padding: var(--spacing-md);
    }
    
    .contractor-info p {
        font-size: var(--text-xs);
        line-height: 1.4;
    }
}

/* 横屏手机和小平板 */
@media (max-width: 1023px) and (orientation: landscape) {
    .header {
        padding: var(--spacing-sm) 0;
    }
    
    .main-content {
        gap: var(--spacing-md);
    }
    
    .metrics-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .charts-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .chart-content {
        height: 180px;
    }
}

/* 打印样式 */
@media print {
    .header-actions,
    .chart-actions,
    .refresh-btn {
        display: none !important;
    }

    .main-content {
        padding: 0;
    }

    .metric-card,
    .chart-card,
    .timeline-card,
    .stats-card {
        box-shadow: none;
        border: 1px solid var(--border-medium);
        break-inside: avoid;
    }

    .charts-container {
        grid-template-columns: 1fr;
    }

    .timeline-container {
        grid-template-columns: 1fr;
    }
}

/* 图表全屏模式 */
.chart-expanded {
    overflow: hidden;
}

.chart-card.expanded {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background: var(--bg-card);
    border-radius: 0;
    margin: 0;
}

.chart-card.expanded .chart-content {
    height: calc(100vh - 80px);
    padding: var(--spacing-xl);
}

.chart-card.expanded .chart-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--bg-primary);
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --border-light: #000000;
        --border-medium: #000000;
        --text-secondary: #000000;
        --text-light: #333333;
    }

    .metric-card,
    .chart-card,
    .timeline-card,
    .stats-card {
        border: 2px solid #000000;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .status-dot {
        animation: none;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #111827;
        --bg-secondary: #1f2937;
        --bg-card: #1f2937;
        --bg-dark: #374151;

        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-light: #9ca3af;

        --border-light: #374151;
        --border-medium: #4b5563;

        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
    }

    .header {
        background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    }

    .chart-header,
    .timeline-header,
    .stats-header {
        background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    }

    .timeline-date {
        background: var(--bg-dark);
    }

    .stat-item {
        background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    }
}

/* 特殊屏幕比例适配 */
@media (aspect-ratio: 21/9) {
    .metrics-container {
        grid-template-columns: repeat(4, 1fr);
    }

    .charts-container {
        grid-template-columns: repeat(4, 1fr);
    }

    .timeline-container {
        grid-template-columns: 3fr 1fr;
    }
}

@media (aspect-ratio: 4/3) {
    .metrics-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .charts-container {
        grid-template-columns: 1fr;
    }

    .timeline-container {
        grid-template-columns: 1fr;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .metric-card,
    .chart-card,
    .timeline-card,
    .stats-card {
        transform: none !important;
    }

    .metric-card:hover,
    .chart-card:hover,
    .timeline-card:hover,
    .stats-card:hover {
        transform: none !important;
    }

    .chart-btn,
    .refresh-btn {
        min-height: 44px;
        min-width: 44px;
    }

    .checkbox-label {
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    .select-dropdown {
        min-height: 44px;
    }
}

/* 超宽屏优化 (32:9) */
@media (min-width: 3440px) {
    .main-content {
        max-width: 3440px;
    }

    .metrics-container {
        grid-template-columns: repeat(6, 1fr);
    }

    .charts-container {
        grid-template-columns: repeat(4, 1fr);
    }

    .timeline-container {
        grid-template-columns: 4fr 1fr;
    }
}

/* 垂直屏幕适配 */
@media (orientation: portrait) and (min-width: 768px) {
    .metrics-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .charts-container {
        grid-template-columns: 1fr;
    }

    .timeline-container {
        grid-template-columns: 1fr;
    }

    .chart-content {
        height: 350px;
    }
}
