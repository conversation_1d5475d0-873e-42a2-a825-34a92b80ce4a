// 立即修复发货量为3831米的脚本

(function() {
    'use strict';
    
    console.log('🎯 立即修复发货量为3831米...');
    
    function immediateFixTo3831() {
        // 根据客户统计显示的数据，正确的发货量应该是：
        // 盐城恒逸明: 3,675米 + 测试客户4: 120米 + 测试客户5: 18米 + 测试客户3: 12米 + 测试客户2: 6米 = 3,831米
        const correctShippedMeters = 3831;
        
        console.log(`🔧 开始修复发货量为: ${correctShippedMeters}米`);
        
        // 1. 更新仪表板数据对象
        if (window.dashboard && window.dashboard.data) {
            const oldShipped = window.dashboard.data.shippedMeters;
            const totalProduced = window.dashboard.data.producedMeters || 73664.2;
            
            window.dashboard.data.shippedMeters = correctShippedMeters;
            window.dashboard.data.unshippedMeters = totalProduced - correctShippedMeters;
            
            console.log(`✅ 仪表板数据已更新:`);
            console.log(`  原已发货量: ${oldShipped}米`);
            console.log(`  新已发货量: ${correctShippedMeters}米`);
            console.log(`  新未发货量: ${window.dashboard.data.unshippedMeters.toFixed(1)}米`);
        }
        
        // 2. 直接更新DOM元素 - 已发货量
        const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
        if (shippedElement) {
            const oldText = shippedElement.textContent;
            shippedElement.textContent = correctShippedMeters.toFixed(1);
            
            console.log(`✅ 已发货量DOM更新: ${oldText} -> ${correctShippedMeters.toFixed(1)}`);
            
            // 添加强烈的视觉效果
            shippedElement.style.cssText = `
                background: linear-gradient(45deg, #10b981, #059669) !important;
                color: white !important;
                font-weight: bold !important;
                font-size: 1.2em !important;
                padding: 8px 12px !important;
                border-radius: 8px !important;
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4) !important;
                transform: scale(1.05) !important;
                transition: all 0.5s ease !important;
            `;
            
            // 3秒后恢复部分样式，但保持颜色
            setTimeout(() => {
                shippedElement.style.transform = 'scale(1)';
                shippedElement.style.fontSize = '';
            }, 3000);
            
        } else {
            console.log('❌ 找不到已发货量DOM元素');
        }
        
        // 3. 直接更新DOM元素 - 未发货量
        const unshippedElement = document.querySelector('.metric-card.unshipped .metric-value');
        if (unshippedElement && window.dashboard && window.dashboard.data) {
            const oldText = unshippedElement.textContent;
            const newUnshipped = window.dashboard.data.unshippedMeters.toFixed(1);
            unshippedElement.textContent = newUnshipped;
            
            console.log(`✅ 未发货量DOM更新: ${oldText} -> ${newUnshipped}`);
            
            // 添加视觉效果
            unshippedElement.style.cssText = `
                background: linear-gradient(45deg, #f59e0b, #d97706) !important;
                color: white !important;
                font-weight: bold !important;
                padding: 8px 12px !important;
                border-radius: 8px !important;
                box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4) !important;
                transition: all 0.5s ease !important;
            `;
        }
        
        // 4. 更新客户统计总计显示
        const customerTotalElement = document.getElementById('totalShippedMeters');
        if (customerTotalElement) {
            const oldText = customerTotalElement.textContent;
            customerTotalElement.textContent = `${correctShippedMeters.toFixed(1)} 米`;
            console.log(`✅ 客户统计总计更新: ${oldText} -> ${correctShippedMeters.toFixed(1)} 米`);
        }
        
        // 5. 强制保存数据
        if (window.dataManager) {
            window.dataManager.saveToLocalStorage();
            console.log('✅ 数据已保存到本地存储');
        }
        
        // 6. 创建成功提示覆盖层
        createSuccessOverlay(correctShippedMeters);
        
        // 7. 强制刷新相关组件
        setTimeout(() => {
            try {
                if (window.dashboard) {
                    if (typeof window.dashboard.updateMetrics === 'function') {
                        window.dashboard.updateMetrics();
                    }
                    if (typeof window.dashboard.updateMetricsFromDataManager === 'function') {
                        window.dashboard.updateMetricsFromDataManager();
                    }
                }
                
                if (window.dataManager && typeof window.dataManager.renderCustomerStats === 'function') {
                    window.dataManager.renderCustomerStats();
                }
                
                console.log('✅ 相关组件已刷新');
            } catch (error) {
                console.log('⚠️ 刷新组件时出错:', error);
            }
        }, 1000);
        
        console.log('🎉 发货量修复完成！');
        
        return correctShippedMeters;
    }
    
    function createSuccessOverlay(shippedMeters) {
        // 创建全屏成功提示
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
            animation: fadeIn 0.5s ease;
        `;
        
        const successBox = document.createElement('div');
        successBox.style.cssText = `
            background: white;
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            animation: slideIn 0.5s ease;
        `;
        
        successBox.innerHTML = `
            <div style="font-size: 60px; margin-bottom: 20px;">🎉</div>
            <h2 style="color: #059669; margin-bottom: 20px; font-size: 28px;">发货量修复成功！</h2>
            <div style="font-size: 20px; margin-bottom: 15px;">
                <strong>已发货量已更新为:</strong>
            </div>
            <div style="font-size: 36px; color: #10b981; font-weight: bold; margin-bottom: 20px;">
                ${shippedMeters.toFixed(1)} 米
            </div>
            <div style="font-size: 16px; color: #6b7280; margin-bottom: 25px;">
                包含所有客户的发货统计数据
            </div>
            <button onclick="this.parentElement.parentElement.remove()" 
                    style="background: #10b981; color: white; border: none; padding: 12px 24px; 
                           border-radius: 8px; font-size: 16px; cursor: pointer; font-weight: bold;">
                确定
            </button>
        `;
        
        overlay.appendChild(successBox);
        document.body.appendChild(overlay);
        
        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes slideIn {
                from { transform: translateY(-50px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        // 5秒后自动关闭
        setTimeout(() => {
            if (overlay.parentElement) {
                overlay.style.opacity = '0';
                overlay.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    overlay.remove();
                }, 500);
            }
        }, 5000);
    }
    
    // 添加全局方法
    window.immediateFixTo3831 = immediateFixTo3831;
    
    // 立即执行修复
    setTimeout(() => {
        console.log('🚀 自动执行发货量修复...');
        immediateFixTo3831();
    }, 2000);
    
    console.log('🚀 立即修复3831米脚本已加载');
    console.log('💡 可用命令: immediateFixTo3831()');
    
})();
