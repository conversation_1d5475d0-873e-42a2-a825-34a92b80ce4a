<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原材料数据诊断工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #fafafa;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 15px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            transition: all 0.2s;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn:hover { opacity: 0.9; transform: translateY(-1px); }
        .log-container {
            background: #1f2937;
            color: #e5e7eb;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            margin: 15px 0;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #d1d5db;
            padding: 8px 12px;
            text-align: left;
        }
        .data-table th {
            background: #f3f4f6;
            font-weight: 600;
        }
        .status-good { color: #10b981; font-weight: bold; }
        .status-warning { color: #f59e0b; font-weight: bold; }
        .status-error { color: #ef4444; font-weight: bold; }
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .alert-info { background: #dbeafe; border: 1px solid #93c5fd; color: #1e40af; }
        .alert-warning { background: #fef3c7; border: 1px solid #fbbf24; color: #92400e; }
        .alert-success { background: #d1fae5; border: 1px solid #6ee7b7; color: #065f46; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 原材料数据诊断工具</h1>
        <p>检查和修复原材料采购管理功能的数据问题</p>

        <div class="section">
            <div class="section-title">📊 快速诊断</div>
            <button class="btn btn-primary" onclick="runFullDiagnosis()">🔍 运行完整诊断</button>
            <button class="btn btn-success" onclick="checkDataManager()">📋 检查DataManager</button>
            <button class="btn btn-warning" onclick="checkLocalStorage()">💾 检查本地存储</button>
            <button class="btn btn-danger" onclick="fixMaterialData()">🛠️ 修复数据问题</button>
        </div>

        <div class="section">
            <div class="section-title">📈 数据状态</div>
            <div id="dataStatus">点击"运行完整诊断"开始检查...</div>
        </div>

        <div class="section">
            <div class="section-title">📝 诊断日志</div>
            <div id="logContainer" class="log-container">等待诊断...</div>
            <button class="btn btn-warning" onclick="clearLog()">🗑️ 清空日志</button>
        </div>

        <div class="section">
            <div class="section-title">📋 原材料记录</div>
            <div id="materialRecords">暂无数据</div>
            <button class="btn btn-success" onclick="addTestData()">➕ 添加测试数据</button>
            <button class="btn btn-primary" onclick="openMainSystem()">🏠 打开主系统</button>
        </div>

        <div class="section">
            <div class="section-title">🛠️ 修复工具</div>
            <button class="btn btn-warning" onclick="reinitializeDataManager()">🔄 重新初始化DataManager</button>
            <button class="btn btn-danger" onclick="clearAllMaterialData()">🗑️ 清空所有原材料数据</button>
            <button class="btn btn-success" onclick="forceRefreshMaterialHistory()">🔄 强制刷新原材料历史</button>
        </div>
    </div>

    <script>
        let logContainer;

        function log(message, type = 'info') {
            if (!logContainer) {
                logContainer = document.getElementById('logContainer');
            }
            
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#60a5fa',
                success: '#34d399',
                warning: '#fbbf24',
                error: '#f87171'
            };
            
            const color = colors[type] || colors.info;
            const logEntry = `<div style="color: ${color}; margin: 3px 0;">[${timestamp}] ${message}</div>`;
            logContainer.innerHTML += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            log('日志已清空', 'info');
        }

        function runFullDiagnosis() {
            log('🔍 开始运行完整诊断...', 'info');
            
            // 1. 检查DataManager
            checkDataManager();
            
            // 2. 检查本地存储
            checkLocalStorage();
            
            // 3. 检查主系统连接
            checkMainSystemConnection();
            
            // 4. 更新状态显示
            updateDataStatus();
            
            log('✅ 完整诊断完成', 'success');
        }

        function checkDataManager() {
            log('📋 检查DataManager状态...', 'info');
            
            try {
                // 检查主窗口的DataManager
                if (window.parent && window.parent.dataManager) {
                    const dm = window.parent.dataManager;
                    log(`✅ 主窗口DataManager存在`, 'success');
                    log(`📊 materialPurchases数组长度: ${dm.materialPurchases.length}`, 'info');
                    
                    if (dm.materialPurchases.length > 0) {
                        log(`📋 最新记录: ${JSON.stringify(dm.materialPurchases[dm.materialPurchases.length - 1])}`, 'info');
                    } else {
                        log(`⚠️ materialPurchases数组为空`, 'warning');
                    }
                    
                    // 检查相关方法
                    const methods = ['saveMaterialPurchase', 'loadMaterialHistory', 'renderMaterialHistoryTable'];
                    methods.forEach(method => {
                        if (typeof dm[method] === 'function') {
                            log(`✅ 方法 ${method} 存在`, 'success');
                        } else {
                            log(`❌ 方法 ${method} 不存在`, 'error');
                        }
                    });
                    
                } else {
                    log(`❌ 主窗口DataManager不存在`, 'error');
                }
                
                // 检查全局DataManager
                if (window.dataManager) {
                    log(`✅ 当前窗口DataManager存在`, 'success');
                } else {
                    log(`⚠️ 当前窗口DataManager不存在`, 'warning');
                }
                
            } catch (error) {
                log(`❌ 检查DataManager时出错: ${error.message}`, 'error');
            }
        }

        function checkLocalStorage() {
            log('💾 检查本地存储...', 'info');
            
            try {
                const materialData = localStorage.getItem('materialPurchases');
                
                if (materialData) {
                    const parsed = JSON.parse(materialData);
                    log(`✅ 本地存储中有原材料数据: ${parsed.length} 条记录`, 'success');
                    
                    if (parsed.length > 0) {
                        log(`📋 最新记录: ${JSON.stringify(parsed[parsed.length - 1])}`, 'info');
                    }
                } else {
                    log(`⚠️ 本地存储中无原材料数据`, 'warning');
                }
                
                // 检查其他相关数据
                const productionData = localStorage.getItem('productionData');
                const shippingHistory = localStorage.getItem('shippingHistory');
                
                log(`📊 生产数据: ${productionData ? JSON.parse(productionData).length : 0} 条`, 'info');
                log(`📊 发货历史: ${shippingHistory ? JSON.parse(shippingHistory).length : 0} 条`, 'info');
                
            } catch (error) {
                log(`❌ 检查本地存储时出错: ${error.message}`, 'error');
            }
        }

        function checkMainSystemConnection() {
            log('🔗 检查主系统连接...', 'info');
            
            try {
                if (window.parent && window.parent !== window) {
                    log(`✅ 在主系统框架中运行`, 'success');
                    
                    // 检查主系统的关键元素
                    const materialHistoryTable = window.parent.document.getElementById('materialHistoryTableBody');
                    if (materialHistoryTable) {
                        log(`✅ 找到原材料历史表格`, 'success');
                        log(`📊 表格行数: ${materialHistoryTable.children.length}`, 'info');
                    } else {
                        log(`❌ 未找到原材料历史表格`, 'error');
                    }
                    
                } else {
                    log(`⚠️ 独立运行，非主系统框架`, 'warning');
                }
            } catch (error) {
                log(`❌ 检查主系统连接时出错: ${error.message}`, 'error');
            }
        }

        function updateDataStatus() {
            const statusDiv = document.getElementById('dataStatus');
            const recordsDiv = document.getElementById('materialRecords');
            
            try {
                let statusHtml = '<div class="alert alert-info"><strong>数据状态摘要：</strong></div>';
                
                // 检查DataManager状态
                if (window.parent && window.parent.dataManager) {
                    const dm = window.parent.dataManager;
                    const recordCount = dm.materialPurchases.length;
                    
                    if (recordCount > 0) {
                        statusHtml += `<div class="status-good">✅ DataManager: ${recordCount} 条记录</div>`;
                    } else {
                        statusHtml += `<div class="status-warning">⚠️ DataManager: 无记录</div>`;
                    }
                } else {
                    statusHtml += `<div class="status-error">❌ DataManager: 不存在</div>`;
                }
                
                // 检查本地存储状态
                const localData = localStorage.getItem('materialPurchases');
                if (localData) {
                    const parsed = JSON.parse(localData);
                    if (parsed.length > 0) {
                        statusHtml += `<div class="status-good">✅ 本地存储: ${parsed.length} 条记录</div>`;
                    } else {
                        statusHtml += `<div class="status-warning">⚠️ 本地存储: 无记录</div>`;
                    }
                } else {
                    statusHtml += `<div class="status-error">❌ 本地存储: 无数据</div>`;
                }
                
                statusDiv.innerHTML = statusHtml;
                
                // 更新记录显示
                displayMaterialRecords();
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status-error">❌ 更新状态时出错: ${error.message}</div>`;
            }
        }

        function displayMaterialRecords() {
            const recordsDiv = document.getElementById('materialRecords');
            
            try {
                let records = [];
                
                // 优先从DataManager获取
                if (window.parent && window.parent.dataManager) {
                    records = window.parent.dataManager.materialPurchases || [];
                }
                
                // 如果DataManager没有数据，从本地存储获取
                if (records.length === 0) {
                    const localData = localStorage.getItem('materialPurchases');
                    if (localData) {
                        records = JSON.parse(localData);
                    }
                }
                
                if (records.length === 0) {
                    recordsDiv.innerHTML = '<div class="alert alert-warning">暂无原材料采购记录</div>';
                    return;
                }
                
                let tableHtml = `
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>厂家</th>
                                <th>直径</th>
                                <th>数量(吨)</th>
                                <th>单价</th>
                                <th>总金额</th>
                                <th>批次</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                records.forEach(record => {
                    tableHtml += `
                        <tr>
                            <td>${record.date}</td>
                            <td>${record.supplier}</td>
                            <td>${record.diameter}</td>
                            <td>${record.quantity}</td>
                            <td>${record.price || '-'}</td>
                            <td>${record.totalAmount || '-'}</td>
                            <td>${record.batch || '-'}</td>
                        </tr>
                    `;
                });
                
                tableHtml += '</tbody></table>';
                recordsDiv.innerHTML = tableHtml;
                
                log(`📋 显示了 ${records.length} 条原材料记录`, 'success');
                
            } catch (error) {
                recordsDiv.innerHTML = `<div class="alert alert-error">显示记录时出错: ${error.message}</div>`;
                log(`❌ 显示记录时出错: ${error.message}`, 'error');
            }
        }

        function addTestData() {
            log('➕ 添加测试数据...', 'info');
            
            try {
                const testRecord = {
                    id: Date.now() + Math.random(),
                    date: new Date().toISOString().split('T')[0],
                    quantity: 10.5,
                    diameter: 'Φ12',
                    supplier: '测试厂家',
                    price: 4200,
                    totalAmount: 10.5 * 4200,
                    batch: 'TEST001',
                    remarks: '测试数据',
                    timestamp: new Date().toISOString()
                };
                
                // 添加到DataManager
                if (window.parent && window.parent.dataManager) {
                    window.parent.dataManager.materialPurchases.push(testRecord);
                    window.parent.dataManager.saveToLocalStorage();
                    log('✅ 测试数据已添加到DataManager', 'success');
                } else {
                    // 直接添加到本地存储
                    const existing = JSON.parse(localStorage.getItem('materialPurchases') || '[]');
                    existing.push(testRecord);
                    localStorage.setItem('materialPurchases', JSON.stringify(existing));
                    log('✅ 测试数据已添加到本地存储', 'success');
                }
                
                // 刷新显示
                updateDataStatus();
                
            } catch (error) {
                log(`❌ 添加测试数据时出错: ${error.message}`, 'error');
            }
        }

        function fixMaterialData() {
            log('🛠️ 开始修复原材料数据问题...', 'info');
            
            try {
                // 1. 检查并修复DataManager
                if (window.parent && window.parent.dataManager) {
                    const dm = window.parent.dataManager;
                    
                    // 确保materialPurchases数组存在
                    if (!dm.materialPurchases) {
                        dm.materialPurchases = [];
                        log('🔧 修复：初始化materialPurchases数组', 'warning');
                    }
                    
                    // 从本地存储加载数据
                    const localData = localStorage.getItem('materialPurchases');
                    if (localData && dm.materialPurchases.length === 0) {
                        dm.materialPurchases = JSON.parse(localData);
                        log(`🔧 修复：从本地存储加载了 ${dm.materialPurchases.length} 条记录`, 'warning');
                    }
                    
                    // 强制刷新原材料历史显示
                    if (typeof dm.loadMaterialHistory === 'function') {
                        dm.loadMaterialHistory();
                        log('🔧 修复：强制刷新原材料历史显示', 'warning');
                    }
                }
                
                // 2. 刷新显示
                updateDataStatus();
                
                log('✅ 数据修复完成', 'success');
                
            } catch (error) {
                log(`❌ 修复数据时出错: ${error.message}`, 'error');
            }
        }

        function reinitializeDataManager() {
            log('🔄 重新初始化DataManager...', 'info');
            
            try {
                if (window.parent && window.parent.dataManager) {
                    const dm = window.parent.dataManager;
                    
                    // 重新加载数据
                    dm.loadFromLocalStorage();
                    
                    // 强制更新显示
                    if (typeof dm.loadMaterialHistory === 'function') {
                        dm.loadMaterialHistory();
                    }
                    
                    log('✅ DataManager重新初始化完成', 'success');
                    updateDataStatus();
                } else {
                    log('❌ 无法访问DataManager', 'error');
                }
            } catch (error) {
                log(`❌ 重新初始化时出错: ${error.message}`, 'error');
            }
        }

        function clearAllMaterialData() {
            if (!confirm('确定要清空所有原材料数据吗？此操作不可恢复！')) {
                return;
            }
            
            log('🗑️ 清空所有原材料数据...', 'warning');
            
            try {
                // 清空DataManager
                if (window.parent && window.parent.dataManager) {
                    window.parent.dataManager.materialPurchases = [];
                    window.parent.dataManager.saveToLocalStorage();
                }
                
                // 清空本地存储
                localStorage.removeItem('materialPurchases');
                
                log('✅ 所有原材料数据已清空', 'success');
                updateDataStatus();
                
            } catch (error) {
                log(`❌ 清空数据时出错: ${error.message}`, 'error');
            }
        }

        function forceRefreshMaterialHistory() {
            log('🔄 强制刷新原材料历史...', 'info');
            
            try {
                if (window.parent && window.parent.dataManager) {
                    const dm = window.parent.dataManager;
                    
                    // 强制刷新
                    if (typeof dm.loadMaterialHistory === 'function') {
                        dm.loadMaterialHistory();
                        log('✅ 原材料历史已强制刷新', 'success');
                    } else {
                        log('❌ loadMaterialHistory方法不存在', 'error');
                    }
                } else {
                    log('❌ 无法访问DataManager', 'error');
                }
            } catch (error) {
                log(`❌ 强制刷新时出错: ${error.message}`, 'error');
            }
        }

        function openMainSystem() {
            window.open('index.html', '_blank');
        }

        // 页面加载时自动运行诊断
        window.addEventListener('DOMContentLoaded', function() {
            log('🚀 原材料数据诊断工具已加载', 'info');
            setTimeout(runFullDiagnosis, 500);
        });
    </script>
</body>
</html>
