/* 浦东机场T3桁架钢筋生产推进管理系统 - 主样式文件 */
/* 16:9 屏幕优化版本 */

/* CSS 变量定义 */
:root {
    /* 主色调 - 工业风格 */
    --primary-blue: #1e3a8a;
    --primary-orange: #f97316;
    --accent-blue: #3b82f6;
    --accent-green: #10b981;
    
    /* 背景色 */
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-dark: #1f2937;
    --bg-card: #ffffff;
    
    /* 文字色 */
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --text-white: #ffffff;
    
    /* 边框和阴影 */
    --border-light: #e5e7eb;
    --border-medium: #d1d5db;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 字体大小 */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    
    /* 圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    
    /* 动画 */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* 基础重置和全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 
                 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    min-height: 100vh;
    overflow-x: hidden;
}

/* 顶部导航栏 */
.header {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--accent-blue) 100%);
    color: var(--text-white);
    padding: var(--spacing-md) 0;
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1920px;
    margin: 0 auto;
    padding: 0 var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-lg);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-section i {
    font-size: var(--text-3xl);
    color: var(--primary-orange);
}

.logo-section h1 {
    font-size: var(--text-xl);
    font-weight: 600;
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--text-sm);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--accent-green);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.refresh-btn {
    background: var(--primary-orange);
    color: var(--text-white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-normal);
}

.refresh-btn:hover {
    background: #ea580c;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.refresh-btn i {
    transition: transform var(--transition-normal);
}

.refresh-btn:hover i {
    transform: rotate(180deg);
}

/* 云端同步按钮 */
.cloud-sync-btn {
    background: linear-gradient(135deg, var(--accent-green), #059669);
    color: var(--text-white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-normal);
    margin-left: var(--spacing-md);
}

.cloud-sync-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.cloud-sync-btn i {
    font-size: 16px;
}

/* 连接状态显示 */
.connection-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    min-width: 80px;
    justify-content: center;
    transition: all 0.3s ease;
}

.connection-status.success {
    background: rgba(16, 185, 129, 0.2);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.connection-status.warning {
    background: rgba(245, 158, 11, 0.2);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.connection-status.error {
    background: rgba(239, 68, 68, 0.2);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.connection-status.info {
    background: rgba(59, 130, 246, 0.2);
    color: #2563eb;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.connection-status::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.last-update {
    font-size: var(--text-xs);
    opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
    max-width: 1920px;
    margin: 0 auto;
    padding: var(--spacing-xl);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* 关键指标卡片区 */
.metrics-section {
    width: 100%;
}

.metrics-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.metrics-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.metric-card {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-orange));
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.metric-card.total::before {
    background: linear-gradient(90deg, var(--primary-blue), var(--accent-blue));
}

.metric-card.produced::before {
    background: linear-gradient(90deg, var(--accent-green), #059669);
}

.metric-card.pending::before {
    background: linear-gradient(90deg, var(--primary-orange), #ea580c);
}

.metric-card.efficiency::before {
    background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.metric-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.metric-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-2xl);
    color: var(--text-white);
}

.metric-card.total .metric-icon {
    background: linear-gradient(135deg, var(--primary-blue), var(--accent-blue));
}

.metric-card.produced .metric-icon {
    background: linear-gradient(135deg, var(--accent-green), #059669);
}

.metric-card.pending .metric-icon {
    background: linear-gradient(135deg, var(--primary-orange), #ea580c);
}

.metric-card.efficiency .metric-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

/* 新增卡片样式 - 只保留顶部彩色条和图标颜色 */
.metric-card.shipped::before {
    background: linear-gradient(90deg, #059669, #10b981);
}

.metric-card.shipped .metric-icon {
    background: linear-gradient(135deg, #047857, #059669);
}

.metric-card.unshipped::before {
    background: linear-gradient(90deg, #d97706, #f59e0b);
}

.metric-card.unshipped .metric-icon {
    background: linear-gradient(135deg, #b45309, #d97706);
}

.metric-card.material::before {
    background: linear-gradient(90deg, #7c3aed, #8b5cf6);
}

.metric-card.material .metric-icon {
    background: linear-gradient(135deg, #6d28d9, #7c3aed);
}

.metric-card.inventory::before {
    background: linear-gradient(90deg, #0891b2, #06b6d4);
}

.metric-card.inventory .metric-icon {
    background: linear-gradient(135deg, #0e7490, #0891b2);
}

.inventory-detail {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.metric-content {
    flex: 1;
}

.metric-content h3 {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.metric-value {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
}

.metric-unit {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

.metric-subtitle {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--text-sm);
    margin-top: var(--spacing-xs);
}

.metric-trend.up {
    color: var(--accent-green);
}

.metric-trend.down {
    color: #ef4444;
}

/* 进度环 */
.progress-ring {
    position: relative;
    width: 60px;
    height: 60px;
}

.progress-ring-svg {
    transform: rotate(-90deg);
}

.progress-ring-circle-bg {
    fill: none;
    stroke: var(--border-light);
    stroke-width: 4;
}

.progress-ring-circle {
    fill: none;
    stroke: var(--accent-green);
    stroke-width: 4;
    stroke-linecap: round;
    transition: stroke-dashoffset var(--transition-slow);
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: var(--text-xs);
    font-weight: 600;
    color: var(--text-primary);
}

/* 产量统计面板 */
.production-stats-panel {
    width: 100%;
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.stats-container {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}

.stats-card-mini {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    flex: 1;
    min-width: 180px;
}

.stats-card-mini:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.stats-icon.daily {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stats-icon.monthly {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.stats-icon.quarterly {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stats-icon.yearly {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stats-content {
    flex: 1;
}

.stats-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary);
    line-height: 1.2;
}

.stats-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-top: 2px;
}

.stats-refresh {
    display: flex;
    align-items: center;
}

.stats-refresh-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--primary-blue);
    color: white;
    border-radius: var(--radius-full);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
}

.stats-refresh-btn:hover {
    background: var(--primary-blue-dark);
    transform: rotate(180deg);
}

.stats-refresh-btn:active {
    transform: rotate(180deg) scale(0.95);
}

/* 新增客户卡片模态框样式 */
.customer-selection-section {
    margin-bottom: var(--spacing-lg);
}

.section-description {
    color: var(--text-secondary);
    font-size: var(--text-sm);
    margin-bottom: var(--spacing-md);
}

.customer-search-box {
    position: relative;
    margin-bottom: var(--spacing-md);
}

.customer-search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: var(--text-sm);
}

.customer-search-box input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 36px;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    transition: border-color var(--transition-normal);
}

.customer-search-box input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.customer-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--bg-white);
}

.customer-list-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    cursor: pointer;
    transition: background-color var(--transition-normal);
}

.customer-list-item:last-child {
    border-bottom: none;
}

.customer-list-item:hover {
    background-color: var(--bg-light);
}

.customer-list-item.selected {
    background-color: rgba(59, 130, 246, 0.1);
    border-color: var(--primary-blue);
}

.customer-item-info {
    flex: 1;
}

.customer-item-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.customer-item-stats {
    font-size: var(--text-xs);
    color: var(--text-secondary);
}

.customer-item-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.selected-customer-section {
    border-top: 1px solid var(--border-light);
    padding-top: var(--spacing-lg);
}

.selected-customer-info {
    background: var(--bg-light);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.customer-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.customer-actions .checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: var(--text-sm);
}

.customer-actions input[type="checkbox"] {
    margin: 0;
}

.customer-badge {
    display: inline-block;
    background: var(--primary-blue);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-container {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .stats-card-mini {
        width: 100%;
        min-width: auto;
    }

    .stats-refresh {
        width: 100%;
        justify-content: center;
    }

    .customer-list {
        max-height: 200px;
    }
}

.filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.filter-group label {
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
}

.checkbox-group {
    display: flex;
    gap: var(--spacing-md);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--text-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: color var(--transition-fast);
}

.checkbox-label:hover {
    color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-blue);
}

.select-dropdown {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    color: var(--text-primary);
    background: var(--bg-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 120px;
}

.select-dropdown:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* 图表展示区域 */
.charts-section {
    width: 100%;
}

.charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-lg);
}

.chart-card {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.chart-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.chart-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.chart-header h3 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.chart-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.chart-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    border: 1px solid var(--border-light);
}

.chart-btn:hover {
    background: var(--primary-blue);
    color: var(--text-white);
    transform: translateY(-1px);
}

.chart-content {
    padding: var(--spacing-lg);
    height: 350px;
    min-height: 300px;
    position: relative;
}

/* 规格型号图表需要更多空间 */
.chart-content:has(#specChart) {
    height: 400px;
    min-height: 350px;
}

/* 响应式图表高度 */
@media (max-width: 768px) {
    .chart-content {
        height: 280px;
        min-height: 250px;
    }

    .chart-content:has(#specChart) {
        height: 320px;
        min-height: 280px;
    }
}

.chart-content canvas {
    width: 100% !important;
    height: 100% !important;
}

/* 区域统计区域 */
.area-stats-section {
    width: 100%;
    margin: 2rem 0;
}

.area-stats-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

.area-stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
}

.area-stats-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--text-lg);
    font-weight: 600;
}

.stats-summary {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--text-secondary);
    font-size: var(--text-sm);
}

.refresh-areas-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--primary-blue);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: var(--text-sm);
    transition: all var(--transition-fast);
}

.refresh-areas-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.area-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.area-card {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.area-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.area-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.area-name {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

/* 可编辑区域名称样式 */
.editable-area-name {
    position: relative;
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 2px 4px;
    border: 1px solid transparent;
}

.editable-area-name:hover {
    background-color: rgba(59, 130, 246, 0.1) !important;
    border-color: #3b82f6 !important;
    cursor: pointer;
}

.editable-area-name::after {
    content: '✏️';
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.editable-area-name:hover::after {
    opacity: 0.7;
}

.area-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: var(--text-xs);
    font-weight: 500;
}

.area-status.active {
    background: var(--accent-green);
    color: white;
}

.area-status.completed {
    background: var(--primary-blue);
    color: white;
}

.area-status.pending {
    background: var(--primary-orange);
    color: white;
}

.area-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.area-metric {
    text-align: center;
}

.area-metric-value {
    font-size: var(--text-xl);
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.area-metric-value.total {
    color: var(--primary-blue);
}

.area-metric-value.produced {
    color: var(--accent-green);
}

.area-metric-value.pending {
    color: var(--primary-orange);
}

.area-metric-label {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    font-weight: 500;
}

.area-progress {
    margin-top: var(--spacing-md);
}

.area-progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    overflow: hidden;
}

.area-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-green), var(--primary-blue));
    border-radius: 4px;
    transition: width var(--transition-fast);
}

.area-progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.area-card-actions {
    display: flex;
    gap: 0.25rem;
    margin-top: var(--spacing-md);
    flex-wrap: wrap;
}

.area-action-btn {
    flex: 1;
    padding: 0.5rem 0.25rem;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: var(--text-xs);
    font-weight: 500;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    min-height: 32px;
}

.area-action-btn.primary {
    background: var(--primary-blue);
    color: white;
}

.area-action-btn.secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.area-action-btn.danger {
    background: #ef4444;
    color: white;
}

.area-action-btn:hover {
    transform: translateY(-1px);
    opacity: 0.9;
}

.area-action-btn.danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.timeline-card,
.stats-card {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.timeline-header,
.stats-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.timeline-header h3,
.stats-header h3 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.timeline-content {
    padding: var(--spacing-lg);
}

.timeline-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-light);
    transition: all var(--transition-fast);
}

.timeline-item:last-child {
    border-bottom: none;
}

.timeline-item:hover {
    background: rgba(30, 58, 138, 0.02);
    border-radius: var(--radius-md);
}

.timeline-date {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-primary);
    min-width: 80px;
    text-align: center;
    padding: var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
}

.timeline-info {
    flex: 1;
}

.timeline-info h4 {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.timeline-info p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
}

.timeline-status {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.timeline-item.completed .timeline-status {
    color: var(--accent-green);
}

.timeline-item.active .timeline-status {
    color: var(--primary-orange);
}

.timeline-item.pending .timeline-status {
    color: var(--text-light);
}

.timeline-item.active .timeline-date {
    background: linear-gradient(135deg, var(--primary-orange), #ea580c);
    color: var(--text-white);
}

.timeline-item.completed .timeline-date {
    background: linear-gradient(135deg, var(--accent-green), #059669);
    color: var(--text-white);
}

/* 统计信息卡片 */
.stats-content {
    padding: var(--spacing-lg);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transition: all var(--transition-normal);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-number {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--primary-blue);
    line-height: 1.2;
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

.contractor-info {
    padding: var(--spacing-md);
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--primary-blue);
}

.contractor-info p {
    font-size: var(--text-sm);
    color: var(--text-primary);
    margin: 0;
    line-height: 1.5;
}

/* 客户发货统计样式 */
.stats-header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.customer-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.customer-stat-card {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: 200px;
}

.customer-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-blue);
}

.customer-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--accent-blue));
}

.customer-stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.customer-name {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.customer-rank {
    background: var(--primary-blue);
    color: var(--text-white);
    font-size: var(--text-xs);
    font-weight: 600;
    padding: 2px 8px;
    border-radius: var(--radius-full);
    min-width: 24px;
    text-align: center;
}

.customer-stat-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.customer-metric {
    text-align: center;
}

.customer-metric-value {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--primary-blue);
    line-height: 1.2;
}

.customer-metric-label {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    margin-top: 2px;
}

.customer-stat-progress {
    margin-top: var(--spacing-sm);
}

.customer-progress-bar {
    width: 100%;
    height: 6px;
    background: var(--border-light);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.customer-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-green), var(--primary-orange));
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
}

.customer-progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-xs);
    font-size: var(--text-xs);
    color: var(--text-secondary);
}

.customer-stats-summary {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    padding: var(--spacing-md);
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--primary-blue);
}

.summary-item {
    text-align: center;
}

.summary-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.summary-value {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--primary-blue);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--text-white);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-dark);
    color: var(--text-white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: 1000;
}

.tooltip:hover::before {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-4px);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* 选择文本样式 */
::selection {
    background: rgba(30, 58, 138, 0.2);
    color: var(--text-primary);
}

/* 焦点样式 */
button:focus,
input:focus,
select:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* 禁用状态 */
.disabled,
button:disabled,
input:disabled,
select:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 成功、警告、错误状态 */
.success {
    color: var(--accent-green);
}

.warning {
    color: var(--primary-orange);
}

.error {
    color: #ef4444;
}

/* 隐藏元素 */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 数据管理区域 */
.data-management-section {
    width: 100%;
}

.management-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* 工具栏 */
.toolbar-card {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.toolbar-header {
    padding: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid var(--border-light);
}

.toolbar-header h3 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.toolbar-actions {
    display: flex;
    gap: var(--spacing-md);
}

.action-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-normal);
    min-width: 100px;
    justify-content: center;
}

.action-btn.primary {
    background: var(--primary-blue);
    color: var(--text-white);
}

.action-btn.primary:hover {
    background: #1e40af;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.action-btn.secondary {
    background: var(--text-secondary);
    color: var(--text-white);
}

.action-btn.secondary:hover {
    background: #4b5563;
    transform: translateY(-1px);
}

.action-btn.success {
    background: var(--accent-green);
    color: var(--text-white);
}

.action-btn.success:hover {
    background: #059669;
    transform: translateY(-1px);
}

.action-btn.warning {
    background: var(--primary-orange);
    color: var(--text-white);
}

.action-btn.warning:hover {
    background: #ea580c;
    transform: translateY(-1px);
}

.action-btn.info {
    background: #3b82f6;
    color: var(--text-white);
}

.action-btn.info:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

.action-btn.danger {
    background: #ef4444;
    color: var(--text-white);
}

.action-btn.danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

/* 数据表格 */
.data-table-card {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.table-header {
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid var(--border-light);
}

.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-lg);
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-box i {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-box input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 40px;
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    transition: all var(--transition-fast);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.table-filters {
    display: flex;
    gap: var(--spacing-md);
}

.filter-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    background: var(--bg-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--text-sm);
}

.data-table th,
.data-table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-light);
}

.data-table th {
    background: var(--bg-primary);
    font-weight: 600;
    color: var(--text-primary);
    cursor: pointer;
    user-select: none;
    transition: background-color var(--transition-fast);
}

.data-table th:hover {
    background: #e2e8f0;
}

.data-table th i {
    margin-left: var(--spacing-xs);
    opacity: 0.5;
    transition: opacity var(--transition-fast);
}

.data-table th.sorted i {
    opacity: 1;
    color: var(--primary-blue);
}

.data-table tbody tr {
    transition: background-color var(--transition-fast);
}

.data-table tbody tr:hover {
    background: rgba(30, 58, 138, 0.02);
}

.data-table tbody tr.selected {
    background: rgba(30, 58, 138, 0.05);
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    font-weight: 500;
    text-align: center;
    min-width: 60px;
}

.status-badge.planned {
    background: rgba(107, 114, 128, 0.1);
    color: var(--text-secondary);
}

.status-badge.producing {
    background: rgba(249, 115, 22, 0.1);
    color: var(--primary-orange);
}

.status-badge.completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--accent-green);
}

.status-badge.shipped {
    background: rgba(30, 58, 138, 0.1);
    color: var(--primary-blue);
}

.table-footer {
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-top: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-info {
    display: flex;
    gap: var(--spacing-lg);
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.pagination {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.page-btn {
    width: 32px;
    height: 32px;
    border: 1px solid var(--border-medium);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.page-btn:hover:not(:disabled) {
    background: var(--primary-blue);
    color: var(--text-white);
    border-color: var(--primary-blue);
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

/* 操作按钮 */
.operation-btns {
    display: flex;
    gap: var(--spacing-sm);
}

.op-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 60px;
}

.op-btn.edit {
    background: var(--text-secondary);
    color: var(--text-white);
}

.op-btn.edit:hover {
    background: #4b5563;
}

.op-btn.ship {
    background: var(--primary-blue);
    color: var(--text-white);
}

.op-btn.ship:hover {
    background: #1e40af;
}

.op-btn.delete {
    background: #ef4444;
    color: var(--text-white);
}

.op-btn.delete:hover {
    background: #dc2626;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

.modal.active {
    display: flex;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal-content.modal-large {
    max-width: 900px;
    width: 95%;
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.modal-header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.modal-header h3 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    color: var(--text-secondary);
}

.modal-close:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.modal-body {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    background: var(--bg-primary);
}

/* 表单样式 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* 搜索功能样式 */
.form-group input[type="text"]#specSearchInput {
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.form-group input[type="text"]#specSearchInput:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input[type="text"]#specSearchInput::placeholder {
    color: #9ca3af;
    font-style: italic;
}

.btn.btn-secondary {
    background: #6b7280;
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn.btn-secondary:hover {
    background: #4b5563;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn.btn-secondary:active {
    transform: translateY(0);
}

/* 搜索结果高亮 */
.shipping-item-row[style*="display: none"] {
    opacity: 0.3;
}

.spec-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

.spec-areas {
    font-size: 12px;
    color: #6b7280;
    font-style: italic;
}

/* 发货购物车样式 */
.shipping-cart-section {
    margin-top: 30px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: #f9fafb;
    overflow: hidden;
}

.shipping-cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #f3f4f6;
    border-bottom: 1px solid #e5e7eb;
}

.shipping-cart-header h4 {
    margin: 0;
    color: #1f2937;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.shipping-cart-header h4 i {
    color: #059669;
}

.shipping-cart-container {
    padding: 20px;
    min-height: 120px;
}

.empty-cart {
    text-align: center;
    color: #6b7280;
    padding: 40px 20px;
}

.empty-cart i {
    font-size: 48px;
    color: #d1d5db;
    margin-bottom: 16px;
    display: block;
}

.empty-cart p {
    margin: 0;
    font-size: 14px;
}

.cart-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.cart-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 16px;
    align-items: center;
    padding: 16px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.cart-item:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cart-item-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.cart-spec {
    font-weight: 600;
    color: #1f2937;
    font-size: 14px;
}

.cart-areas {
    font-size: 12px;
    color: #6b7280;
    font-style: italic;
}

.cart-quantity {
    display: flex;
    align-items: center;
    gap: 8px;
}

.cart-quantity-input {
    width: 80px;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
}

.cart-quantity-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.quantity-unit {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
}

.cart-meters {
    font-weight: 500;
    color: #059669;
    text-align: right;
}

.cart-actions {
    display: flex;
    gap: 8px;
}

.action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.btn.btn-outline {
    background: transparent;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.btn.btn-outline:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
    color: #374151;
}

.btn.btn-success {
    background: #059669;
    color: white;
}

.btn.btn-success:hover {
    background: #047857;
}

.btn.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .cart-item {
        grid-template-columns: 1fr;
        gap: 12px;
        text-align: center;
    }

    .action-buttons {
        justify-content: center;
    }

    .shipping-cart-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
}

/* 发货历史样式 */
.shipping-history-filters {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.shipping-history-list {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 20px;
}

.shipping-record-card {
    border-bottom: 1px solid #e5e7eb;
    padding: 20px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.shipping-record-card:hover {
    background: #f9fafb;
}

.shipping-record-card:last-child {
    border-bottom: none;
}

.shipping-record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.shipping-record-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.shipping-record-date {
    font-size: 14px;
    color: #6b7280;
    background: #f3f4f6;
    padding: 4px 8px;
    border-radius: 4px;
}

.shipping-record-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 12px;
}

.shipping-record-field {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.shipping-record-field .label {
    color: #6b7280;
    font-weight: 500;
}

.shipping-record-field .value {
    color: #1f2937;
}

.shipping-record-stats {
    display: flex;
    gap: 20px;
    padding: 12px 0;
    border-top: 1px solid #f3f4f6;
}

.shipping-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.shipping-stat .label {
    font-size: 12px;
    color: #6b7280;
}

.shipping-stat .value {
    font-size: 14px;
    font-weight: 600;
    color: #059669;
}

.shipping-history-summary {
    background: #ecfdf5;
    border: 1px solid #059669;
    border-radius: 8px;
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.shipping-history-summary .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.shipping-history-summary .summary-item span:first-child {
    color: #047857;
    font-weight: 500;
}

.shipping-history-summary .summary-item span:last-child {
    color: #059669;
    font-weight: 600;
}

/* 发货详情样式 */
.shipping-detail-info {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.shipping-detail-items {
    margin-bottom: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e5e7eb;
}

.section-header h4 {
    margin: 0;
    color: #1f2937;
    font-size: 16px;
    font-weight: 600;
}

.shipping-items-table-container {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 15px;
}

.shipping-items-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.shipping-items-table th,
.shipping-items-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.shipping-items-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.shipping-items-table tr:hover {
    background: #f9fafb;
}

.shipping-items-table tr:last-child td {
    border-bottom: none;
}

.shipping-items-table .number-cell {
    text-align: right;
    font-weight: 500;
}

.edit-mode-column {
    width: 120px;
}

.shipping-detail-summary {
    background: #ecfdf5;
    border: 1px solid #059669;
    border-radius: 8px;
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.shipping-detail-summary .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.shipping-detail-summary .summary-item span:first-child {
    color: #047857;
    font-weight: 500;
}

.shipping-detail-summary .summary-item span:last-child {
    color: #059669;
    font-weight: 600;
}

/* 编辑模式样式 */
.edit-mode input,
.edit-mode textarea,
.edit-mode select {
    border: 1px solid #3b82f6;
    background: white;
}

.edit-mode input:read-only,
.edit-mode textarea:read-only {
    border: 1px solid #d1d5db;
    background: #f9fafb;
}

.item-quantity-input {
    width: 80px;
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    text-align: center;
    font-size: 14px;
}

.item-quantity-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 空状态样式 */
.empty-shipping-history {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-shipping-history i {
    font-size: 48px;
    color: #d1d5db;
    margin-bottom: 16px;
    display: block;
}

.empty-shipping-history p {
    margin: 0;
    font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .shipping-record-info {
        grid-template-columns: 1fr;
    }

    .shipping-record-stats {
        flex-wrap: wrap;
        gap: 10px;
    }

    .shipping-history-summary {
        grid-template-columns: 1fr;
    }

    .shipping-detail-summary {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-group input[readonly] {
    background-color: var(--bg-primary);
    color: var(--text-secondary);
    cursor: not-allowed;
}

.form-group select:disabled {
    background-color: var(--bg-primary);
    color: var(--text-light);
    cursor: not-allowed;
    opacity: 0.6;
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    min-width: 80px;
}

.btn.primary {
    background: var(--primary-blue);
    color: var(--text-white);
}

.btn.primary:hover {
    background: #1e40af;
    transform: translateY(-1px);
}

.btn.secondary {
    background: var(--text-secondary);
    color: var(--text-white);
}

.btn.secondary:hover {
    background: #4b5563;
}

.btn.success {
    background: var(--accent-green);
    color: var(--text-white);
}

.btn.success:hover {
    background: #059669;
    transform: translateY(-1px);
}

.btn.info {
    background: #3b82f6;
    color: var(--text-white);
}

.btn.info:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

/* 发货信息 */
.shipping-info {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.shipping-info h4 {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-md) 0;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-light);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item .label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.info-item .value {
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--text-primary);
}

/* 批量操作 */
.batch-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.batch-option {
    padding: var(--spacing-lg);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.batch-option:hover {
    border-color: var(--primary-blue);
    background: rgba(30, 58, 138, 0.02);
}

.batch-option.selected {
    border-color: var(--primary-blue);
    background: rgba(30, 58, 138, 0.05);
}

.batch-option i {
    font-size: var(--text-xl);
    color: var(--primary-blue);
}

.batch-option span {
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--text-primary);
}

/* 大型模态框 */
.modal.large .modal-content {
    max-width: 900px;
    max-height: 80vh;
}

/* 操作日志 */
.logs-controls {
    margin-bottom: var(--spacing-lg);
}

.logs-filters {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    flex-wrap: wrap;
}

.logs-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
}

.logs-list {
    padding: var(--spacing-md);
}

.log-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    transition: background-color var(--transition-fast);
}

.log-item:last-child {
    border-bottom: none;
}

.log-item:hover {
    background: var(--bg-primary);
}

.log-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-sm);
    color: var(--text-white);
    flex-shrink: 0;
}

.log-icon.create {
    background: var(--accent-green);
}

.log-icon.update {
    background: var(--primary-blue);
}

.log-icon.delete {
    background: #ef4444;
}

.log-icon.shipping {
    background: var(--primary-orange);
}

.log-icon.batch {
    background: #8b5cf6;
}

.log-content {
    flex: 1;
}

.log-title {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.log-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-xs) 0;
}

.log-meta {
    font-size: var(--text-xs);
    color: var(--text-light);
    display: flex;
    gap: var(--spacing-md);
}

.log-time {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.log-user {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.empty-logs {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.empty-logs i {
    font-size: var(--text-4xl);
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* 批量生产模式样式 */
.production-mode {
    width: 100%;
}

.batch-header {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: #f8fafc;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.batch-table-container {
    margin: var(--spacing-lg) 0;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.batch-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.batch-table th {
    background: #f8fafc;
    padding: var(--spacing-md);
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-light);
}

.batch-table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.batch-table tr:last-child td {
    border-bottom: none;
}

.batch-table tr:hover {
    background: #f8fafc;
}

.batch-table select,
.batch-table input {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    font-size: var(--text-sm);
}

.batch-table .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--text-sm);
    min-width: auto;
}

.batch-summary {
    display: flex;
    justify-content: space-around;
    padding: var(--spacing-md);
    background: #f0f9ff;
    border-radius: var(--radius-md);
    margin-top: var(--spacing-lg);
}

.summary-item {
    text-align: center;
}

.summary-item span:first-child {
    display: block;
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.summary-item span:last-child {
    display: block;
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--primary-blue);
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--primary-blue);
    color: var(--primary-blue);
}

.btn-outline:hover {
    background: var(--primary-blue);
    color: white;
}

/* 批量发货模式样式 */
.shipping-mode {
    width: 100%;
}

.batch-shipping-header {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: #f0f9ff;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.batch-shipping-table-container {
    margin: var(--spacing-lg) 0;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    overflow: hidden;
    max-height: 400px;
    overflow-y: auto;
}

.batch-shipping-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.batch-shipping-table th {
    background: #f8fafc;
    padding: var(--spacing-md);
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    z-index: 10;
}

.batch-shipping-table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.batch-shipping-table tr:last-child td {
    border-bottom: none;
}

.batch-shipping-table tr:hover {
    background: #f8fafc;
}

.batch-shipping-table tr.selected {
    background: rgba(59, 130, 246, 0.05);
}

.batch-shipping-table input[type="number"] {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    font-size: var(--text-sm);
}

.batch-shipping-table .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--text-sm);
    min-width: auto;
}

.batch-shipping-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #ecfdf5;
    border-radius: var(--radius-md);
    margin-top: var(--spacing-lg);
}

.batch-shipping-summary .summary-item {
    text-align: center;
}

.batch-shipping-summary .summary-item span:first-child {
    display: block;
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.batch-shipping-summary .summary-item span:last-child {
    display: block;
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--accent-green);
}

.shipping-item-row {
    transition: all var(--transition-fast);
}

.shipping-item-row.disabled {
    opacity: 0.5;
    background: #f9fafb;
}

.shipping-item-row.disabled input {
    background: #f3f4f6;
    cursor: not-allowed;
}

/* 发货单预览样式 */
.shipping-document {
    background: white;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 800px;
    margin: 0 auto;
}

.shipping-document-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    border-bottom: 2px solid var(--primary-blue);
    padding-bottom: var(--spacing-lg);
}

.shipping-document-title {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--primary-blue);
    margin: 0 0 var(--spacing-md) 0;
}

.shipping-document-subtitle {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    margin: 0;
}

.shipping-document-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.shipping-document-section {
    background: #f8fafc;
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
}

.shipping-document-section h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: var(--text-base);
    font-weight: 600;
}

.shipping-document-field {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
    font-size: var(--text-sm);
}

.shipping-document-field:last-child {
    margin-bottom: 0;
}

.shipping-document-field .label {
    color: var(--text-secondary);
}

.shipping-document-field .value {
    color: var(--text-primary);
    font-weight: 500;
}

.shipping-document-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--spacing-2xl);
    border: 1px solid var(--border-light);
}

.shipping-document-table th,
.shipping-document-table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-light);
}

.shipping-document-table th {
    background: var(--primary-blue);
    color: white;
    font-weight: 600;
}

.shipping-document-table tr:last-child td {
    border-bottom: none;
}

.shipping-document-table .number-cell {
    text-align: right;
}

.shipping-document-footer {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-2xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

.shipping-document-signature {
    text-align: center;
}

.shipping-document-signature .title {
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.shipping-document-signature .line {
    border-bottom: 1px solid var(--text-secondary);
    height: 40px;
    margin-bottom: var(--spacing-sm);
}

.shipping-document-signature .date {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

/* 原材料采购管理样式 */
.material-mode {
    width: 100%;
}

.material-history-header {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: #f8fafc;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.history-filters {
    display: flex;
    align-items: end;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.history-filters .form-group {
    margin-bottom: 0;
    min-width: 150px;
}

.history-filters label {
    font-size: var(--text-sm);
    margin-bottom: var(--spacing-xs);
}

.material-history-table-container {
    margin: var(--spacing-lg) 0;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    overflow: hidden;
    max-height: 400px;
    overflow-y: auto;
}

.material-history-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.material-history-table th {
    background: #f8fafc;
    padding: var(--spacing-md);
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    z-index: 10;
}

.material-history-table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.material-history-table tr:last-child td {
    border-bottom: none;
}

.material-history-table tr:hover {
    background: #f8fafc;
}

.material-history-table .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--text-sm);
    min-width: auto;
}

.material-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #f0f9ff;
    border-radius: var(--radius-md);
    margin-top: var(--spacing-lg);
}

.material-summary .summary-item {
    text-align: center;
}

.material-summary .summary-item span:first-child {
    display: block;
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.material-summary .summary-item span:last-child {
    display: block;
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--primary-blue);
}

/* Excel导入模态框样式 */
.import-instructions {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.instruction-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.instruction-item:last-child {
    margin-bottom: 0;
}

.instruction-item i {
    color: var(--primary-blue);
    width: 16px;
    text-align: center;
}

.file-input-container {
    position: relative;
    border: 2px dashed var(--border-medium);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    text-align: center;
    transition: all var(--transition-fast);
    background: var(--bg-secondary);
}

.file-input-container:hover {
    border-color: var(--primary-blue);
    background: #f8fafc;
}

.file-input-container input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-input-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
}

.file-input-info i {
    font-size: var(--text-2xl);
    color: var(--primary-blue);
}

.preview-table-container {
    max-height: 300px;
    overflow: auto;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    margin-top: var(--spacing-md);
}

.preview-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.preview-table th {
    background: #f8fafc;
    padding: var(--spacing-sm);
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    z-index: 10;
}

.preview-table td {
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
    font-size: var(--text-sm);
}

.preview-table tr:last-child td {
    border-bottom: none;
}

.preview-table tr:hover {
    background: #f8fafc;
}

.preview-info {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

/* 云端同步模态框样式 */
.sync-status-card {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.sync-status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.sync-status-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--text-base);
    font-weight: 600;
}

.sync-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--text-sm);
    font-weight: 500;
}

.sync-indicator .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--border-medium);
}

.sync-indicator .status-dot.active {
    background: var(--accent-green);
    animation: pulse 2s infinite;
}

.sync-indicator .status-dot.error {
    background: #ef4444;
}

.sync-indicator .status-dot.warning {
    background: var(--primary-orange);
}

.sync-info {
    color: var(--text-secondary);
    font-size: var(--text-sm);
    line-height: 1.5;
}

.sync-options {
    margin: var(--spacing-lg) 0;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.sync-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

.sync-actions .btn {
    flex: 1;
}

/* 未生产规格统计样式 */
.unproduced-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.unproduced-spec-card {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.unproduced-spec-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-orange), #ea580c);
}

.unproduced-spec-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.unproduced-spec-card.h100::before {
    background: linear-gradient(90deg, var(--primary-blue), var(--accent-blue));
}

.unproduced-spec-card.h80::before {
    background: linear-gradient(90deg, var(--accent-green), #059669);
}

.unproduced-spec-card.h120::before {
    background: linear-gradient(90deg, var(--primary-orange), #ea580c);
}

.unproduced-spec-title {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    word-break: break-all;
}

.unproduced-spec-value {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: var(--spacing-xs) 0;
}

.unproduced-spec-unit {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.unproduced-spec-details {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    line-height: 1.3;
}

.unproduced-spec-progress {
    width: 100%;
    height: 4px;
    background: var(--border-light);
    border-radius: 2px;
    margin: var(--spacing-xs) 0;
    overflow: hidden;
}

.unproduced-spec-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-green), #059669);
    border-radius: 2px;
    transition: width var(--transition-normal);
}

/* 规格类型分组样式 */
.spec-type-header {
    grid-column: 1 / -1;
    margin: var(--spacing-lg) 0 var(--spacing-md) 0;
}

.spec-type-header:first-child {
    margin-top: 0;
}

.spec-type-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid var(--border-light);
    border-left: 4px solid var(--primary-blue);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
}

.spec-type-title h4 {
    margin: 0;
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--text-primary);
}

.spec-type-count {
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: 2px 8px;
    border-radius: var(--radius-full);
}

.spec-type-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--spacing-md);
    grid-column: 1 / -1;
    margin-bottom: var(--spacing-lg);
}

/* 区域拖拽排序样式 */
.drag-hint {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 4px;
}

.drag-hint i {
    color: var(--primary-blue);
}

.area-card-title-wrapper {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    flex: 1;
}

.priority-badge {
    background: var(--primary-blue);
    color: white;
    font-size: 11px;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: var(--radius-full);
    margin-left: auto;
    opacity: 0.8;
    transition: all 0.2s ease;
}

.area-card:hover .priority-badge {
    opacity: 1;
    transform: scale(1.1);
}

.area-drag-handle {
    color: var(--text-secondary);
    cursor: grab;
    padding: 4px;
    border-radius: 4px;
    transition: all var(--transition-fast);
    font-size: 14px;
}

.area-drag-handle:hover {
    color: var(--primary-blue);
    background: var(--bg-secondary);
    transform: scale(1.1);
}

.area-drag-handle:active {
    cursor: grabbing;
}

.area-card[draggable="true"] {
    cursor: move;
    transition: all 0.2s ease;
    position: relative;
}

.area-card[draggable="true"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.1);
}

/* 拖拽状态样式 */
.area-card.dragging {
    opacity: 0.6;
    transform: rotate(3deg) scale(1.02);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.area-card.drag-over {
    transform: scale(0.98);
    border: 2px solid var(--primary-blue);
    background: rgba(59, 130, 246, 0.05);
}

/* 占位符样式 */
.area-card-placeholder {
    height: 200px;
    margin: var(--spacing-md);
    border: 2px dashed var(--primary-blue);
    border-radius: var(--radius-lg);
    background: rgba(59, 130, 246, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-blue);
    font-weight: 500;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
}

/* 拖拽提示动画 */
.area-cards-grid {
    position: relative;
}

.area-cards-grid::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 2px dashed transparent;
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    pointer-events: none;
}

.area-cards-grid.drag-active::before {
    border-color: var(--primary-blue);
    background: rgba(59, 130, 246, 0.02);
}

/* 拖拽手柄增强 */
.area-drag-handle {
    position: relative;
}

.area-drag-handle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: rgba(59, 130, 246, 0.1);
    opacity: 0;
    transition: all 0.2s ease;
}

.area-drag-handle:hover::before {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
}

/* 排序指示器 */
.area-card {
    position: relative;
}

.area-card::before {
    content: attr(data-priority);
    position: absolute;
    top: -8px;
    left: -8px;
    width: 24px;
    height: 24px;
    background: var(--primary-blue);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s ease;
    z-index: 10;
}

.area-card:hover::before {
    opacity: 1;
    transform: scale(1);
}

/* 响应式拖拽 */
@media (max-width: 768px) {
    .area-drag-handle {
        padding: 6px;
        font-size: 16px;
    }

    .drag-hint {
        font-size: 12px;
    }

    .area-card-placeholder {
        height: 150px;
        margin: var(--spacing-sm);
    }
}

/* 客户统计卡片动作按钮 */
.customer-card-actions {
    display: flex;
    gap: 8px;
    margin-top: var(--spacing-md);
}

.customer-action-btn {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: var(--text-sm);
    font-weight: 500;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.customer-action-btn.primary {
    background: var(--primary-blue);
    color: white;
}

.customer-action-btn.primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* 批次发货计划模态框样式 */
.shipping-plan-controls {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.shipping-plans-container {
    display: grid;
    gap: var(--spacing-md);
    max-height: 60vh;
    overflow-y: auto;
}

.shipping-plan-card {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-light);
    transition: all var(--transition-fast);
}

.shipping-plan-card.sufficient {
    border-color: var(--success-green);
    background: rgba(16, 185, 129, 0.05);
}

.shipping-plan-card.insufficient {
    border-color: var(--warning-orange);
    background: rgba(245, 158, 11, 0.05);
}

.shipping-plan-card.pending {
    border-color: var(--border-light);
}

.plan-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.plan-number {
    background: var(--primary-blue);
    color: white;
    padding: 4px 12px;
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: var(--text-sm);
}

.plan-delete-btn {
    background: var(--danger-red);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: 6px 8px;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.plan-delete-btn:hover {
    background: #dc2626;
    transform: scale(1.05);
}

.plan-content {
    display: grid;
    gap: var(--spacing-md);
}

.plan-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.plan-field {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.plan-field label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: var(--text-sm);
}

.plan-model-select,
.plan-spec-select,
.plan-quantity-input {
    padding: 8px 12px;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    transition: border-color var(--transition-fast);
}

.plan-model-select:focus,
.plan-spec-select:focus,
.plan-quantity-input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.plan-quantity-input {
    display: flex;
    align-items: center;
}

.plan-field .unit {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-left: 8px;
}

.available-quantity {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: var(--radius-md);
    font-weight: 500;
}

.available-quantity.sufficient {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-green);
}

.available-quantity.insufficient {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-orange);
}

.available-quantity.pending {
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.status-indicator {
    font-weight: bold;
    font-size: 16px;
}

.plan-status {
    margin-top: var(--spacing-sm);
    padding: 8px 12px;
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: 500;
}

.status-text.success {
    color: var(--success-green);
    background: rgba(16, 185, 129, 0.1);
}

.status-text.warning {
    color: var(--warning-orange);
    background: rgba(245, 158, 11, 0.1);
}

.status-text.pending {
    color: var(--text-secondary);
    background: var(--bg-secondary);
}

.empty-plans {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.empty-plans i {
    font-size: 3rem;
    opacity: 0.3;
    margin-bottom: 1rem;
    display: block;
}

/* 计划标题和操作区域 */
.plan-title-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;
}

.plan-name-input {
    border: none;
    background: transparent;
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--text-primary);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    min-width: 200px;
}

.plan-name-input:hover,
.plan-name-input:focus {
    background: var(--bg-secondary);
    outline: none;
}

.plan-header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.plan-add-item-btn {
    background: var(--success-green);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: 6px 12px;
    cursor: pointer;
    font-size: var(--text-sm);
    font-weight: 500;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 6px;
}

.plan-add-item-btn:hover {
    background: #059669;
    transform: translateY(-1px);
}

/* 计划项目容器 */
.plan-items-container {
    margin-bottom: var(--spacing-lg);
}

.empty-plan-items {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 2px dashed var(--border-light);
}

.empty-plan-items i {
    font-size: 2rem;
    opacity: 0.5;
    margin-bottom: 0.5rem;
    display: block;
}

.empty-add-item-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-top: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.empty-add-item-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.empty-add-item-btn:active {
    transform: translateY(0);
}

/* 计划项目样式 */
.plan-item {
    background: var(--bg-card);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    transition: all var(--transition-fast);
}

.plan-item.sufficient {
    border-color: var(--success-green);
    background: rgba(16, 185, 129, 0.03);
}

.plan-item.insufficient {
    border-color: var(--warning-orange);
    background: rgba(245, 158, 11, 0.03);
}

.plan-item.pending {
    border-color: var(--border-light);
}

.plan-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.item-number {
    background: var(--primary-blue);
    color: white;
    padding: 2px 8px;
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: var(--text-xs);
}

.item-delete-btn {
    background: var(--danger-red);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    padding: 4px 6px;
    cursor: pointer;
    font-size: var(--text-xs);
    transition: all var(--transition-fast);
}

.item-delete-btn:hover {
    background: #dc2626;
    transform: scale(1.05);
}

.plan-item-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

.plan-item-status {
    margin-top: var(--spacing-sm);
    padding: 6px 10px;
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    font-weight: 500;
}

/* 计划汇总样式 */
.plan-summary {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    border-top: 3px solid var(--primary-blue);
}

.plan-summary-empty {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
}

.plan-summary-content {
    display: grid;
    gap: var(--spacing-md);
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.summary-stat {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: var(--text-xs);
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.summary-status {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.status-badge {
    padding: 4px 8px;
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 500;
}

.status-badge.sufficient {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-green);
}

.status-badge.insufficient {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-orange);
}

.status-badge.pending {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .plan-row,
    .plan-item-row {
        grid-template-columns: 1fr;
    }

    .shipping-plan-controls {
        flex-direction: column;
    }

    .plan-title-section {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .plan-header-actions {
        width: 100%;
        justify-content: space-between;
    }

    .summary-stats {
        grid-template-columns: 1fr;
    }
}

/* 生产数据管理模态框样式 */
.production-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-sm);
}

.stat-label {
    display: block;
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.stat-value {
    display: block;
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--primary-blue);
    word-break: break-word;
    line-height: 1.3;
}

/* 分型号统计的特殊样式 */
.stat-item:last-child .stat-value {
    font-size: var(--text-sm);
    font-weight: 500;
    line-height: 1.4;
    color: var(--text-primary);
}

/* 发货规格信息样式 */
.spec-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.spec-name {
    font-weight: 600;
    color: var(--text-primary);
}

.spec-areas {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    font-style: italic;
}

.production-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: white;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
}

.toolbar-filters {
    display: flex;
    gap: var(--spacing-sm);
    flex: 1;
}

.toolbar-filters select,
.toolbar-filters input {
    padding: 8px 12px;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    font-size: var(--text-sm);
}

.toolbar-filters input {
    min-width: 200px;
}

.toolbar-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.production-table-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
}

.production-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--text-sm);
}

.production-table th,
.production-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-light);
}

.production-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.production-table tbody tr:hover {
    background: var(--bg-secondary);
}

.production-table .quantity-cell {
    font-weight: 600;
    color: var(--success-color);
}

.production-table .date-cell {
    color: var(--text-secondary);
    font-size: var(--text-xs);
}

.production-table .actions-cell {
    white-space: nowrap;
}

.production-table .actions-cell .btn {
    padding: 4px 8px;
    font-size: var(--text-xs);
    margin-right: 4px;
}

.production-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.pagination-info {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

/* 已生产量卡片点击样式 */
.metric-card.produced {
    transition: all 0.2s ease;
}

.metric-card.produced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
    border-color: var(--primary-blue);
}

.metric-card.produced::after {
    content: '点击管理';
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 10px;
    color: var(--text-secondary);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.metric-card.produced:hover::after {
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .production-toolbar {
        flex-direction: column;
        align-items: stretch;
    }

    .toolbar-filters {
        flex-direction: column;
    }

    .toolbar-filters input {
        min-width: auto;
    }

    .production-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .production-table-container {
        max-height: 300px;
    }

    .production-pagination {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}

.unproduced-stats-info {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.unproduced-stats-info p {
    margin: 0;
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.unproduced-stats-info span {
    font-weight: 600;
    color: var(--primary-orange);
}

.refresh-stats-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--primary-blue);
    color: var(--text-white);
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: var(--text-xs);
    transition: all var(--transition-fast);
}

.refresh-stats-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}
