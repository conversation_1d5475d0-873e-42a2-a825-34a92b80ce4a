<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>强制禁用云同步</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .header h1 {
            color: #2d3748;
            margin: 0 0 10px 0;
            font-size: 28px;
        }
        .status-card {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }
        .status-indicator {
            display: inline-block;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .status-enabled { background: #48bb78; }
        .status-disabled { background: #f56565; }
        .status-unknown { background: #a0aec0; }
        .btn {
            display: inline-block;
            padding: 14px 28px;
            margin: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 15px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .btn-danger { background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%); color: white; }
        .btn-success { background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); color: white; }
        .btn-warning { background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%); color: white; }
        .btn-primary { background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%); color: white; }
        .btn-secondary { background: linear-gradient(135deg, #a0aec0 0%, #718096 100%); color: white; }
        .method-card {
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            background: #fafafa;
        }
        .method-title {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .alert {
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid;
        }
        .alert-danger { 
            background: #fed7d7; 
            border-color: #f56565; 
            color: #742a2a; 
        }
        .alert-success { 
            background: #c6f6d5; 
            border-color: #48bb78; 
            color: #22543d; 
        }
        .alert-warning { 
            background: #feebc8; 
            border-color: #ed8936; 
            color: #7b341e; 
        }
        .log-container {
            background: #1a202c;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
            margin: 15px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #3182ce);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ 强制禁用云同步工具</h1>
            <p style="color: #718096; margin: 0;">彻底禁用云同步功能，防止自动重新启用</p>
        </div>

        <div class="alert alert-danger">
            <strong>⚠️ 重要说明：</strong>
            <p style="margin: 10px 0 0 0;">
                系统检测到有自动重新启用云同步的机制。此工具将彻底禁用所有云同步功能，
                包括自动重新启用机制，确保系统完全使用本地存储模式。
            </p>
        </div>

        <div class="status-card">
            <h3 style="margin: 0 0 15px 0; color: #2d3748;">📊 当前状态检查</h3>
            <div id="currentStatus">
                <span class="status-indicator status-unknown"></span>
                <span>正在检查云同步状态...</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="statusDetails" style="margin-top: 15px; font-size: 14px; color: #718096;"></div>
        </div>

        <div class="method-card">
            <div class="method-title">
                🎯 一键强制禁用
            </div>
            <p style="color: #4a5568; margin-bottom: 20px;">
                执行多层次禁用操作，确保云同步完全关闭且不会自动重新启用
            </p>
            <button class="btn btn-danger" onclick="forceDisableSync()" id="forceDisableBtn">
                🚫 强制禁用云同步
            </button>
            <button class="btn btn-success" onclick="enableSync()" id="enableBtn">
                ✅ 重新启用云同步
            </button>
            <button class="btn btn-warning" onclick="checkAllStatus()" id="checkBtn">
                🔍 全面状态检查
            </button>
        </div>

        <div class="method-card">
            <div class="method-title">
                🛠️ 高级操作
            </div>
            <button class="btn btn-primary" onclick="openMainSystem()">
                📱 打开主系统
            </button>
            <button class="btn btn-secondary" onclick="backupSettings()">
                💾 备份当前设置
            </button>
            <button class="btn btn-warning" onclick="restoreSettings()">
                🔄 恢复设置
            </button>
        </div>

        <div id="logOutput" class="method-card" style="display: none;">
            <div class="method-title">
                📝 操作日志
            </div>
            <div id="logContent" class="log-container"></div>
            <button class="btn btn-secondary" onclick="clearLog()" style="margin-top: 10px;">
                🗑️ 清空日志
            </button>
        </div>
    </div>

    <script>
        let logVisible = false;
        let operationInProgress = false;

        // 日志功能
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const logContent = document.getElementById('logContent');
            
            if (!logVisible) {
                logOutput.style.display = 'block';
                logVisible = true;
            }
            
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#4299e1',
                success: '#48bb78',
                warning: '#ed8936',
                error: '#f56565'
            };
            
            const color = colors[type] || colors.info;
            const logEntry = `<div style="color: ${color}; margin: 5px 0;">[${timestamp}] ${message}</div>`;
            logContent.innerHTML += logEntry;
            logContent.scrollTop = logContent.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 更新进度条
        function updateProgress(percent) {
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = percent + '%';
        }

        // 检查所有状态
        function checkAllStatus() {
            if (operationInProgress) return;
            
            log('🔍 开始全面状态检查...', 'info');
            updateProgress(0);
            
            // 检查localStorage
            const isLocalDisabled = localStorage.getItem('disableFirebase') === 'true';
            log(`localStorage.disableFirebase: ${isLocalDisabled ? '已禁用' : '未禁用'}`, isLocalDisabled ? 'success' : 'warning');
            updateProgress(25);
            
            // 检查配置文件状态（通过读取当前页面引用的配置）
            setTimeout(() => {
                try {
                    // 尝试检查全局配置
                    if (window.parent && window.parent.systemConfig) {
                        const configDisabled = window.parent.systemConfig.disableFirebase;
                        log(`配置文件.disableFirebase: ${configDisabled ? '已禁用' : '未禁用'}`, configDisabled ? 'success' : 'warning');
                    } else {
                        log('配置文件状态: 无法直接检查，需要查看firebase-config.js文件', 'warning');
                    }
                    updateProgress(50);
                } catch (e) {
                    log('配置文件检查失败: ' + e.message, 'error');
                }
                
                // 检查Firebase连接状态
                setTimeout(() => {
                    if (window.parent && window.parent.firebaseSync) {
                        const isConnected = window.parent.firebaseSync.isConnected();
                        log(`Firebase连接状态: ${isConnected ? '已连接' : '未连接'}`, isConnected ? 'warning' : 'success');
                    } else {
                        log('Firebase连接状态: 未初始化或不存在', 'success');
                    }
                    updateProgress(75);
                    
                    // 最终状态更新
                    setTimeout(() => {
                        updateProgress(100);
                        updateStatusDisplay();
                        log('✅ 全面状态检查完成', 'success');
                    }, 500);
                }, 500);
            }, 500);
        }

        // 更新状态显示
        function updateStatusDisplay() {
            const isDisabled = localStorage.getItem('disableFirebase') === 'true';
            const statusElement = document.getElementById('currentStatus');
            const detailsElement = document.getElementById('statusDetails');
            
            if (isDisabled) {
                statusElement.innerHTML = '<span class="status-indicator status-disabled"></span><span style="font-weight: 600; color: #f56565;">云同步已禁用（本地模式）</span>';
                detailsElement.innerHTML = '✅ 系统当前使用本地存储，不会连接到云端服务器<br/>✅ 自动重新启用机制已被阻止';
            } else {
                statusElement.innerHTML = '<span class="status-indicator status-enabled"></span><span style="font-weight: 600; color: #48bb78;">云同步已启用</span>';
                detailsElement.innerHTML = '⚠️ 系统将尝试连接到Firebase云端服务器进行数据同步<br/>⚠️ 可能存在自动重新启用机制';
            }
        }

        // 强制禁用云同步
        function forceDisableSync() {
            if (operationInProgress) return;
            
            if (!confirm('确定要强制禁用云同步吗？\n\n这将：\n- 禁用所有云同步功能\n- 阻止自动重新启用\n- 只使用本地存储\n- 断开现有连接')) {
                return;
            }
            
            operationInProgress = true;
            const btn = document.getElementById('forceDisableBtn');
            btn.disabled = true;
            btn.textContent = '正在禁用...';
            
            log('🚫 开始强制禁用云同步...', 'warning');
            updateProgress(0);
            
            try {
                // 步骤1：设置localStorage标志
                localStorage.setItem('disableFirebase', 'true');
                log('✅ 已设置localStorage禁用标志', 'success');
                updateProgress(20);
                
                // 步骤2：设置额外的禁用标志
                localStorage.setItem('forceDisableFirebase', 'true');
                localStorage.setItem('preventAutoEnable', 'true');
                log('✅ 已设置防止自动启用标志', 'success');
                updateProgress(40);
                
                // 步骤3：尝试断开现有连接
                setTimeout(() => {
                    if (window.parent && window.parent.firebaseSync) {
                        try {
                            if (window.parent.firebaseSync.disconnect) {
                                window.parent.firebaseSync.disconnect();
                                log('✅ 已断开Firebase连接', 'success');
                            }
                            if (window.parent.firebaseSync.cleanup) {
                                window.parent.firebaseSync.cleanup();
                                log('✅ 已清理Firebase监听器', 'success');
                            }
                        } catch (e) {
                            log('⚠️ 断开连接时出现警告: ' + e.message, 'warning');
                        }
                    }
                    updateProgress(60);
                    
                    // 步骤4：禁用自动重新启用机制
                    setTimeout(() => {
                        try {
                            // 覆盖可能的自动启用函数
                            if (window.parent) {
                                window.parent.autoEnableFirebase = function() {
                                    console.log('自动启用Firebase被阻止');
                                    return false;
                                };
                                window.parent.enableCloudSync = function() {
                                    console.log('云同步启用被阻止，请使用强制禁用工具重新启用');
                                    return false;
                                };
                            }
                            log('✅ 已阻止自动重新启用机制', 'success');
                        } catch (e) {
                            log('⚠️ 阻止自动启用时出现警告: ' + e.message, 'warning');
                        }
                        updateProgress(80);
                        
                        // 步骤5：完成
                        setTimeout(() => {
                            updateProgress(100);
                            updateStatusDisplay();
                            log('✅ 强制禁用云同步完成！', 'success');
                            
                            btn.disabled = false;
                            btn.textContent = '🚫 强制禁用云同步';
                            operationInProgress = false;
                            
                            if (confirm('云同步已强制禁用。是否要刷新主系统页面以应用更改？')) {
                                if (window.parent && window.parent.location) {
                                    window.parent.location.reload();
                                } else {
                                    window.open('index.html', '_blank');
                                }
                            }
                        }, 1000);
                    }, 1000);
                }, 1000);
                
            } catch (error) {
                log('❌ 强制禁用时出错: ' + error.message, 'error');
                btn.disabled = false;
                btn.textContent = '🚫 强制禁用云同步';
                operationInProgress = false;
            }
        }

        // 重新启用云同步
        function enableSync() {
            if (operationInProgress) return;
            
            if (!confirm('确定要重新启用云同步吗？\n\n这将：\n- 清除所有禁用标志\n- 重新连接到云端\n- 启用多用户协作\n- 自动同步数据')) {
                return;
            }
            
            operationInProgress = true;
            log('✅ 正在重新启用云同步...', 'info');
            
            try {
                // 清除所有禁用标志
                localStorage.removeItem('disableFirebase');
                localStorage.removeItem('forceDisableFirebase');
                localStorage.removeItem('preventAutoEnable');
                log('✅ 已清除所有禁用标志', 'success');
                
                updateStatusDisplay();
                log('✅ 云同步重新启用完成', 'success');
                
                if (confirm('云同步已重新启用。是否要刷新页面以重新连接？')) {
                    if (window.parent && window.parent.location) {
                        window.parent.location.reload();
                    } else {
                        location.reload();
                    }
                }
                
            } catch (error) {
                log('❌ 重新启用时出错: ' + error.message, 'error');
            }
            
            operationInProgress = false;
        }

        // 其他功能
        function openMainSystem() {
            log('📱 打开主系统...', 'info');
            window.open('index.html', '_blank');
        }

        function backupSettings() {
            const settings = {
                disableFirebase: localStorage.getItem('disableFirebase'),
                forceDisableFirebase: localStorage.getItem('forceDisableFirebase'),
                preventAutoEnable: localStorage.getItem('preventAutoEnable'),
                timestamp: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(settings, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'cloud-sync-settings-backup.json';
            a.click();
            URL.revokeObjectURL(url);
            
            log('💾 设置已备份到文件', 'success');
        }

        function restoreSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const settings = JSON.parse(e.target.result);
                            
                            if (settings.disableFirebase) localStorage.setItem('disableFirebase', settings.disableFirebase);
                            if (settings.forceDisableFirebase) localStorage.setItem('forceDisableFirebase', settings.forceDisableFirebase);
                            if (settings.preventAutoEnable) localStorage.setItem('preventAutoEnable', settings.preventAutoEnable);
                            
                            log('🔄 设置已从备份文件恢复', 'success');
                            updateStatusDisplay();
                        } catch (error) {
                            log('❌ 恢复设置失败: ' + error.message, 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        function clearLog() {
            document.getElementById('logContent').innerHTML = '';
            log('🗑️ 日志已清空', 'info');
        }

        // 页面加载时初始化
        window.addEventListener('DOMContentLoaded', function() {
            log('🚀 强制禁用云同步工具已加载', 'info');
            checkAllStatus();
        });
    </script>
</body>
</html>
