<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>同步逻辑测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.danger:hover {
            background: #c82333;
        }
        .button.warning {
            background: #ffc107;
            color: #212529;
        }
        .button.warning:hover {
            background: #e0a800;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .data-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        .conflict-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
        }
        .version-info {
            font-size: 11px;
            color: #666;
            margin-top: 5px;
        }
    </style>

    <!-- 加载必要的脚本 -->
    <script src="https://www.gstatic.com/firebasejs/10.12.2/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.2/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.12.2/firebase-auth-compat.js"></script>
    <script src="firebase-config.js"></script>
    <script src="scripts/firebase-sync.js"></script>
    <script src="scripts/data-management.js"></script>
</head>
<body>
    <div class="container">
        <h1>🔄 同步逻辑测试工具</h1>
        <p>测试多用户数据同步的冲突解决机制</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>📊 当前数据状态</h3>
            <button class="button" onclick="checkDataStatus()">检查数据状态</button>
            <button class="button" onclick="showVersionInfo()">显示版本信息</button>
            <div id="dataStatus" class="status" style="display: none;"></div>
            <div id="versionInfo" class="data-preview" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🧪 冲突模拟测试</h3>
            <button class="button" onclick="simulateConflict1()">模拟时间戳冲突</button>
            <button class="button" onclick="simulateConflict2()">模拟版本号冲突</button>
            <button class="button" onclick="simulateConflict3()">模拟字段冲突</button>
            <button class="button warning" onclick="clearTestData()">清除测试数据</button>
        </div>

        <div class="test-section">
            <h3>🔧 手动同步测试</h3>
            <button class="button" onclick="testManualSync()">执行手动同步</button>
            <button class="button" onclick="testRealTimeSync()">测试实时同步</button>
            <button class="button danger" onclick="forceConflictResolution()">强制冲突解决</button>
        </div>

        <div class="test-section">
            <h3>📝 测试日志</h3>
            <button class="button" onclick="clearLog()">清除日志</button>
            <div id="testLog" class="log-area"></div>
        </div>
    </div>

    <script>
        let testLog = '';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
            testLog += `[${timestamp}] ${prefix} ${message}\n`;
            document.getElementById('testLog').textContent = testLog;
            document.getElementById('testLog').scrollTop = document.getElementById('testLog').scrollHeight;
            console.log(`[SyncTest] ${message}`);
        }

        function clearLog() {
            testLog = '';
            document.getElementById('testLog').textContent = '';
        }

        // 检查数据状态
        function checkDataStatus() {
            log('开始检查数据状态...');
            
            const localData = JSON.parse(localStorage.getItem('productionData') || '[]');
            const shippingData = JSON.parse(localStorage.getItem('shippingHistory') || '[]');
            const materialData = JSON.parse(localStorage.getItem('materialPurchases') || '[]');
            
            const status = {
                local: {
                    production: localData.length,
                    shipping: shippingData.length,
                    materials: materialData.length
                },
                firebase: {
                    connected: window.firebaseSync?.isConnected() || false,
                    user: window.firebaseSync?.userConfig?.name || 'Unknown'
                }
            };

            log(`本地数据: 生产${status.local.production}条, 发货${status.local.shipping}条, 原材料${status.local.materials}条`);
            log(`Firebase状态: ${status.firebase.connected ? '已连接' : '未连接'}, 用户: ${status.firebase.user}`);

            const statusDiv = document.getElementById('dataStatus');
            statusDiv.style.display = 'block';
            statusDiv.className = `status ${status.firebase.connected ? 'success' : 'warning'}`;
            statusDiv.innerHTML = `
                <strong>数据状态:</strong><br>
                本地数据: ${status.local.production + status.local.shipping + status.local.materials} 条记录<br>
                Firebase: ${status.firebase.connected ? '✅ 已连接' : '⚠️ 未连接'}<br>
                当前用户: ${status.firebase.user}
            `;
        }

        // 显示版本信息
        function showVersionInfo() {
            log('显示版本信息...');
            
            const localData = JSON.parse(localStorage.getItem('productionData') || '[]');
            const versionInfo = localData.slice(0, 5).map(item => ({
                id: item.id,
                spec: item.spec,
                version: item.version || 'N/A',
                lastModified: item.lastModified ? new Date(item.lastModified).toLocaleString() : 'N/A',
                lastModifiedBy: item.lastModifiedByName || 'Unknown'
            }));

            const versionDiv = document.getElementById('versionInfo');
            versionDiv.style.display = 'block';
            versionDiv.innerHTML = `
                <strong>前5条记录的版本信息:</strong><br>
                ${versionInfo.map(item => `
                    ID: ${item.id}, 规格: ${item.spec}<br>
                    版本: ${item.version}, 修改时间: ${item.lastModified}<br>
                    修改者: ${item.lastModifiedBy}<br>
                    ---
                `).join('')}
            `;

            log(`显示了 ${versionInfo.length} 条记录的版本信息`);
        }

        // 模拟时间戳冲突
        function simulateConflict1() {
            log('模拟时间戳冲突...', 'warning');
            
            if (!window.dataManager) {
                log('DataManager未加载', 'error');
                return;
            }

            // 创建两个时间戳不同的相同记录
            const baseTime = Date.now();
            const localItem = {
                id: 'test_conflict_1',
                spec: 'H80-6000',
                area: '测试区域',
                planned: 100,
                produced: 50,
                shipped: 0,
                status: '进行中',
                timestamp: baseTime,
                lastModified: baseTime,
                version: 1,
                lastModifiedBy: 'user1',
                lastModifiedByName: '用户1'
            };

            const remoteItem = {
                ...localItem,
                produced: 75,
                status: '已完成',
                lastModified: baseTime + 10000, // 10秒后
                version: 1,
                lastModifiedBy: 'user2',
                lastModifiedByName: '用户2'
            };

            // 测试合并逻辑
            const result = window.dataManager.mergeDataWithRemote([localItem], [remoteItem]);
            log(`时间戳冲突测试完成，合并结果: ${JSON.stringify(result[0], null, 2)}`);
        }

        // 模拟版本号冲突
        function simulateConflict2() {
            log('模拟版本号冲突...', 'warning');
            
            if (!window.dataManager) {
                log('DataManager未加载', 'error');
                return;
            }

            const baseTime = Date.now();
            const localItem = {
                id: 'test_conflict_2',
                spec: 'H100-8000',
                area: '测试区域',
                planned: 200,
                produced: 100,
                version: 3,
                lastModified: baseTime,
                lastModifiedBy: 'user1',
                lastModifiedByName: '用户1'
            };

            const remoteItem = {
                ...localItem,
                produced: 120,
                version: 2, // 版本号较低
                lastModified: baseTime + 5000,
                lastModifiedBy: 'user2',
                lastModifiedByName: '用户2'
            };

            const result = window.dataManager.mergeDataWithRemote([localItem], [remoteItem]);
            log(`版本号冲突测试完成，合并结果: ${JSON.stringify(result[0], null, 2)}`);
        }

        // 模拟字段冲突
        function simulateConflict3() {
            log('模拟字段冲突...', 'warning');
            
            if (!window.dataManager) {
                log('DataManager未加载', 'error');
                return;
            }

            const baseTime = Date.now();
            const localItem = {
                id: 'test_conflict_3',
                spec: 'H80-5000',
                area: '测试区域',
                planned: 150,
                produced: 80,
                shipped: 20,
                status: '进行中',
                remarks: '本地备注',
                version: 2,
                lastModified: baseTime,
                lastModifiedBy: 'user1',
                lastModifiedByName: '用户1'
            };

            const remoteItem = {
                ...localItem,
                produced: 90,
                shipped: 30,
                status: '已完成',
                remarks: '远程备注',
                version: 2,
                lastModified: baseTime + 1000, // 1秒后，在容错范围内
                lastModifiedBy: 'user2',
                lastModifiedByName: '用户2'
            };

            const result = window.dataManager.mergeDataWithRemote([localItem], [remoteItem]);
            log(`字段冲突测试完成，合并结果: ${JSON.stringify(result[0], null, 2)}`);
        }

        // 清除测试数据
        function clearTestData() {
            log('清除测试数据...', 'warning');
            
            if (!window.dataManager) {
                log('DataManager未加载', 'error');
                return;
            }

            // 移除测试数据
            window.dataManager.data = window.dataManager.data.filter(item => 
                !item.id.toString().startsWith('test_conflict_')
            );
            
            window.dataManager.saveToLocalStorage();
            log('测试数据已清除', 'success');
        }

        // 测试手动同步
        function testManualSync() {
            log('开始手动同步测试...');
            
            if (!window.dataManager) {
                log('DataManager未加载', 'error');
                return;
            }

            window.dataManager.manualSync().then(() => {
                log('手动同步测试完成', 'success');
            }).catch(error => {
                log(`手动同步测试失败: ${error.message}`, 'error');
            });
        }

        // 测试实时同步
        function testRealTimeSync() {
            log('测试实时同步...');
            
            if (!window.firebaseSync || !window.firebaseSync.isConnected()) {
                log('Firebase未连接，无法测试实时同步', 'error');
                return;
            }

            // 添加一条测试数据触发实时同步
            const testData = {
                id: 'realtime_test_' + Date.now(),
                spec: 'H80-7000',
                area: '实时测试区域',
                planned: 50,
                produced: 0,
                shipped: 0,
                status: '未开始',
                remarks: '实时同步测试',
                timestamp: Date.now()
            };

            if (window.dataManager) {
                window.dataManager.data.push(testData);
                window.dataManager.saveToLocalStorage();
                log('已添加实时同步测试数据，观察其他客户端是否收到更新', 'success');
            }
        }

        // 强制冲突解决
        function forceConflictResolution() {
            log('强制执行冲突解决...', 'warning');
            
            if (!window.dataManager) {
                log('DataManager未加载', 'error');
                return;
            }

            // 创建一个复杂的冲突场景
            const conflicts = [
                {
                    id: 'force_test_1',
                    spec: 'H80-6000',
                    area: '冲突测试',
                    planned: 100,
                    produced: 60,
                    version: 2,
                    lastModified: Date.now() - 5000
                },
                {
                    id: 'force_test_1',
                    spec: 'H80-6000',
                    area: '冲突测试',
                    planned: 100,
                    produced: 70,
                    version: 3,
                    lastModified: Date.now()
                }
            ];

            const result = window.dataManager.mergeDataWithRemote([conflicts[0]], [conflicts[1]]);
            log(`强制冲突解决完成: ${JSON.stringify(result[0], null, 2)}`, 'success');
        }

        // 初始化系统
        function initializeSystem() {
            log('开始初始化系统...');

            try {
                // 初始化Firebase
                if (window.firebase && window.firebaseConfig) {
                    if (!window.firebaseSync) {
                        window.firebaseSync = new FirebaseSync();
                    }
                    log('Firebase初始化完成', 'success');
                } else {
                    log('Firebase配置未找到', 'warning');
                }

                // 初始化DataManager
                if (!window.dataManager) {
                    window.dataManager = new DataManager();
                    window.dataManager.loadFromLocalStorage();
                    log('DataManager初始化完成', 'success');
                } else {
                    log('DataManager已存在', 'success');
                }

                // 检查数据状态
                checkDataStatus();

            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', () => {
            log('同步逻辑测试工具已加载');

            // 等待脚本加载完成
            setTimeout(() => {
                initializeSystem();
            }, 1000);
        });
    </script>
</body>
</html>
