<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽排序测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            color: #059669;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-info {
            background: #eff6ff;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .test-info h3 {
            color: #3b82f6;
            margin: 0 0 15px 0;
        }
        
        .test-info ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .test-info li {
            margin: 8px 0;
        }
        
        .big-button {
            background: #059669;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            width: calc(50% - 20px);
            box-sizing: border-box;
        }
        
        .big-button:hover {
            background: #047857;
        }
        
        .big-button.secondary {
            background: #3b82f6;
        }
        
        .big-button.secondary:hover {
            background: #2563eb;
        }
        
        .demo-area {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #e5e7eb;
            border-radius: 12px;
            background: #f9fafb;
        }
        
        .demo-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .demo-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            cursor: move;
            transition: all 0.2s ease;
            position: relative;
        }
        
        .demo-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.1);
        }
        
        .demo-card.dragging {
            opacity: 0.6;
            transform: rotate(3deg) scale(1.02);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }
        
        .demo-card.drag-over {
            transform: scale(0.98);
            border: 2px solid #3b82f6;
            background: rgba(59, 130, 246, 0.05);
        }
        
        .demo-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .demo-card-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .drag-handle {
            color: #6b7280;
            cursor: grab;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            font-size: 14px;
        }
        
        .drag-handle:hover {
            color: #3b82f6;
            background: #f3f4f6;
            transform: scale(1.1);
        }
        
        .drag-handle:active {
            cursor: grabbing;
        }
        
        .priority-badge {
            background: #3b82f6;
            color: white;
            font-size: 11px;
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 12px;
            opacity: 0.8;
            transition: all 0.2s ease;
        }
        
        .demo-card:hover .priority-badge {
            opacity: 1;
            transform: scale(1.1);
        }
        
        .demo-card-content {
            color: #6b7280;
            font-size: 14px;
        }
        
        .placeholder {
            height: 120px;
            margin: 10px 0;
            border: 2px dashed #3b82f6;
            border-radius: 12px;
            background: rgba(59, 130, 246, 0.05);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #3b82f6;
            font-weight: 500;
            animation: pulse 1.5s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                opacity: 0.6;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.02);
            }
        }
        
        .result {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .success {
            background: #ecfdf5;
            border: 2px solid #059669;
            color: #059669;
        }
        
        .info {
            background: #eff6ff;
            border: 2px solid #3b82f6;
            color: #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 拖拽排序功能测试</h1>
        
        <div class="test-info">
            <h3>📋 测试说明</h3>
            <ul>
                <li><strong>拖拽操作</strong>：点击并拖拽左侧的拖拽手柄 <i class="fas fa-grip-vertical"></i></li>
                <li><strong>视觉反馈</strong>：拖拽时卡片会变透明并旋转，目标位置会显示蓝色虚线框</li>
                <li><strong>优先级显示</strong>：右上角的蓝色徽章显示当前优先级</li>
                <li><strong>实时更新</strong>：拖拽完成后优先级徽章会自动更新</li>
                <li><strong>触摸支持</strong>：在移动设备上也支持触摸拖拽</li>
            </ul>
        </div>
        
        <div style="display: flex; gap: 20px;">
            <button class="big-button" onclick="createTestCards()">
                🎨 创建测试卡片
            </button>
            
            <button class="big-button secondary" onclick="openMainPage()">
                📊 打开主页面测试
            </button>
        </div>
        
        <div class="demo-area">
            <h3>🧪 拖拽排序演示区域</h3>
            <p>在下面的区域中拖拽卡片来测试排序功能：</p>
            
            <div class="demo-cards" id="demoContainer">
                <!-- 测试卡片将在这里生成 -->
            </div>
        </div>
        
        <div id="results"></div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        let draggedElement = null;
        let placeholder = null;
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const result = document.createElement('div');
            result.className = 'result ' + type;
            result.innerHTML = message;
            resultsDiv.appendChild(result);
        }
        
        function createTestCards() {
            const container = document.getElementById('demoContainer');
            const areas = ['C1区域', 'C3区域', 'E1区域', 'D6区域', 'A2区域'];
            
            container.innerHTML = '';
            
            areas.forEach((area, index) => {
                const card = createDemoCard(area, index + 1);
                container.appendChild(card);
            });
            
            initDragSort(container);
            addResult('✅ 测试卡片已创建，现在可以拖拽排序了！', 'success');
        }
        
        function createDemoCard(area, priority) {
            const card = document.createElement('div');
            card.className = 'demo-card';
            card.draggable = true;
            card.dataset.area = area;
            card.dataset.priority = priority;
            
            card.innerHTML = `
                <div class="demo-card-header">
                    <div class="demo-card-title">
                        <i class="fas fa-grip-vertical drag-handle" title="拖拽排序 - 当前优先级: ${priority}"></i>
                        <h4 style="margin: 0;">${area}</h4>
                    </div>
                    <span class="priority-badge">#${priority}</span>
                </div>
                <div class="demo-card-content">
                    这是 ${area} 的测试卡片，当前优先级为 ${priority}。<br>
                    拖拽左侧的手柄来改变排序。
                </div>
            `;
            
            return card;
        }
        
        function initDragSort(container) {
            // 创建占位符
            const createPlaceholder = () => {
                const placeholder = document.createElement('div');
                placeholder.className = 'placeholder';
                placeholder.innerHTML = '<i class="fas fa-arrow-down" style="margin-right: 8px;"></i>放置到这里';
                return placeholder;
            };
            
            // 拖拽开始
            container.addEventListener('dragstart', (e) => {
                if (e.target.classList.contains('demo-card')) {
                    draggedElement = e.target;
                    placeholder = createPlaceholder();
                    
                    e.target.classList.add('dragging');
                    e.dataTransfer.effectAllowed = 'move';
                    
                    addResult(`🎯 开始拖拽：${e.target.dataset.area}`, 'info');
                }
            });
            
            // 拖拽结束
            container.addEventListener('dragend', (e) => {
                if (e.target.classList.contains('demo-card')) {
                    e.target.classList.remove('dragging');
                    
                    if (placeholder && placeholder.parentNode) {
                        placeholder.parentNode.removeChild(placeholder);
                    }
                    
                    container.querySelectorAll('.demo-card').forEach(card => {
                        card.classList.remove('drag-over');
                    });
                    
                    draggedElement = null;
                    placeholder = null;
                }
            });
            
            // 拖拽经过
            container.addEventListener('dragover', (e) => {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
                
                const afterElement = getDragAfterElement(container, e.clientY);
                
                if (draggedElement && placeholder) {
                    if (afterElement == null) {
                        container.appendChild(placeholder);
                    } else {
                        container.insertBefore(placeholder, afterElement);
                    }
                }
            });
            
            // 拖拽进入
            container.addEventListener('dragenter', (e) => {
                e.preventDefault();
                if (e.target.classList.contains('demo-card') && e.target !== draggedElement) {
                    e.target.classList.add('drag-over');
                }
            });
            
            // 拖拽离开
            container.addEventListener('dragleave', (e) => {
                if (e.target.classList.contains('demo-card') && e.target !== draggedElement) {
                    e.target.classList.remove('drag-over');
                }
            });
            
            // 放置
            container.addEventListener('drop', (e) => {
                e.preventDefault();
                
                if (draggedElement && placeholder && placeholder.parentNode) {
                    placeholder.parentNode.insertBefore(draggedElement, placeholder);
                    placeholder.parentNode.removeChild(placeholder);
                    
                    updatePriorityBadges(container);
                    
                    const newOrder = Array.from(container.children)
                        .filter(card => card.classList.contains('demo-card'))
                        .map(card => card.dataset.area);
                    
                    addResult(`✅ 排序已更新：${newOrder.join(' → ')}`, 'success');
                }
            });
        }
        
        function getDragAfterElement(container, y) {
            const draggableElements = [...container.querySelectorAll('.demo-card:not(.dragging)')];
            
            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = y - box.top - box.height / 2;
                
                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        }
        
        function updatePriorityBadges(container) {
            const cards = container.querySelectorAll('.demo-card');
            cards.forEach((card, index) => {
                const priorityBadge = card.querySelector('.priority-badge');
                const dragHandle = card.querySelector('.drag-handle');
                
                if (priorityBadge) {
                    priorityBadge.textContent = `#${index + 1}`;
                }
                
                if (dragHandle) {
                    dragHandle.title = `拖拽排序 - 当前优先级: ${index + 1}`;
                }
                
                card.dataset.priority = index + 1;
            });
        }
        
        function openMainPage() {
            window.open('index.html', '_blank');
        }
        
        // 页面加载时自动创建测试卡片
        window.onload = function() {
            setTimeout(createTestCards, 500);
        };
    </script>
</body>
</html>
