<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡片联动诊断工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.error { background: #f8d7da; color: #721c24; }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 卡片联动诊断工具</h1>
        
        <div class="section">
            <h3>📊 系统状态检查</h3>
            <div id="systemStatus">检查中...</div>
            <button class="btn" onclick="checkSystemStatus()">重新检查</button>
        </div>
        
        <div class="section">
            <h3>📈 数据源状态</h3>
            <div id="dataSourceStatus">检查中...</div>
            <button class="btn" onclick="checkDataSources()">刷新数据源</button>
        </div>
        
        <div class="section">
            <h3>🎯 卡片数据对比</h3>
            <table class="data-table" id="cardDataTable">
                <thead>
                    <tr>
                        <th>卡片名称</th>
                        <th>当前显示值</th>
                        <th>计算值</th>
                        <th>状态</th>
                        <th>差异</th>
                    </tr>
                </thead>
                <tbody id="cardDataBody">
                    <tr><td colspan="5">加载中...</td></tr>
                </tbody>
            </table>
            <button class="btn" onclick="compareCardData()">重新对比</button>
        </div>
        
        <div class="section">
            <h3>🔄 联动测试</h3>
            <button class="btn" onclick="testLinkage()">测试卡片联动</button>
            <button class="btn" onclick="forceUpdate()">强制更新所有卡片</button>
            <div id="linkageTestResult"></div>
        </div>
        
        <div class="section">
            <h3>📝 诊断日志</h3>
            <div class="log" id="diagnosticLog"></div>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        let logContainer = null;
        
        function log(message, type = 'info') {
            if (!logContainer) {
                logContainer = document.getElementById('diagnosticLog');
            }
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logContainer.textContent += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('diagnosticLog').textContent = '';
        }
        
        function checkSystemStatus() {
            log('🔍 检查系统状态...');
            
            const checks = [
                { name: 'window.dataManager', exists: !!window.parent.dataManager },
                { name: 'window.dashboard', exists: !!window.parent.dashboard },
                { name: 'window.dataCore', exists: !!window.parent.dataCore },
                { name: 'window.productionManager', exists: !!window.parent.productionManager },
                { name: 'window.shippingManager', exists: !!window.parent.shippingManager },
                { name: 'window.uiController', exists: !!window.parent.uiController }
            ];
            
            let html = '<table class="data-table"><tr><th>组件</th><th>状态</th></tr>';
            
            checks.forEach(check => {
                const status = check.exists ? 
                    '<span class="status success">✅ 已加载</span>' : 
                    '<span class="status error">❌ 未加载</span>';
                html += `<tr><td>${check.name}</td><td>${status}</td></tr>`;
                log(`${check.name}: ${check.exists ? '已加载' : '未加载'}`);
            });
            
            html += '</table>';
            document.getElementById('systemStatus').innerHTML = html;
        }
        
        function checkDataSources() {
            log('📊 检查数据源状态...');
            
            const parentWindow = window.parent;
            let dataStats = {};
            
            try {
                if (parentWindow.dataManager) {
                    dataStats.traditional = {
                        productionData: parentWindow.dataManager.data?.length || 0,
                        shippingHistory: parentWindow.dataManager.shippingHistory?.length || 0,
                        materialPurchases: parentWindow.dataManager.materialPurchases?.length || 0
                    };
                }
                
                if (parentWindow.dataCore) {
                    dataStats.modular = {
                        productionData: parentWindow.dataCore.data?.length || 0,
                        shippingHistory: parentWindow.dataCore.shippingHistory?.length || 0,
                        materialPurchases: parentWindow.dataCore.materialPurchases?.length || 0
                    };
                }
                
                let html = '<table class="data-table"><tr><th>数据源</th><th>生产数据</th><th>发货历史</th><th>原材料采购</th></tr>';
                
                if (dataStats.traditional) {
                    html += `<tr><td>传统架构 (dataManager)</td>
                            <td>${dataStats.traditional.productionData}</td>
                            <td>${dataStats.traditional.shippingHistory}</td>
                            <td>${dataStats.traditional.materialPurchases}</td></tr>`;
                }
                
                if (dataStats.modular) {
                    html += `<tr><td>模块化架构 (dataCore)</td>
                            <td>${dataStats.modular.productionData}</td>
                            <td>${dataStats.modular.shippingHistory}</td>
                            <td>${dataStats.modular.materialPurchases}</td></tr>`;
                }
                
                html += '</table>';
                document.getElementById('dataSourceStatus').innerHTML = html;
                
                log(`数据源检查完成: ${JSON.stringify(dataStats)}`);
                
            } catch (error) {
                log(`❌ 数据源检查失败: ${error.message}`);
                document.getElementById('dataSourceStatus').innerHTML = 
                    '<span class="status error">❌ 检查失败</span>';
            }
        }
        
        function compareCardData() {
            log('🎯 对比卡片数据...');
            
            const parentWindow = window.parent;
            const parentDoc = parentWindow.document;
            
            const cards = [
                { name: '总需求量', selector: '.metric-card.total .metric-value', field: 'totalDemandMeters' },
                { name: '已生产量', selector: '.metric-card.produced .metric-value', field: 'producedMeters' },
                { name: '待生产量', selector: '.metric-card.pending .metric-value', field: 'pendingMeters' },
                { name: '已发货量', selector: '.metric-card.shipped .metric-value', field: 'shippedMeters' },
                { name: '未发货量', selector: '.metric-card.unshipped .metric-value', field: 'unshippedMeters' },
                { name: '原材料采购', selector: '.metric-card.material .metric-value', field: 'materialTons' }
            ];
            
            let html = '';
            
            cards.forEach(card => {
                try {
                    const element = parentDoc.querySelector(card.selector);
                    const displayValue = element ? parseFloat(element.textContent.replace(/,/g, '')) || 0 : 0;
                    
                    // 计算实际值
                    let calculatedValue = 0;
                    if (parentWindow.dashboard && parentWindow.dashboard.data) {
                        calculatedValue = parentWindow.dashboard.data[card.field] || 0;
                    }
                    
                    const difference = Math.abs(displayValue - calculatedValue);
                    const isMatch = difference < 0.1;
                    
                    const status = isMatch ? 
                        '<span class="status success">✅ 匹配</span>' : 
                        '<span class="status error">❌ 不匹配</span>';
                    
                    html += `<tr>
                        <td>${card.name}</td>
                        <td>${displayValue.toFixed(1)}</td>
                        <td>${calculatedValue.toFixed(1)}</td>
                        <td>${status}</td>
                        <td>${difference.toFixed(1)}</td>
                    </tr>`;
                    
                    log(`${card.name}: 显示=${displayValue.toFixed(1)}, 计算=${calculatedValue.toFixed(1)}, 差异=${difference.toFixed(1)}`);
                    
                } catch (error) {
                    html += `<tr>
                        <td>${card.name}</td>
                        <td colspan="4"><span class="status error">❌ 检查失败: ${error.message}</span></td>
                    </tr>`;
                    log(`❌ ${card.name} 检查失败: ${error.message}`);
                }
            });
            
            document.getElementById('cardDataBody').innerHTML = html;
        }
        
        function testLinkage() {
            log('🔄 测试卡片联动...');
            
            const parentWindow = window.parent;
            
            try {
                // 触发数据更新
                if (parentWindow.dataManager && typeof parentWindow.dataManager.updateStats === 'function') {
                    log('触发 dataManager.updateStats()...');
                    parentWindow.dataManager.updateStats();
                }
                
                // 触发仪表板更新
                if (parentWindow.dashboard && typeof parentWindow.dashboard.updateMetricsFromDataManager === 'function') {
                    log('触发 dashboard.updateMetricsFromDataManager()...');
                    parentWindow.dashboard.updateMetricsFromDataManager();
                }
                
                // 等待更新完成后重新对比
                setTimeout(() => {
                    log('✅ 联动测试完成，重新对比数据...');
                    compareCardData();
                    document.getElementById('linkageTestResult').innerHTML = 
                        '<span class="status success">✅ 联动测试已执行</span>';
                }, 1000);
                
            } catch (error) {
                log(`❌ 联动测试失败: ${error.message}`);
                document.getElementById('linkageTestResult').innerHTML = 
                    `<span class="status error">❌ 测试失败: ${error.message}</span>`;
            }
        }
        
        function forceUpdate() {
            log('🔧 强制更新所有卡片...');
            
            const parentWindow = window.parent;
            
            try {
                // 触发自定义事件
                const event = new CustomEvent('dataUpdated', {
                    detail: { source: 'diagnostic', timestamp: new Date() }
                });
                parentWindow.document.dispatchEvent(event);
                
                log('✅ 强制更新事件已触发');
                
                setTimeout(() => {
                    compareCardData();
                }, 500);
                
            } catch (error) {
                log(`❌ 强制更新失败: ${error.message}`);
            }
        }
        
        // 页面加载完成后自动执行检查
        window.addEventListener('load', () => {
            log('🚀 诊断工具已加载');
            
            setTimeout(() => {
                checkSystemStatus();
                checkDataSources();
                compareCardData();
            }, 1000);
        });
    </script>
</body>
</html>
