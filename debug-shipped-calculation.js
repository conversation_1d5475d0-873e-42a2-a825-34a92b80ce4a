// 调试已发货量计算问题
// 检查为什么主页面显示3831米而客户详情显示3675米

(function() {
    'use strict';
    
    console.log('🔍 开始调试已发货量计算问题...');
    
    function debugShippedCalculation() {
        if (!window.dataManager || !window.dashboard) {
            console.log('⏳ 等待组件加载...');
            setTimeout(debugShippedCalculation, 500);
            return;
        }
        
        const dm = window.dataManager;
        const dashboard = window.dashboard;
        
        console.log('='.repeat(60));
        console.log('🔍 已发货量计算调试报告');
        console.log('='.repeat(60));
        
        // 1. 检查客户发货统计
        console.log('\n📊 1. 客户发货统计检查:');
        console.log('-'.repeat(40));
        
        let customerStatsTotal = 0;
        try {
            if (typeof dm.calculateCustomerStats === 'function') {
                const customerStats = dm.calculateCustomerStats();
                console.log(`✅ 客户统计方法存在，返回 ${customerStats.length} 个客户`);
                
                customerStats.forEach(customer => {
                    if (customer.totalMeters > 0) {
                        customerStatsTotal += customer.totalMeters;
                        console.log(`  ${customer.customerName}: ${customer.totalMeters.toFixed(1)}米`);
                    }
                });
                
                console.log(`📈 客户统计总计: ${customerStatsTotal.toFixed(1)}米`);
            } else {
                console.log('❌ calculateCustomerStats 方法不存在');
            }
        } catch (error) {
            console.error('❌ 客户统计计算失败:', error);
        }
        
        // 2. 检查生产数据中的shipped字段
        console.log('\n🏭 2. 生产数据shipped字段检查:');
        console.log('-'.repeat(40));
        
        let productionShippedTotal = 0;
        let shippedItemsCount = 0;
        
        if (dm.data && dm.data.length > 0) {
            console.log(`✅ 生产数据存在，共 ${dm.data.length} 条记录`);
            
            dm.data.forEach((item, index) => {
                const shipped = item.shipped || 0;
                if (shipped > 0) {
                    const length = dm.extractLengthFromSpec ? dm.extractLengthFromSpec(item.spec) : 6000;
                    const meters = shipped * length / 1000;
                    productionShippedTotal += meters;
                    shippedItemsCount++;
                    
                    if (shippedItemsCount <= 5) {
                        console.log(`  ${item.spec} (${item.area}): ${shipped}根 × ${length}mm = ${meters.toFixed(1)}米`);
                    }
                }
            });
            
            if (shippedItemsCount > 5) {
                console.log(`  ... 还有 ${shippedItemsCount - 5} 项有shipped数据`);
            }
            
            console.log(`📈 生产数据shipped总计: ${productionShippedTotal.toFixed(1)}米 (${shippedItemsCount}项)`);
        } else {
            console.log('❌ 生产数据不存在或为空');
        }
        
        // 3. 检查主页面计算逻辑
        console.log('\n🎛️ 3. 主页面计算逻辑分析:');
        console.log('-'.repeat(40));
        
        console.log('主页面计算逻辑:');
        console.log('1. 优先使用客户统计数据');
        console.log('2. 如果客户统计为0，使用生产数据shipped字段');
        
        let finalCalculatedValue = 0;
        let calculationMethod = '';
        
        if (customerStatsTotal > 0) {
            finalCalculatedValue = customerStatsTotal;
            calculationMethod = '客户统计';
        } else {
            finalCalculatedValue = productionShippedTotal;
            calculationMethod = '生产数据shipped字段';
        }
        
        console.log(`📊 计算结果: ${finalCalculatedValue.toFixed(1)}米 (使用${calculationMethod})`);
        
        // 4. 检查当前仪表板数据
        console.log('\n🎛️ 4. 当前仪表板数据:');
        console.log('-'.repeat(40));
        
        if (dashboard.data) {
            console.log(`仪表板.shippedMeters: ${dashboard.data.shippedMeters || 0}米`);
            console.log(`仪表板.unshippedMeters: ${dashboard.data.unshippedMeters || 0}米`);
        } else {
            console.log('❌ 仪表板数据不存在');
        }
        
        // 5. 检查DOM显示
        console.log('\n🎨 5. DOM显示检查:');
        console.log('-'.repeat(40));
        
        const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
        if (shippedElement) {
            console.log(`DOM显示值: ${shippedElement.textContent}`);
        } else {
            console.log('❌ 找不到已发货量DOM元素');
        }
        
        // 6. 数据差异分析
        console.log('\n⚖️ 6. 数据差异分析:');
        console.log('-'.repeat(40));
        
        const difference = Math.abs(customerStatsTotal - productionShippedTotal);
        console.log(`客户统计: ${customerStatsTotal.toFixed(1)}米`);
        console.log(`生产shipped: ${productionShippedTotal.toFixed(1)}米`);
        console.log(`差异: ${difference.toFixed(1)}米`);
        
        if (difference > 0.1) {
            console.log('⚠️ 发现数据不一致！');
            
            if (customerStatsTotal > 0 && productionShippedTotal > customerStatsTotal) {
                console.log('🔍 可能原因: 生产数据中的shipped字段包含了已删除的客户发货记录');
                console.log('💡 建议: 清理生产数据中的shipped字段，使其与客户统计保持一致');
            }
        } else {
            console.log('✅ 数据一致');
        }
        
        // 7. 修复建议
        console.log('\n🔧 7. 修复建议:');
        console.log('-'.repeat(40));
        
        if (customerStatsTotal > 0 && productionShippedTotal > customerStatsTotal) {
            console.log('建议执行以下修复:');
            console.log('1. 清理生产数据中的shipped字段');
            console.log('2. 强制使用客户统计作为唯一数据源');
            console.log('3. 重新计算仪表板数据');
            
            // 提供修复选项
            if (confirm('发现数据不一致，是否立即修复？')) {
                fixShippedDataInconsistency();
            }
        } else {
            console.log('数据状态正常，无需修复');
        }
        
        console.log('\n' + '='.repeat(60));
        console.log('🔍 调试报告完成');
        console.log('='.repeat(60));
    }
    
    function fixShippedDataInconsistency() {
        console.log('🔧 开始修复数据不一致问题...');
        
        const dm = window.dataManager;
        const dashboard = window.dashboard;
        
        // 1. 获取正确的客户统计数据
        const customerStats = dm.calculateCustomerStats();
        const correctShippedMeters = customerStats.reduce((sum, customer) => {
            return sum + (customer.totalMeters || 0);
        }, 0);
        
        console.log(`✅ 正确的已发货量: ${correctShippedMeters.toFixed(1)}米`);
        
        // 2. 清理生产数据中的shipped字段（可选）
        let clearedCount = 0;
        dm.data.forEach(item => {
            if (item.shipped && item.shipped > 0) {
                // 注释掉清理代码，避免意外删除数据
                // item.shipped = 0;
                clearedCount++;
            }
        });
        
        console.log(`🧹 发现 ${clearedCount} 项生产数据包含shipped字段`);
        
        // 3. 强制更新仪表板数据
        if (dashboard.data) {
            dashboard.data.shippedMeters = correctShippedMeters;
            dashboard.data.unshippedMeters = Math.max(0, (dashboard.data.producedMeters || 0) - correctShippedMeters);
            console.log(`✅ 已更新仪表板数据: ${correctShippedMeters.toFixed(1)}米`);
        }
        
        // 4. 重写计算方法，强制使用客户统计
        const originalMethod = dashboard.updateMetricsFromDataManager;
        dashboard.updateMetricsFromDataManager = function() {
            // 调用原方法获取其他数据
            if (originalMethod) {
                originalMethod.call(this);
            }
            
            // 强制使用客户统计数据
            const customerStats = dm.calculateCustomerStats();
            const correctShipped = customerStats.reduce((sum, customer) => {
                return sum + (customer.totalMeters || 0);
            }, 0);
            
            this.data.shippedMeters = correctShipped;
            this.data.unshippedMeters = Math.max(0, (this.data.producedMeters || 0) - correctShipped);
            
            console.log(`🔧 强制使用客户统计: ${correctShipped.toFixed(1)}米`);
        };
        
        // 5. 立即更新显示
        dashboard.updateMetricsFromDataManager();
        dashboard.updateMetrics();
        
        // 6. 直接更新DOM
        const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
        if (shippedElement) {
            shippedElement.textContent = correctShippedMeters.toFixed(1);
            
            // 添加视觉反馈
            shippedElement.style.cssText = `
                background: linear-gradient(45deg, #10b981, #059669) !important;
                color: white !important;
                font-weight: bold !important;
                padding: 8px 12px !important;
                border-radius: 8px !important;
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4) !important;
                transition: all 0.5s ease !important;
            `;
            
            setTimeout(() => {
                shippedElement.style.cssText = '';
            }, 3000);
        }
        
        // 7. 保存数据
        dm.saveToLocalStorage();
        
        console.log('🎉 修复完成！');
        
        if (dm.showNotification) {
            dm.showNotification(
                `✅ 数据一致性已修复！已发货量: ${correctShippedMeters.toFixed(1)}米`, 
                'success'
            );
        }
    }
    
    // 立即执行调试
    debugShippedCalculation();
    
    console.log('✅ 已发货量计算调试脚本已启动');
    
})();
