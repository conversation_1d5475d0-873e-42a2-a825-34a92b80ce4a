<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发货数据调试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            color: #dc2626;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .debug-section {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .debug-section h3 {
            color: #374151;
            margin: 0 0 15px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-success {
            background: #059669;
            color: white;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        
        .btn-danger {
            background: #dc2626;
            color: white;
        }
        
        .output {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 15px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-card {
            background: #ecfdf5;
            border: 1px solid #059669;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #059669;
        }
        
        .stat-label {
            font-size: 12px;
            color: #047857;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 发货数据调试工具</h1>
        
        <div class="debug-section">
            <h3>📊 数据统计</h3>
            <div class="stats" id="dataStats">
                <div class="stat-card">
                    <div class="stat-value" id="totalItems">-</div>
                    <div class="stat-label">总数据条数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="itemsWithShipping">-</div>
                    <div class="stat-label">有发货记录的项目</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalShippingRecords">-</div>
                    <div class="stat-label">发货记录总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="historyRecords">-</div>
                    <div class="stat-label">历史记录数</div>
                </div>
            </div>
        </div>
        
        <div class="debug-section">
            <h3>🔍 数据检查</h3>
            <button class="btn btn-primary" onclick="checkData()">检查本地数据</button>
            <button class="btn btn-success" onclick="checkShippingRecords()">检查发货记录</button>
            <button class="btn btn-warning" onclick="checkHistoryRecords()">检查历史记录</button>
            <button class="btn btn-danger" onclick="forceMigration()">强制数据迁移</button>
            <div class="output" id="output"></div>
        </div>
        
        <div class="debug-section">
            <h3>🛠️ 数据操作</h3>
            <button class="btn btn-primary" onclick="clearHistory()">清空历史记录</button>
            <button class="btn btn-success" onclick="exportDebugData()">导出调试数据</button>
            <button class="btn btn-warning" onclick="openMainSystem()">打开主系统</button>
        </div>
    </div>

    <script>
        let debugData = null;
        
        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        function updateStats() {
            if (!debugData) return;
            
            const totalItems = debugData.length;
            let itemsWithShipping = 0;
            let totalShippingRecords = 0;
            
            debugData.forEach(item => {
                if (item.shippingRecords && item.shippingRecords.length > 0) {
                    itemsWithShipping++;
                    totalShippingRecords += item.shippingRecords.length;
                }
            });
            
            const historyRecords = JSON.parse(localStorage.getItem('shippingHistory') || '[]').length;
            
            document.getElementById('totalItems').textContent = totalItems;
            document.getElementById('itemsWithShipping').textContent = itemsWithShipping;
            document.getElementById('totalShippingRecords').textContent = totalShippingRecords;
            document.getElementById('historyRecords').textContent = historyRecords;
        }
        
        function checkData() {
            log('开始检查本地数据...');
            
            try {
                const savedData = localStorage.getItem('productionData');
                if (!savedData) {
                    log('❌ 没有找到本地数据');
                    return;
                }
                
                debugData = JSON.parse(savedData);
                log(`✅ 成功加载 ${debugData.length} 条数据`);
                
                updateStats();
                
                // 检查数据结构
                if (debugData.length > 0) {
                    const sampleItem = debugData[0];
                    log(`📋 数据结构示例:`);
                    log(`   ID: ${sampleItem.id}`);
                    log(`   规格: ${sampleItem.spec}`);
                    log(`   区域: ${sampleItem.area}`);
                    log(`   已发货: ${sampleItem.shipped || 0}`);
                    log(`   发货记录: ${sampleItem.shippingRecords ? sampleItem.shippingRecords.length : 0} 条`);
                }
                
            } catch (error) {
                log(`❌ 数据解析失败: ${error.message}`);
            }
        }
        
        function checkShippingRecords() {
            if (!debugData) {
                log('❌ 请先检查本地数据');
                return;
            }
            
            log('开始检查发货记录...');
            
            let totalRecords = 0;
            let recordsWithCustomer = 0;
            let recordsWithCustomerName = 0;
            
            debugData.forEach((item, index) => {
                if (item.shippingRecords && item.shippingRecords.length > 0) {
                    item.shippingRecords.forEach((record, recordIndex) => {
                        totalRecords++;
                        
                        if (record.customer) recordsWithCustomer++;
                        if (record.customerName) recordsWithCustomerName++;
                        
                        if (totalRecords <= 3) { // 只显示前3条记录的详情
                            log(`📦 发货记录 ${totalRecords}:`);
                            log(`   项目: ${item.spec} (${item.area})`);
                            log(`   日期: ${record.date}`);
                            log(`   客户(customer): ${record.customer || '无'}`);
                            log(`   客户(customerName): ${record.customerName || '无'}`);
                            log(`   数量: ${record.quantity}`);
                            log(`   运输公司: ${record.company || '无'}`);
                            log(`   ---`);
                        }
                    });
                }
            });
            
            log(`✅ 发货记录检查完成:`);
            log(`   总记录数: ${totalRecords}`);
            log(`   有customer字段: ${recordsWithCustomer}`);
            log(`   有customerName字段: ${recordsWithCustomerName}`);
        }
        
        function checkHistoryRecords() {
            log('开始检查历史记录...');
            
            try {
                const historyData = JSON.parse(localStorage.getItem('shippingHistory') || '[]');
                log(`✅ 历史记录数量: ${historyData.length}`);
                
                if (historyData.length > 0) {
                    const sampleRecord = historyData[0];
                    log(`📋 历史记录示例:`);
                    log(`   ID: ${sampleRecord.id}`);
                    log(`   客户: ${sampleRecord.customerName}`);
                    log(`   日期: ${sampleRecord.date}`);
                    log(`   项目数: ${sampleRecord.items ? sampleRecord.items.length : 0}`);
                    log(`   总数量: ${sampleRecord.totalQuantity}`);
                }
                
            } catch (error) {
                log(`❌ 历史记录解析失败: ${error.message}`);
            }
        }
        
        function forceMigration() {
            log('开始强制数据迁移...');
            
            if (!debugData) {
                log('❌ 请先检查本地数据');
                return;
            }
            
            // 模拟迁移逻辑
            const shippingGroups = new Map();
            
            debugData.forEach(item => {
                if (item.shippingRecords && item.shippingRecords.length > 0) {
                    item.shippingRecords.forEach(record => {
                        const customerName = record.customerName || record.customer || '未知客户';
                        const groupKey = `${record.date}-${customerName}-${record.company || ''}-${record.trackingNumber || ''}`;
                        
                        if (!shippingGroups.has(groupKey)) {
                            shippingGroups.set(groupKey, {
                                id: Date.now() + Math.random(),
                                documentNumber: `FH${Date.now()}`,
                                date: record.date,
                                customerName: customerName,
                                company: record.company || '',
                                trackingNumber: record.trackingNumber || '',
                                deliveryAddress: record.deliveryAddress || '',
                                remarks: record.remarks || '',
                                items: [],
                                totalQuantity: 0,
                                totalWeight: 0,
                                totalMeters: 0,
                                timestamp: record.timestamp || new Date().toISOString()
                            });
                        }
                        
                        const group = shippingGroups.get(groupKey);
                        group.items.push({
                            spec: item.spec,
                            quantity: record.quantity,
                            weight: 0, // 简化计算
                            meters: 0  // 简化计算
                        });
                        
                        group.totalQuantity += record.quantity;
                    });
                }
            });
            
            const migratedRecords = Array.from(shippingGroups.values());
            
            // 保存到localStorage
            localStorage.setItem('shippingHistory', JSON.stringify(migratedRecords));
            
            log(`✅ 迁移完成，生成 ${migratedRecords.length} 条历史记录`);
            updateStats();
        }
        
        function clearHistory() {
            if (confirm('确定要清空所有历史记录吗？')) {
                localStorage.removeItem('shippingHistory');
                log('✅ 历史记录已清空');
                updateStats();
            }
        }
        
        function exportDebugData() {
            const debugInfo = {
                productionData: debugData,
                shippingHistory: JSON.parse(localStorage.getItem('shippingHistory') || '[]'),
                timestamp: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(debugInfo, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `debug-data-${new Date().toISOString().slice(0, 10)}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('✅ 调试数据已导出');
        }
        
        function openMainSystem() {
            window.open('index.html', '_blank');
        }
        
        // 页面加载时自动检查数据
        window.onload = function() {
            log('🔧 发货数据调试工具已启动');
            checkData();
        };
    </script>
</body>
</html>
