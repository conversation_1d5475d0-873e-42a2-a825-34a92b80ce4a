// 强制更新仪表板显示的脚本

(function() {
    'use strict';
    
    console.log('🔧 强制更新仪表板显示...');
    
    function forceDashboardUpdate() {
        if (!window.dataManager || !window.dashboard) {
            console.log('⏳ 等待组件加载...');
            setTimeout(forceDashboardUpdate, 500);
            return;
        }
        
        const dm = window.dataManager;
        const dashboard = window.dashboard;
        
        console.log('✅ 开始强制更新仪表板...');
        
        // 1. 从客户统计获取真实发货量
        const customerStats = dm.calculateCustomerStats();
        let totalShippedMeters = 0;
        
        console.log('📊 重新计算客户发货总量:');
        customerStats.forEach(customer => {
            if (customer.totalMeters > 0) {
                totalShippedMeters += customer.totalMeters;
                console.log(`  ${customer.customerName}: ${customer.totalMeters.toFixed(1)}米`);
            }
        });
        
        console.log(`📈 客户发货总计: ${totalShippedMeters.toFixed(1)}米`);
        
        // 2. 检查当前仪表板数据
        console.log('🎛️ 当前仪表板数据:');
        if (dashboard.data) {
            console.log(`  dashboard.data.shippedMeters: ${dashboard.data.shippedMeters}`);
            console.log(`  dashboard.data.producedMeters: ${dashboard.data.producedMeters}`);
            console.log(`  dashboard.data.unshippedMeters: ${dashboard.data.unshippedMeters}`);
        }
        
        // 3. 强制更新仪表板数据对象
        if (dashboard.data) {
            dashboard.data.shippedMeters = totalShippedMeters;
            dashboard.data.unshippedMeters = Math.max(0, (dashboard.data.producedMeters || 0) - totalShippedMeters);
            console.log(`✅ 已更新dashboard.data.shippedMeters = ${totalShippedMeters.toFixed(1)}`);
            console.log(`✅ 已更新dashboard.data.unshippedMeters = ${dashboard.data.unshippedMeters.toFixed(1)}`);
        }
        
        // 4. 直接操作DOM元素
        console.log('🎨 直接更新DOM元素...');
        
        // 查找所有可能的已发货量元素
        const shippedSelectors = [
            '.metric-card.shipped .metric-value',
            '.metric-value[data-metric="shipped"]',
            '[data-shipped] .metric-value',
            '.shipped-metric .value',
            '.dashboard-metric.shipped .value'
        ];
        
        let shippedElement = null;
        for (const selector of shippedSelectors) {
            shippedElement = document.querySelector(selector);
            if (shippedElement) {
                console.log(`✅ 找到已发货量元素: ${selector}`);
                break;
            }
        }
        
        if (shippedElement) {
            const oldValue = shippedElement.textContent;
            shippedElement.textContent = totalShippedMeters.toFixed(1);
            console.log(`✅ 已发货量更新: ${oldValue} -> ${totalShippedMeters.toFixed(1)}`);
            
            // 添加视觉反馈
            shippedElement.style.backgroundColor = '#10b981';
            shippedElement.style.color = 'white';
            shippedElement.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                shippedElement.style.backgroundColor = '';
                shippedElement.style.color = '';
            }, 2000);
        } else {
            console.log('❌ 找不到已发货量显示元素');
            
            // 尝试查找所有包含数字的元素
            const allElements = document.querySelectorAll('*');
            console.log('🔍 查找包含51.8的元素:');
            allElements.forEach(el => {
                if (el.textContent && el.textContent.includes('51.8')) {
                    console.log('  找到元素:', el.className, el.textContent);
                }
            });
        }
        
        // 5. 查找并更新未发货量元素
        const unshippedSelectors = [
            '.metric-card.unshipped .metric-value',
            '.metric-value[data-metric="unshipped"]',
            '[data-unshipped] .metric-value',
            '.unshipped-metric .value'
        ];
        
        let unshippedElement = null;
        for (const selector of unshippedSelectors) {
            unshippedElement = document.querySelector(selector);
            if (unshippedElement) {
                console.log(`✅ 找到未发货量元素: ${selector}`);
                break;
            }
        }
        
        if (unshippedElement && dashboard.data) {
            const oldValue = unshippedElement.textContent;
            unshippedElement.textContent = dashboard.data.unshippedMeters.toFixed(1);
            console.log(`✅ 未发货量更新: ${oldValue} -> ${dashboard.data.unshippedMeters.toFixed(1)}`);
            
            // 添加视觉反馈
            unshippedElement.style.backgroundColor = '#f59e0b';
            unshippedElement.style.color = 'white';
            unshippedElement.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                unshippedElement.style.backgroundColor = '';
                unshippedElement.style.color = '';
            }, 2000);
        }
        
        // 6. 强制调用仪表板更新方法
        console.log('🔄 调用仪表板更新方法...');
        
        try {
            if (typeof dashboard.updateMetrics === 'function') {
                dashboard.updateMetrics();
                console.log('✅ 调用了dashboard.updateMetrics()');
            }
            
            if (typeof dashboard.updateMetricsFromDataManager === 'function') {
                dashboard.updateMetricsFromDataManager();
                console.log('✅ 调用了dashboard.updateMetricsFromDataManager()');
            }
            
            if (typeof dashboard.renderMetrics === 'function') {
                dashboard.renderMetrics();
                console.log('✅ 调用了dashboard.renderMetrics()');
            }
            
            if (typeof dashboard.refresh === 'function') {
                dashboard.refresh();
                console.log('✅ 调用了dashboard.refresh()');
            }
        } catch (error) {
            console.log('⚠️ 调用仪表板方法时出错:', error);
        }
        
        // 7. 保存数据
        dm.saveToLocalStorage();
        
        // 8. 最终验证
        setTimeout(() => {
            const finalShipped = document.querySelector('.metric-card.shipped .metric-value')?.textContent || '未找到';
            const finalUnshipped = document.querySelector('.metric-card.unshipped .metric-value')?.textContent || '未找到';
            
            console.log('🔍 最终验证:');
            console.log(`  已发货量显示: ${finalShipped}`);
            console.log(`  未发货量显示: ${finalUnshipped}`);
            console.log(`  期望已发货量: ${totalShippedMeters.toFixed(1)}`);
            
            if (finalShipped === totalShippedMeters.toFixed(1)) {
                console.log('🎉 仪表板更新成功！');
                if (dm.showNotification) {
                    dm.showNotification(`✅ 仪表板已更新！已发货量: ${totalShippedMeters.toFixed(1)}米`, 'success');
                }
            } else {
                console.log('⚠️ 仪表板更新可能失败，需要手动刷新页面');
                if (dm.showNotification) {
                    dm.showNotification('⚠️ 请手动刷新页面查看更新结果', 'warning');
                }
            }
        }, 1000);
        
        console.log('🎉 强制仪表板更新完成！');
    }
    
    // 添加全局方法
    window.forceDashboardUpdate = forceDashboardUpdate;
    
    // 立即执行
    forceDashboardUpdate();
    
    console.log('🚀 强制仪表板更新脚本已执行');
    console.log('💡 可用命令: forceDashboardUpdate()');
    
})();
