// 立即修复所有客户发货量统计的脚本

(function() {
    'use strict';
    
    console.log('🚀 立即修复所有客户发货量统计...');
    
    // 立即执行修复
    function immediateFixAllCustomers() {
        if (!window.dataManager || !window.dashboard) {
            console.log('⏳ 等待组件加载...');
            setTimeout(immediateFixAllCustomers, 500);
            return;
        }
        
        console.log('✅ 组件已加载，开始修复...');
        
        const dm = window.dataManager;
        const dashboard = window.dashboard;
        
        // 1. 获取客户发货统计数据
        console.log('📊 获取客户发货统计数据...');
        const customerStats = dm.calculateCustomerStats();
        console.log('客户统计原始数据:', customerStats);
        
        // 2. 详细分析每个客户
        let totalShippedMeters = 0;
        let totalShippedQuantity = 0;
        let customerCount = 0;
        
        console.log('🔍 详细分析每个客户:');
        customerStats.forEach((customer, index) => {
            console.log(`客户 ${index + 1}: ${customer.customerName}`);
            console.log(`  - 总米数: ${customer.totalMeters}`);
            console.log(`  - 总根数: ${customer.totalQuantity}`);
            console.log(`  - 是否预定义: ${customer.isPredefined}`);
            console.log(`  - 订单数: ${customer.orderCount}`);
            
            // 统计所有有发货量的客户
            if (customer.totalMeters > 0) {
                totalShippedMeters += customer.totalMeters;
                totalShippedQuantity += customer.totalQuantity;
                customerCount++;
                console.log(`  ✅ 已统计: ${customer.totalMeters.toFixed(1)}米`);
            } else {
                console.log(`  ⚠️ 跳过: 无发货量`);
            }
            console.log('---');
        });
        
        console.log(`📈 汇总结果:`);
        console.log(`  有效客户数: ${customerCount}`);
        console.log(`  总发货量: ${totalShippedMeters.toFixed(1)}米`);
        console.log(`  总发货根数: ${totalShippedQuantity}根`);
        
        // 3. 检查当前仪表板状态
        console.log('🎛️ 当前仪表板状态:');
        if (dashboard.data) {
            console.log(`  当前已发货量: ${dashboard.data.shippedMeters || 0}米`);
            console.log(`  当前已生产量: ${dashboard.data.producedMeters || 0}米`);
            console.log(`  当前未发货量: ${dashboard.data.unshippedMeters || 0}米`);
        }
        
        // 4. 强制更新仪表板数据
        if (dashboard.data) {
            const oldShipped = dashboard.data.shippedMeters || 0;
            dashboard.data.shippedMeters = totalShippedMeters;
            dashboard.data.unshippedMeters = Math.max(0, (dashboard.data.producedMeters || 0) - totalShippedMeters);
            
            console.log(`🔄 仪表板数据更新:`);
            console.log(`  原已发货量: ${oldShipped.toFixed(1)}米`);
            console.log(`  新已发货量: ${totalShippedMeters.toFixed(1)}米`);
            console.log(`  新未发货量: ${dashboard.data.unshippedMeters.toFixed(1)}米`);
        }
        
        // 5. 直接更新DOM元素
        console.log('🎨 更新界面显示...');
        
        const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
        if (shippedElement) {
            const oldValue = parseFloat(shippedElement.textContent) || 0;
            console.log(`更新已发货量显示: ${oldValue} -> ${totalShippedMeters.toFixed(1)}`);
            
            // 立即更新，不使用动画
            shippedElement.textContent = totalShippedMeters.toFixed(1);
            
            // 添加闪烁效果表示更新
            shippedElement.style.background = '#fef3c7';
            shippedElement.style.transition = 'background 0.5s ease';
            setTimeout(() => {
                shippedElement.style.background = '';
            }, 1000);
            
            console.log('✅ 已发货量显示已更新');
        } else {
            console.log('❌ 找不到已发货量显示元素');
        }
        
        const unshippedElement = document.querySelector('.metric-card.unshipped .metric-value');
        if (unshippedElement && dashboard.data) {
            const oldValue = parseFloat(unshippedElement.textContent) || 0;
            console.log(`更新未发货量显示: ${oldValue} -> ${dashboard.data.unshippedMeters.toFixed(1)}`);
            
            unshippedElement.textContent = dashboard.data.unshippedMeters.toFixed(1);
            
            // 添加闪烁效果
            unshippedElement.style.background = '#fef3c7';
            unshippedElement.style.transition = 'background 0.5s ease';
            setTimeout(() => {
                unshippedElement.style.background = '';
            }, 1000);
            
            console.log('✅ 未发货量显示已更新');
        } else {
            console.log('❌ 找不到未发货量显示元素');
        }
        
        // 6. 更新客户发货统计总计显示
        const totalShippedMetersSpan = document.getElementById('totalShippedMeters');
        if (totalShippedMetersSpan) {
            const oldText = totalShippedMetersSpan.textContent;
            const newText = dm.formatNumber(totalShippedMeters.toFixed(1)) + ' 米';
            console.log(`更新客户统计总计: ${oldText} -> ${newText}`);
            totalShippedMetersSpan.textContent = newText;
            console.log('✅ 客户统计总计已更新');
        }
        
        // 7. 保存数据
        dm.saveToLocalStorage();
        
        // 8. 强制刷新相关组件
        setTimeout(() => {
            console.log('🔄 强制刷新组件...');
            dashboard.updateMetricsFromDataManager();
            dashboard.updateMetrics();
            dm.renderCustomerStats();
        }, 500);
        
        console.log('🎉 所有客户发货量统计修复完成！');
        
        // 9. 显示成功提示
        if (dm.showNotification) {
            dm.showNotification(
                `✅ 发货量已修复！统计了${customerCount}个客户，总计: ${totalShippedMeters.toFixed(1)}米`, 
                'success'
            );
        }
        
        // 10. 最终验证
        setTimeout(() => {
            const finalShipped = document.querySelector('.metric-card.shipped .metric-value')?.textContent || '0';
            const finalUnshipped = document.querySelector('.metric-card.unshipped .metric-value')?.textContent || '0';
            
            console.log('🔍 最终验证结果:');
            console.log(`  已发货量显示: ${finalShipped}米`);
            console.log(`  未发货量显示: ${finalUnshipped}米`);
            
            if (parseFloat(finalShipped) === totalShippedMeters) {
                console.log('🎉 修复成功！已发货量显示正确');
            } else {
                console.log('⚠️ 显示可能不一致，需要进一步检查');
            }
        }, 1500);
    }
    
    // 添加全局方法
    window.immediateFixAllCustomers = immediateFixAllCustomers;
    
    // 立即执行
    immediateFixAllCustomers();
    
    console.log('🚀 立即修复脚本已执行');
    console.log('💡 可用命令: immediateFixAllCustomers()');
    
})();
