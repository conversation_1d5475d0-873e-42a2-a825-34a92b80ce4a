<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .status.success {
            background: #f0f9ff;
            border-color: #10b981;
            color: #065f46;
        }
        .status.error {
            background: #fef2f2;
            border-color: #ef4444;
            color: #991b1b;
        }
        .status.warning {
            background: #fffbeb;
            border-color: #f59e0b;
            color: #92400e;
        }
        .console-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 错误修复测试页面</h1>
        
        <div id="status-container">
            <div class="status warning">
                <strong>正在检查系统状态...</strong>
            </div>
        </div>

        <div>
            <button class="test-button" onclick="testDataManager()">测试数据管理器</button>
            <button class="test-button" onclick="testFirebaseSync()">测试Firebase同步</button>
            <button class="test-button" onclick="testDataMerge()">测试数据合并</button>
            <button class="test-button" onclick="clearConsole()">清空控制台</button>
        </div>

        <div class="console-output" id="console-output">
            控制台输出将显示在这里...
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <script src="firebase-config.js"></script>
    <script src="scripts/firebase-sync.js"></script>
    <script src="scripts/data-management.js"></script>

    <script>
        // 重写console.log来显示在页面上
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ef4444' : 
                         type === 'warn' ? '#f59e0b' : '#10b981';
            
            consoleOutput.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function clearConsole() {
            consoleOutput.innerHTML = '控制台已清空...';
        }

        function updateStatus(message, type) {
            const container = document.getElementById('status-container');
            container.innerHTML = `<div class="status ${type}"><strong>${message}</strong></div>`;
        }

        function testDataManager() {
            console.log('🧪 开始测试数据管理器...');
            
            try {
                if (typeof window.dataManager === 'undefined') {
                    console.error('❌ DataManager未定义');
                    updateStatus('DataManager未加载', 'error');
                    return;
                }
                
                console.log('✅ DataManager已加载');
                
                // 测试数据合并功能
                const testLocalData = [
                    { id: '1', spec: 'H80-2000', produced: 100, shipped: 50 },
                    { id: '2', spec: 'H100-3000', produced: 200, shipped: 100 }
                ];
                
                const testRemoteData = [
                    { id: '1', spec: 'H80-2000', produced: 120, shipped: 60 },
                    { id: '3', spec: 'H80-2500', produced: 80, shipped: 30 }
                ];
                
                console.log('测试数据合并...');
                const merged = window.dataManager.mergeDataWithRemote(testLocalData, testRemoteData);
                console.log('合并结果:', merged);
                
                updateStatus('DataManager测试通过', 'success');
                
            } catch (error) {
                console.error('❌ DataManager测试失败:', error);
                updateStatus('DataManager测试失败: ' + error.message, 'error');
            }
        }

        function testFirebaseSync() {
            console.log('🧪 开始测试Firebase同步...');
            
            try {
                if (typeof window.firebaseSync === 'undefined') {
                    console.error('❌ FirebaseSync未定义');
                    updateStatus('FirebaseSync未加载', 'error');
                    return;
                }
                
                console.log('✅ FirebaseSync已加载');
                console.log('Firebase状态:', window.firebaseSync.getConnectionStatus());
                
                updateStatus('FirebaseSync测试通过', 'success');
                
            } catch (error) {
                console.error('❌ FirebaseSync测试失败:', error);
                updateStatus('FirebaseSync测试失败: ' + error.message, 'error');
            }
        }

        function testDataMerge() {
            console.log('🧪 开始测试数据合并逻辑...');
            
            try {
                // 测试各种边界情况
                const testCases = [
                    {
                        name: '正常数据',
                        local: [{ id: '1', name: 'test1' }],
                        remote: [{ id: '2', name: 'test2' }]
                    },
                    {
                        name: '空数据',
                        local: [],
                        remote: []
                    },
                    {
                        name: '无效ID',
                        local: [{ name: 'test1' }],
                        remote: [{ id: null, name: 'test2' }]
                    }
                ];
                
                testCases.forEach(testCase => {
                    console.log(`测试用例: ${testCase.name}`);
                    try {
                        const result = window.dataManager.mergeDataWithRemote(testCase.local, testCase.remote);
                        console.log(`✅ ${testCase.name} 通过，结果长度: ${result.length}`);
                    } catch (error) {
                        console.error(`❌ ${testCase.name} 失败:`, error);
                    }
                });
                
                updateStatus('数据合并测试完成', 'success');
                
            } catch (error) {
                console.error('❌ 数据合并测试失败:', error);
                updateStatus('数据合并测试失败: ' + error.message, 'error');
            }
        }

        // 页面加载完成后自动检查状态
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🚀 页面加载完成，开始自动检查...');
                
                // 检查关键组件
                const checks = [
                    { name: 'DataManager', obj: window.dataManager },
                    { name: 'FirebaseSync', obj: window.firebaseSync },
                    { name: 'Firebase SDK', obj: window.firebase }
                ];
                
                let allPassed = true;
                checks.forEach(check => {
                    if (check.obj) {
                        console.log(`✅ ${check.name} 已加载`);
                    } else {
                        console.error(`❌ ${check.name} 未加载`);
                        allPassed = false;
                    }
                });
                
                if (allPassed) {
                    updateStatus('所有组件加载正常', 'success');
                } else {
                    updateStatus('部分组件加载失败', 'warning');
                }
            }, 2000);
        });
    </script>
</body>
</html>
