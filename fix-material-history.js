// 修复原材料历史记录显示问题的脚本

(function() {
    'use strict';
    
    console.log('🔧 开始修复原材料历史记录显示问题...');
    
    // 等待DataManager加载完成
    function waitForDataManager() {
        return new Promise((resolve) => {
            const checkDataManager = () => {
                if (window.dataManager) {
                    resolve(window.dataManager);
                } else {
                    setTimeout(checkDataManager, 100);
                }
            };
            checkDataManager();
        });
    }
    
    // 修复原材料历史记录显示
    async function fixMaterialHistory() {
        try {
            const dataManager = await waitForDataManager();
            console.log('✅ DataManager已加载');
            
            // 1. 检查materialPurchases数组
            if (!dataManager.materialPurchases) {
                console.log('🔧 初始化materialPurchases数组');
                dataManager.materialPurchases = [];
            }
            
            // 2. 从本地存储加载数据
            const savedMaterials = localStorage.getItem('materialPurchases');
            if (savedMaterials) {
                try {
                    const materials = JSON.parse(savedMaterials);
                    if (Array.isArray(materials) && materials.length > 0) {
                        dataManager.materialPurchases = materials;
                        console.log(`✅ 从本地存储加载了 ${materials.length} 条原材料记录`);
                    }
                } catch (e) {
                    console.error('❌ 解析本地存储数据失败:', e);
                }
            }
            
            // 3. 修复loadMaterialHistory方法
            const originalLoadMaterialHistory = dataManager.loadMaterialHistory;
            dataManager.loadMaterialHistory = function() {
                console.log('🔄 执行loadMaterialHistory，记录数量:', this.materialPurchases.length);
                
                // 确保表格元素存在
                const tableBody = document.getElementById('materialHistoryTableBody');
                if (!tableBody) {
                    console.error('❌ 找不到materialHistoryTableBody元素');
                    return;
                }
                
                // 调用原始方法
                if (originalLoadMaterialHistory) {
                    originalLoadMaterialHistory.call(this);
                } else {
                    // 如果原始方法不存在，手动渲染
                    this.renderMaterialHistoryTable(this.materialPurchases);
                    this.updateMaterialSummary(this.materialPurchases);
                }
                
                console.log('✅ 原材料历史记录已刷新');
            };
            
            // 4. 修复toggleMaterialMode方法
            const originalToggleMaterialMode = dataManager.toggleMaterialMode;
            dataManager.toggleMaterialMode = function() {
                console.log('🔄 执行toggleMaterialMode');
                
                // 调用原始方法
                if (originalToggleMaterialMode) {
                    originalToggleMaterialMode.call(this);
                }
                
                // 确保在切换到历史模式时加载数据
                if (this.isMaterialHistoryMode) {
                    setTimeout(() => {
                        this.loadMaterialHistory();
                    }, 100);
                }
            };
            
            // 5. 修复renderMaterialHistoryTable方法
            const originalRenderMaterialHistoryTable = dataManager.renderMaterialHistoryTable;
            dataManager.renderMaterialHistoryTable = function(purchases) {
                console.log('🔄 执行renderMaterialHistoryTable，记录数量:', purchases ? purchases.length : 0);
                
                const tableBody = document.getElementById('materialHistoryTableBody');
                if (!tableBody) {
                    console.error('❌ 找不到materialHistoryTableBody元素');
                    return;
                }
                
                // 清空表格
                tableBody.innerHTML = '';
                
                // 如果没有记录，显示空状态
                if (!purchases || purchases.length === 0) {
                    const emptyRow = document.createElement('tr');
                    emptyRow.innerHTML = `
                        <td colspan="8" style="text-align: center; padding: 40px; color: #6b7280;">
                            <div style="display: flex; flex-direction: column; align-items: center; gap: 12px;">
                                <i class="fas fa-inbox" style="font-size: 32px; opacity: 0.3;"></i>
                                <div>暂无采购记录</div>
                                <div style="font-size: 14px; opacity: 0.7;">
                                    点击"新增采购"按钮添加第一条记录
                                </div>
                            </div>
                        </td>
                    `;
                    tableBody.appendChild(emptyRow);
                    console.log('📋 显示空状态');
                    return;
                }
                
                // 渲染记录
                purchases.forEach((purchase, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${purchase.date || '-'}</td>
                        <td>${purchase.diameter || '-'}</td>
                        <td>${purchase.supplier || '-'}</td>
                        <td>${purchase.quantity ? purchase.quantity.toFixed(1) : '-'}</td>
                        <td>${purchase.price > 0 ? purchase.price.toFixed(2) : '-'}</td>
                        <td>${purchase.totalAmount > 0 ? purchase.totalAmount.toFixed(2) : '-'}</td>
                        <td>${purchase.batch || '-'}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger"
                                    onclick="dataManager.deleteMaterialPurchase(${purchase.id})"
                                    title="删除记录">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
                
                console.log(`✅ 已渲染 ${purchases.length} 条记录`);
            };
            
            // 6. 添加调试方法
            dataManager.debugMaterialHistory = function() {
                console.log('=== 原材料历史记录调试信息 ===');
                console.log('materialPurchases数组:', this.materialPurchases);
                console.log('数组长度:', this.materialPurchases.length);
                console.log('isMaterialHistoryMode:', this.isMaterialHistoryMode);
                
                const tableBody = document.getElementById('materialHistoryTableBody');
                console.log('表格元素:', tableBody);
                console.log('表格行数:', tableBody ? tableBody.children.length : 'N/A');
                
                const modal = document.getElementById('materialModal');
                const historyMode = document.getElementById('materialHistoryMode');
                console.log('模态框显示:', modal ? modal.style.display : 'N/A');
                console.log('历史模式显示:', historyMode ? historyMode.style.display : 'N/A');
                
                return {
                    recordCount: this.materialPurchases.length,
                    isHistoryMode: this.isMaterialHistoryMode,
                    tableExists: !!tableBody,
                    tableRows: tableBody ? tableBody.children.length : 0
                };
            };
            
            // 7. 添加强制刷新方法
            dataManager.forceRefreshMaterialHistory = function() {
                console.log('🔄 强制刷新原材料历史记录...');
                
                // 重新加载数据
                const savedMaterials = localStorage.getItem('materialPurchases');
                if (savedMaterials) {
                    try {
                        this.materialPurchases = JSON.parse(savedMaterials);
                        console.log(`✅ 重新加载了 ${this.materialPurchases.length} 条记录`);
                    } catch (e) {
                        console.error('❌ 重新加载数据失败:', e);
                    }
                }
                
                // 强制刷新显示
                this.loadMaterialHistory();
                
                // 如果当前在历史模式，确保显示正确
                if (this.isMaterialHistoryMode) {
                    const historyMode = document.getElementById('materialHistoryMode');
                    if (historyMode) {
                        historyMode.style.display = 'block';
                    }
                }
                
                console.log('✅ 强制刷新完成');
            };
            
            // 8. 添加测试数据方法
            dataManager.addTestMaterialData = function() {
                const testRecord = {
                    id: Date.now() + Math.random(),
                    date: new Date().toISOString().split('T')[0],
                    quantity: 15.5,
                    diameter: 'Φ16',
                    supplier: '测试钢厂',
                    price: 4300,
                    totalAmount: 15.5 * 4300,
                    batch: 'TEST' + Date.now(),
                    remarks: '测试数据 - 可删除',
                    timestamp: new Date().toISOString()
                };
                
                this.materialPurchases.push(testRecord);
                this.saveToLocalStorage();
                this.loadMaterialHistory();
                
                console.log('✅ 已添加测试数据:', testRecord);
                return testRecord;
            };
            
            console.log('✅ 原材料历史记录修复完成');
            
            // 如果当前在历史模式，立即刷新
            if (dataManager.isMaterialHistoryMode) {
                dataManager.loadMaterialHistory();
            }
            
            return dataManager;
            
        } catch (error) {
            console.error('❌ 修复原材料历史记录时出错:', error);
            throw error;
        }
    }
    
    // 页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixMaterialHistory);
    } else {
        fixMaterialHistory();
    }
    
    // 导出到全局作用域，方便调试
    window.fixMaterialHistory = fixMaterialHistory;
    
})();

// 添加全局调试函数
window.debugMaterial = function() {
    if (window.dataManager && window.dataManager.debugMaterialHistory) {
        return window.dataManager.debugMaterialHistory();
    } else {
        console.log('DataManager或调试方法不存在');
        return null;
    }
};

window.forceRefreshMaterial = function() {
    if (window.dataManager && window.dataManager.forceRefreshMaterialHistory) {
        window.dataManager.forceRefreshMaterialHistory();
    } else {
        console.log('DataManager或强制刷新方法不存在');
    }
};

window.addTestMaterial = function() {
    if (window.dataManager && window.dataManager.addTestMaterialData) {
        return window.dataManager.addTestMaterialData();
    } else {
        console.log('DataManager或添加测试数据方法不存在');
        return null;
    }
};

console.log('🚀 原材料历史记录修复脚本已加载');
console.log('💡 可用的调试命令:');
console.log('  - debugMaterial() - 查看调试信息');
console.log('  - forceRefreshMaterial() - 强制刷新');
console.log('  - addTestMaterial() - 添加测试数据');
