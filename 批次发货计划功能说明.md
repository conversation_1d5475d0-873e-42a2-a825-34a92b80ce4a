# 📦 批次发货计划功能说明（多规格版本）

## 🎯 功能概述

为客户发货统计模块的每个客户卡片添加了"批次发货计划"功能，支持创建包含多个规格的发货计划，实时检查库存状态，并一键转换为发货单执行。

## ✨ 核心特性

### 📋 多规格计划管理
- **多计划支持**：可为同一客户创建多个独立的发货计划
- **多规格支持**：每个计划可包含多个不同规格的项目
- **计划命名**：支持自定义计划名称，便于管理和识别
- **型号联动**：支持H80/H100型号自动联动规格选择
- **实时库存**：自动显示每个规格的可发货数量
- **状态检查**：智能判断每个规格的库存是否充足

### 🎨 可视化状态显示
- **绿色状态**：库存充足，可以发货（sufficient）
- **红色状态**：库存不足，显示缺少数量（insufficient）
- **灰色状态**：信息不完整，待完善（pending）

### 🚀 一键执行发货
- **自动转换**：将计划转换为标准发货单格式
- **预填数据**：自动填入客户信息和规格数据
- **无缝集成**：与现有发货流程完美集成

## 🎮 使用流程

### 1. 打开发货计划
- 在客户发货统计卡片上点击"批次发货计划"按钮
- 系统打开专用的发货计划模态框

### 2. 添加计划项目
- 点击"新增发货计划"按钮
- 选择型号类型（H80或H100）
- 根据型号自动加载可用规格
- 输入计划发货数量

### 3. 状态检查
- 系统自动计算可发货数量
- 实时显示库存状态：
  - ✅ **绿色**：库存充足，可以发货
  - ⚠️ **红色**：库存不足，显示缺少数量
  - ❓ **灰色**：信息不完整，需要完善

### 4. 执行发货
- 点击"执行发货计划"按钮
- 系统自动筛选库存充足的计划
- 转换为发货单并预填客户信息

## 🔧 技术实现

### 数据结构
```javascript
// 发货计划数据结构（多规格版本）
{
    id: "唯一标识",
    name: "计划名称",
    items: [  // 包含多个规格项目的数组
        {
            id: "规格项目唯一标识",
            modelType: "H80/H100",
            spec: "规格型号",
            quantity: "计划数量",
            availableQuantity: "可发货数量",
            status: "sufficient/insufficient/pending"
        },
        // ... 更多规格项目
    ],
    status: "sufficient/insufficient/pending", // 整个计划的状态
    createdAt: "创建时间"
}
```

### 核心方法
- `openShippingPlanModal()` - 打开发货计划模态框
- `addShippingPlan()` - 添加新的发货计划
- `renderShippingPlans()` - 渲染发货计划列表
- `updatePlanStatus()` - 更新计划状态
- `executeShippingPlans()` - 执行发货计划

### 库存联动
- 实时查询生产数据中的库存信息
- 计算公式：可发货数量 = 已生产数量 - 已发货数量
- 自动更新状态显示

## 🎨 界面设计

### 客户卡片增强
- 在原有客户统计卡片底部添加操作按钮
- 按钮样式与系统整体风格保持一致
- 支持响应式设计

### 发货计划模态框
- 大尺寸模态框，提供充足的操作空间
- 卡片式布局，每个计划独立显示
- 清晰的状态指示和操作按钮

### 状态可视化
- 颜色编码：绿色（充足）、红色（不足）、灰色（待定）
- 图标指示：✓、⚠、? 
- 文字说明：详细的状态描述

## 💾 数据存储

### 本地存储
- 使用localStorage保存发货计划数据
- 按客户名称分组存储
- 支持数据持久化

### 数据格式
```javascript
{
    "客户名称1": [计划1, 计划2, ...],
    "客户名称2": [计划1, 计划2, ...],
    ...
}
```

## 🔄 与现有系统集成

### 发货流程集成
- 计划执行后自动打开批量发货模态框
- 预填客户名称和发货项目
- 保持原有发货流程不变

### 数据同步
- 与库存数据实时同步
- 与客户统计数据联动
- 支持数据导入导出

## 📱 响应式支持

- 移动设备优化布局
- 触摸友好的操作界面
- 自适应屏幕尺寸

## 🛡️ 错误处理

- 完整的数据验证
- 友好的错误提示
- 异常情况处理

这个功能大大提升了发货管理的效率，让用户可以提前规划发货，实时了解库存状态，并快速执行发货操作！
