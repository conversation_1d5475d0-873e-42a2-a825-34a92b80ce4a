<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原材料修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            transition: all 0.2s;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn:hover { opacity: 0.9; transform: translateY(-1px); }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .warning { border-color: #ffc107; background: #fff3cd; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 原材料修复测试工具</h1>
        <p>测试原材料采购管理功能的修复效果</p>

        <div style="margin: 20px 0;">
            <button class="btn btn-primary" onclick="testDataManager()">🔍 测试DataManager</button>
            <button class="btn btn-success" onclick="testMaterialHistory()">📋 测试历史记录</button>
            <button class="btn btn-warning" onclick="addTestData()">➕ 添加测试数据</button>
            <button class="btn btn-danger" onclick="clearTestData()">🗑️ 清空测试数据</button>
        </div>

        <div style="margin: 20px 0;">
            <button class="btn btn-primary" onclick="openMainSystem()">🏠 打开主系统</button>
            <button class="btn btn-success" onclick="openMaterialModal()">📦 打开原材料管理</button>
        </div>

        <div id="testResults"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `[${timestamp}] ${message}`;
            
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function testDataManager() {
            log('🔍 开始测试DataManager...', 'info');
            
            try {
                // 检查主窗口的DataManager
                if (window.parent && window.parent.dataManager) {
                    const dm = window.parent.dataManager;
                    log('✅ 主窗口DataManager存在', 'success');
                    log(`📊 materialPurchases数组长度: ${dm.materialPurchases.length}`, 'info');
                    
                    // 检查修复方法
                    const methods = ['debugMaterialHistory', 'forceRefreshMaterialHistory', 'addTestMaterialData'];
                    methods.forEach(method => {
                        if (typeof dm[method] === 'function') {
                            log(`✅ 修复方法 ${method} 存在`, 'success');
                        } else {
                            log(`❌ 修复方法 ${method} 不存在`, 'error');
                        }
                    });
                    
                    // 运行调试方法
                    if (typeof dm.debugMaterialHistory === 'function') {
                        const debugInfo = dm.debugMaterialHistory();
                        log(`📋 调试信息: ${JSON.stringify(debugInfo)}`, 'info');
                    }
                    
                } else {
                    log('❌ 主窗口DataManager不存在', 'error');
                }
                
                // 检查全局调试函数
                if (typeof window.parent.debugMaterial === 'function') {
                    log('✅ 全局调试函数存在', 'success');
                } else {
                    log('❌ 全局调试函数不存在', 'error');
                }
                
            } catch (error) {
                log(`❌ 测试DataManager时出错: ${error.message}`, 'error');
            }
        }

        function testMaterialHistory() {
            log('📋 开始测试原材料历史记录...', 'info');
            
            try {
                if (window.parent && window.parent.dataManager) {
                    const dm = window.parent.dataManager;
                    
                    // 检查本地存储
                    const localData = localStorage.getItem('materialPurchases');
                    if (localData) {
                        const parsed = JSON.parse(localData);
                        log(`💾 本地存储有 ${parsed.length} 条记录`, 'info');
                    } else {
                        log('⚠️ 本地存储无数据', 'warning');
                    }
                    
                    // 检查DataManager中的数据
                    log(`📊 DataManager中有 ${dm.materialPurchases.length} 条记录`, 'info');
                    
                    // 检查表格元素
                    const tableBody = window.parent.document.getElementById('materialHistoryTableBody');
                    if (tableBody) {
                        log(`✅ 找到历史记录表格，当前行数: ${tableBody.children.length}`, 'success');
                    } else {
                        log('❌ 未找到历史记录表格', 'error');
                    }
                    
                    // 测试强制刷新
                    if (typeof dm.forceRefreshMaterialHistory === 'function') {
                        dm.forceRefreshMaterialHistory();
                        log('🔄 已执行强制刷新', 'success');
                    }
                    
                } else {
                    log('❌ 无法访问DataManager', 'error');
                }
                
            } catch (error) {
                log(`❌ 测试历史记录时出错: ${error.message}`, 'error');
            }
        }

        function addTestData() {
            log('➕ 开始添加测试数据...', 'info');
            
            try {
                if (window.parent && window.parent.dataManager) {
                    const dm = window.parent.dataManager;
                    
                    if (typeof dm.addTestMaterialData === 'function') {
                        const testRecord = dm.addTestMaterialData();
                        log(`✅ 已添加测试数据: ${testRecord.supplier} ${testRecord.diameter} ${testRecord.quantity}吨`, 'success');
                    } else {
                        // 手动添加测试数据
                        const testRecord = {
                            id: Date.now() + Math.random(),
                            date: new Date().toISOString().split('T')[0],
                            quantity: 12.5,
                            diameter: 'Φ14',
                            supplier: '测试钢厂' + Date.now(),
                            price: 4100,
                            totalAmount: 12.5 * 4100,
                            batch: 'TEST' + Date.now(),
                            remarks: '测试数据',
                            timestamp: new Date().toISOString()
                        };
                        
                        dm.materialPurchases.push(testRecord);
                        dm.saveToLocalStorage();
                        dm.loadMaterialHistory();
                        
                        log(`✅ 手动添加测试数据: ${testRecord.supplier} ${testRecord.diameter} ${testRecord.quantity}吨`, 'success');
                    }
                    
                } else {
                    log('❌ 无法访问DataManager', 'error');
                }
                
            } catch (error) {
                log(`❌ 添加测试数据时出错: ${error.message}`, 'error');
            }
        }

        function clearTestData() {
            if (!confirm('确定要清空所有原材料测试数据吗？')) {
                return;
            }
            
            log('🗑️ 开始清空测试数据...', 'warning');
            
            try {
                if (window.parent && window.parent.dataManager) {
                    const dm = window.parent.dataManager;
                    
                    // 只删除测试数据
                    const originalLength = dm.materialPurchases.length;
                    dm.materialPurchases = dm.materialPurchases.filter(item => 
                        !item.supplier.includes('测试') && !item.remarks.includes('测试')
                    );
                    
                    const deletedCount = originalLength - dm.materialPurchases.length;
                    
                    dm.saveToLocalStorage();
                    dm.loadMaterialHistory();
                    
                    log(`✅ 已删除 ${deletedCount} 条测试数据`, 'success');
                    
                } else {
                    log('❌ 无法访问DataManager', 'error');
                }
                
            } catch (error) {
                log(`❌ 清空测试数据时出错: ${error.message}`, 'error');
            }
        }

        function openMainSystem() {
            window.open('index.html', '_blank');
        }

        function openMaterialModal() {
            try {
                if (window.parent && window.parent.dataManager) {
                    // 打开主系统并触发原材料模态框
                    const mainWindow = window.open('index.html', '_blank');
                    
                    // 等待主系统加载完成后打开模态框
                    setTimeout(() => {
                        if (mainWindow && mainWindow.dataManager) {
                            mainWindow.dataManager.openMaterialModal();
                            log('✅ 已打开原材料管理模态框', 'success');
                        }
                    }, 2000);
                    
                } else {
                    window.open('index.html', '_blank');
                }
            } catch (error) {
                log(`❌ 打开原材料模态框时出错: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动运行测试
        window.addEventListener('DOMContentLoaded', function() {
            log('🚀 原材料修复测试工具已加载', 'info');
            
            // 等待一段时间后自动测试
            setTimeout(() => {
                testDataManager();
            }, 1000);
        });
    </script>
</body>
</html>
