// 修复已发货量数据一致性问题
// 确保主页面显示的已发货量与客户发货详情一致

(function() {
    'use strict';
    
    console.log('🔧 开始修复已发货量数据一致性...');
    
    function fixShippedDataConsistency() {
        if (!window.dataManager || !window.dashboard) {
            console.log('⏳ 等待组件加载...');
            setTimeout(fixShippedDataConsistency, 500);
            return;
        }
        
        const dm = window.dataManager;
        const dashboard = window.dashboard;
        
        console.log('✅ 开始数据一致性检查...');
        
        // 1. 从客户发货统计获取真实发货量（这是正确的数据源）
        console.log('📊 从客户发货统计计算真实发货量...');
        const customerStats = dm.calculateCustomerStats();
        let realShippedMeters = 0;
        let realShippedQuantity = 0;
        let customerCount = 0;
        
        customerStats.forEach(customer => {
            if (customer.totalMeters > 0) {
                realShippedMeters += customer.totalMeters;
                realShippedQuantity += customer.totalQuantity;
                customerCount++;
                console.log(`  ${customer.customerName}: ${customer.totalMeters.toFixed(1)}米, ${customer.totalQuantity}根`);
            }
        });
        
        console.log(`📈 客户发货统计汇总: ${customerCount}个客户, ${realShippedMeters.toFixed(1)}米, ${realShippedQuantity}根`);
        
        // 2. 检查生产数据中的shipped字段总计
        console.log('🏭 检查生产数据中的shipped字段...');
        let productionShippedMeters = 0;
        let productionShippedQuantity = 0;
        let shippedItemsCount = 0;
        
        dm.data.forEach(item => {
            const shipped = item.shipped || 0;
            if (shipped > 0) {
                const length = dm.extractLengthFromSpec(item.spec);
                const meters = shipped * length / 1000;
                productionShippedMeters += meters;
                productionShippedQuantity += shipped;
                shippedItemsCount++;
            }
        });
        
        console.log(`🏭 生产数据shipped字段汇总: ${shippedItemsCount}项, ${productionShippedMeters.toFixed(1)}米, ${productionShippedQuantity}根`);
        
        // 3. 比较两个数据源
        const difference = Math.abs(realShippedMeters - productionShippedMeters);
        console.log(`🔍 数据差异: ${difference.toFixed(1)}米`);
        
        if (difference > 0.1) {
            console.log('⚠️ 发现数据不一致！');
            console.log(`  客户发货统计: ${realShippedMeters.toFixed(1)}米`);
            console.log(`  生产数据shipped: ${productionShippedMeters.toFixed(1)}米`);
            console.log(`  差异: ${difference.toFixed(1)}米`);
        } else {
            console.log('✅ 数据一致性检查通过');
        }
        
        // 4. 强制使用客户发货统计作为标准（因为这是用户看到的正确数据）
        console.log('🔧 强制使用客户发货统计作为标准...');
        
        // 更新仪表板数据
        if (dashboard.data) {
            const oldShipped = dashboard.data.shippedMeters || 0;
            dashboard.data.shippedMeters = realShippedMeters;
            dashboard.data.unshippedMeters = Math.max(0, (dashboard.data.producedMeters || 0) - realShippedMeters);
            
            console.log(`🔄 仪表板数据更新:`);
            console.log(`  原已发货量: ${oldShipped.toFixed(1)}米`);
            console.log(`  新已发货量: ${realShippedMeters.toFixed(1)}米`);
            console.log(`  未发货量: ${dashboard.data.unshippedMeters.toFixed(1)}米`);
        }
        
        // 5. 重写updateMetricsFromDataManager方法，确保始终使用客户统计数据
        console.log('🔧 重写updateMetricsFromDataManager方法...');
        
        const originalUpdateMetrics = dashboard.updateMetricsFromDataManager;
        dashboard.updateMetricsFromDataManager = function() {
            console.log('🔄 执行修正的updateMetricsFromDataManager...');
            
            // 调用原方法获取其他数据
            if (originalUpdateMetrics) {
                originalUpdateMetrics.call(this);
            }
            
            // 强制使用客户发货统计的数据
            const customerStats = dm.calculateCustomerStats();
            let correctShippedMeters = 0;
            
            customerStats.forEach(customer => {
                if (customer.totalMeters > 0) {
                    correctShippedMeters += customer.totalMeters;
                }
            });
            
            // 覆盖发货量数据
            this.data.shippedMeters = correctShippedMeters;
            this.data.unshippedMeters = Math.max(0, (this.data.producedMeters || 0) - correctShippedMeters);
            
            console.log(`✅ 已强制使用客户统计数据: ${correctShippedMeters.toFixed(1)}米`);
        };
        
        // 6. 立即更新界面显示
        console.log('🎨 更新界面显示...');
        
        // 直接更新DOM元素
        const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
        if (shippedElement) {
            const oldValue = shippedElement.textContent;
            shippedElement.textContent = realShippedMeters.toFixed(1);
            console.log(`✅ 已发货量DOM更新: ${oldValue} -> ${realShippedMeters.toFixed(1)}`);
            
            // 添加视觉反馈
            shippedElement.style.cssText = `
                background: linear-gradient(45deg, #10b981, #059669) !important;
                color: white !important;
                font-weight: bold !important;
                padding: 8px 12px !important;
                border-radius: 8px !important;
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4) !important;
                transition: all 0.5s ease !important;
            `;
            
            setTimeout(() => {
                shippedElement.style.cssText = '';
            }, 3000);
        }
        
        const unshippedElement = document.querySelector('.metric-card.unshipped .metric-value');
        if (unshippedElement && dashboard.data) {
            const oldValue = unshippedElement.textContent;
            unshippedElement.textContent = dashboard.data.unshippedMeters.toFixed(1);
            console.log(`✅ 未发货量DOM更新: ${oldValue} -> ${dashboard.data.unshippedMeters.toFixed(1)}`);
        }
        
        // 7. 调用仪表板更新方法
        console.log('🔄 调用仪表板更新方法...');
        dashboard.updateMetricsFromDataManager();
        dashboard.updateMetrics();
        
        // 8. 保存数据
        dm.saveToLocalStorage();
        
        console.log('🎉 已发货量数据一致性修复完成！');
        console.log(`📊 最终结果: ${realShippedMeters.toFixed(1)}米 (来自${customerCount}个客户的发货统计)`);
        
        // 9. 显示成功提示
        if (dm.showNotification) {
            dm.showNotification(
                `✅ 数据一致性已修复！已发货量: ${realShippedMeters.toFixed(1)}米`, 
                'success'
            );
        }
        
        // 10. 验证修复结果
        setTimeout(() => {
            const finalShippedDisplay = document.querySelector('.metric-card.shipped .metric-value')?.textContent || '0';
            console.log('🔍 最终验证 - 主页面显示:', finalShippedDisplay);
            
            if (Math.abs(parseFloat(finalShippedDisplay) - realShippedMeters) < 0.1) {
                console.log('🎉 修复成功！主页面与客户详情数据一致');
            } else {
                console.log('⚠️ 仍有差异，可能需要刷新页面');
            }
        }, 1000);
    }
    
    // 立即执行
    fixShippedDataConsistency();
    
    console.log('✅ 已发货量数据一致性修复脚本已启动');
    
})();
