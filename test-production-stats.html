<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产统计显示测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            color: #059669;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .production-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f9fafb;
            border-radius: 12px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
        }
        
        .stat-label {
            display: block;
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 8px;
        }
        
        .stat-value {
            display: block;
            font-size: 24px;
            font-weight: 600;
            color: #3b82f6;
            word-break: break-word;
            line-height: 1.3;
        }
        
        /* 分型号统计的特殊样式 */
        .stat-item:last-child .stat-value {
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
            color: #1f2937;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #e5e7eb;
            border-radius: 12px;
            background: #f9fafb;
        }
        
        .big-button {
            background: #059669;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            width: calc(50% - 20px);
            box-sizing: border-box;
        }
        
        .big-button:hover {
            background: #047857;
        }
        
        .big-button.secondary {
            background: #3b82f6;
        }
        
        .big-button.secondary:hover {
            background: #2563eb;
        }
        
        .result {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .success {
            background: #ecfdf5;
            border: 2px solid #059669;
            color: #059669;
        }
        
        .info {
            background: #eff6ff;
            border: 2px solid #3b82f6;
            color: #3b82f6;
        }
        
        .warning {
            background: #fffbeb;
            border: 2px solid #f59e0b;
            color: #f59e0b;
        }
        
        .error {
            background: #fef2f2;
            border: 2px solid #dc2626;
            color: #dc2626;
        }
        
        .data-preview {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 生产统计显示测试</h1>
        
        <div class="test-section">
            <h3>🎯 修改后的统计显示</h3>
            <p>现在统计信息显示：总记录数、已生产总量、涉及规格、<strong>分型号统计</strong>（替代了涉及区域）</p>
            
            <!-- 模拟统计显示 -->
            <div class="production-stats">
                <div class="stat-item">
                    <span class="stat-label">总生产记录</span>
                    <span class="stat-value" id="totalRecords">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">已生产总量</span>
                    <span class="stat-value" id="totalQuantity">0 根</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">涉及规格</span>
                    <span class="stat-value" id="totalSpecs">0 种</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">分型号统计</span>
                    <span class="stat-value" id="typeStats">暂无数据</span>
                </div>
            </div>
        </div>
        
        <div style="display: flex; gap: 20px; flex-wrap: wrap;">
            <button class="big-button" onclick="loadProductionData()">
                📊 加载生产数据
            </button>
            
            <button class="big-button secondary" onclick="openMainPage()">
                🏠 打开主页面测试
            </button>
        </div>
        
        <div id="results"></div>
        
        <div class="data-preview" id="dataPreview" style="display: none;">
            <h4>📋 数据预览：</h4>
            <div id="dataContent"></div>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const result = document.createElement('div');
            result.className = 'result ' + type;
            result.innerHTML = message;
            resultsDiv.appendChild(result);
        }
        
        function loadProductionData() {
            addResult('🔍 加载生产数据...', 'info');
            
            const productionData = localStorage.getItem('productionData');
            if (!productionData) {
                addResult('❌ 没有找到生产数据', 'error');
                return;
            }
            
            try {
                const data = JSON.parse(productionData);
                
                // 提取生产记录
                const productionRecords = [];
                let recordId = 1;
                
                data.forEach(item => {
                    if (item.produced > 0) {
                        if (item.productionRecords && Array.isArray(item.productionRecords)) {
                            item.productionRecords.forEach(record => {
                                productionRecords.push({
                                    id: `${item.id}_${recordId++}`,
                                    spec: item.spec,
                                    area: item.area,
                                    quantity: record.quantity || 0
                                });
                            });
                        } else {
                            productionRecords.push({
                                id: `${item.id}_${recordId++}`,
                                spec: item.spec,
                                area: item.area,
                                quantity: item.produced
                            });
                        }
                    }
                });
                
                // 计算统计信息
                const totalRecords = productionRecords.length;
                const totalQuantity = productionRecords.reduce((sum, record) => sum + record.quantity, 0);
                const uniqueSpecs = new Set(productionRecords.map(record => record.spec));
                
                // 按型号分类统计
                const typeStats = {};
                productionRecords.forEach(record => {
                    const typeMatch = record.spec.match(/^(H\\d+)/);
                    const type = typeMatch ? typeMatch[1] : '其他';
                    
                    if (!typeStats[type]) {
                        typeStats[type] = new Set();
                    }
                    typeStats[type].add(record.spec);
                });
                
                // 生成型号统计文本
                const typeStatsText = Object.keys(typeStats)
                    .sort()
                    .map(type => `${type}: ${typeStats[type].size}种`)
                    .join(', ');
                
                // 更新显示
                document.getElementById('totalRecords').textContent = totalRecords;
                document.getElementById('totalQuantity').textContent = `${totalQuantity.toLocaleString()} 根`;
                document.getElementById('totalSpecs').textContent = `${uniqueSpecs.size} 种`;
                document.getElementById('typeStats').textContent = typeStatsText || '暂无数据';
                
                // 显示数据预览
                const dataPreview = document.getElementById('dataPreview');
                const dataContent = document.getElementById('dataContent');
                
                dataContent.innerHTML = `
                    <strong>型号统计详情：</strong><br>
                    ${Object.keys(typeStats).map(type => 
                        `${type}: ${Array.from(typeStats[type]).join(', ')}`
                    ).join('<br>')}
                    <br><br>
                    <strong>生产记录示例：</strong><br>
                    ${productionRecords.slice(0, 5).map(record => 
                        `${record.spec} (${record.area}) - ${record.quantity}根`
                    ).join('<br>')}
                    ${productionRecords.length > 5 ? '<br>...' : ''}
                `;
                
                dataPreview.style.display = 'block';
                
                addResult(`✅ 数据加载成功！`, 'success');
                addResult(`📊 共 ${totalRecords} 条生产记录，涉及 ${uniqueSpecs.size} 种规格`, 'info');
                addResult(`🏷️ 型号分布：${typeStatsText}`, 'info');
                
            } catch (error) {
                addResult(`❌ 数据加载失败：${error.message}`, 'error');
            }
        }
        
        function openMainPage() {
            window.open('index.html', '_blank');
        }
        
        // 页面加载时自动尝试加载数据
        window.onload = function() {
            setTimeout(loadProductionData, 500);
        };
    </script>
</body>
</html>
