@echo off
chcp 65001 >nul
title 云同步控制工具

echo.
echo ========================================
echo    梯桁筋与组合肋生产管理系统
echo        云同步控制工具
echo ========================================
echo.

:menu
echo 请选择操作：
echo.
echo [1] 禁用云同步（仅本地模式）
echo [2] 启用云同步
echo [3] 查看当前状态
echo [4] 打开云同步控制页面
echo [5] 打开主系统
echo [0] 退出
echo.
set /p choice=请输入选项 (0-5): 

if "%choice%"=="1" goto disable_sync
if "%choice%"=="2" goto enable_sync
if "%choice%"=="3" goto check_status
if "%choice%"=="4" goto open_control_page
if "%choice%"=="5" goto open_main_system
if "%choice%"=="0" goto exit
goto invalid_choice

:disable_sync
echo.
echo 🚫 正在禁用云同步...
echo.

REM 方法1：修改配置文件
powershell -Command "(Get-Content 'firebase-config.js') -replace 'disableFirebase: false', 'disableFirebase: true' | Set-Content 'firebase-config.js'"

REM 方法2：设置localStorage标志（通过临时HTML文件）
echo ^<!DOCTYPE html^>^<html^>^<head^>^<meta charset="UTF-8"^>^</head^>^<body^>^<script^>localStorage.setItem('disableFirebase', 'true'); alert('云同步已禁用！'); window.close();^</script^>^</body^>^</html^> > temp_disable.html
start temp_disable.html
timeout /t 2 >nul
del temp_disable.html 2>nul

echo ✅ 云同步已禁用！
echo.
echo 📝 说明：
echo    - 系统现在只使用本地存储
echo    - 数据不会同步到云端
echo    - 多用户协作功能不可用
echo    - 本地数据完全安全
echo.
pause
goto menu

:enable_sync
echo.
echo ✅ 正在启用云同步...
echo.

REM 方法1：修改配置文件
powershell -Command "(Get-Content 'firebase-config.js') -replace 'disableFirebase: true', 'disableFirebase: false' | Set-Content 'firebase-config.js'"

REM 方法2：清除localStorage标志
echo ^<!DOCTYPE html^>^<html^>^<head^>^<meta charset="UTF-8"^>^</head^>^<body^>^<script^>localStorage.removeItem('disableFirebase'); alert('云同步已启用！'); window.close();^</script^>^</body^>^</html^> > temp_enable.html
start temp_enable.html
timeout /t 2 >nul
del temp_enable.html 2>nul

echo ✅ 云同步已启用！
echo.
echo 📝 说明：
echo    - 系统将连接到Firebase云端
echo    - 支持多用户实时协作
echo    - 数据自动同步到云端
echo    - 本地数据会上传到云端
echo.
pause
goto menu

:check_status
echo.
echo 📊 检查当前云同步状态...
echo.

REM 检查配置文件中的设置
findstr /C:"disableFirebase: true" firebase-config.js >nul
if %errorlevel%==0 (
    echo 🔴 配置文件状态：已禁用
) else (
    echo 🟢 配置文件状态：已启用
)

REM 检查localStorage（通过临时HTML文件）
echo ^<!DOCTYPE html^>^<html^>^<head^>^<meta charset="UTF-8"^>^</head^>^<body^>^<script^>var status = localStorage.getItem('disableFirebase'); if(status === 'true') { document.write('🔴 浏览器存储：已禁用'); } else { document.write('🟢 浏览器存储：已启用'); } setTimeout(function(){ window.close(); }, 3000);^</script^>^</body^>^</html^> > temp_status.html
start temp_status.html

echo.
echo 📝 说明：
echo    - 如果两个状态都显示"已禁用"，则云同步完全关闭
echo    - 如果任一状态显示"已启用"，系统可能仍会尝试连接云端
echo    - 建议两个状态保持一致
echo.
pause
goto menu

:open_control_page
echo.
echo 📱 正在打开云同步控制页面...
start disable-cloud-sync.html
goto menu

:open_main_system
echo.
echo 🏠 正在打开主系统...
start index.html
goto menu

:invalid_choice
echo.
echo ❌ 无效选项，请重新选择！
echo.
pause
goto menu

:exit
echo.
echo 👋 感谢使用云同步控制工具！
echo.
pause
exit
