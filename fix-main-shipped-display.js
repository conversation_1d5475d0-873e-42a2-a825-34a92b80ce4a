// 修复主页面已发货量显示问题
// 确保主页面显示与客户发货详情一致

(function() {
    'use strict';
    
    console.log('🔧 开始修复主页面已发货量显示...');
    
    function fixMainShippedDisplay() {
        if (!window.dataManager || !window.dashboard) {
            console.log('⏳ 等待组件加载...');
            setTimeout(fixMainShippedDisplay, 500);
            return;
        }
        
        const dm = window.dataManager;
        const dashboard = window.dashboard;
        
        console.log('✅ 开始修复主页面已发货量...');
        
        // 1. 从客户发货统计获取正确的发货量
        console.log('📊 从客户发货统计获取正确数据...');
        const customerStats = dm.calculateCustomerStats();
        let correctShippedMeters = 0;
        let customerCount = 0;
        
        customerStats.forEach(customer => {
            if (customer.totalMeters > 0) {
                correctShippedMeters += customer.totalMeters;
                customerCount++;
                console.log(`  ${customer.customerName}: ${customer.totalMeters.toFixed(1)}米`);
            }
        });
        
        console.log(`📈 正确的已发货量: ${correctShippedMeters.toFixed(1)}米 (来自${customerCount}个客户)`);
        
        // 2. 检查当前主页面显示
        const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
        if (shippedElement) {
            const currentDisplay = shippedElement.textContent;
            console.log(`🔍 当前主页面显示: ${currentDisplay}`);
            
            if (parseFloat(currentDisplay) !== correctShippedMeters) {
                console.log('⚠️ 发现显示不一致！开始修复...');
                
                // 直接更新DOM显示
                shippedElement.textContent = correctShippedMeters.toFixed(1);
                console.log(`✅ 已更新主页面显示: ${currentDisplay} -> ${correctShippedMeters.toFixed(1)}`);
                
                // 添加视觉反馈
                shippedElement.style.cssText = `
                    background: linear-gradient(45deg, #10b981, #059669) !important;
                    color: white !important;
                    font-weight: bold !important;
                    padding: 8px 12px !important;
                    border-radius: 8px !important;
                    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4) !important;
                    transition: all 0.5s ease !important;
                `;
                
                setTimeout(() => {
                    shippedElement.style.cssText = '';
                }, 3000);
            } else {
                console.log('✅ 显示已经正确，无需修复');
            }
        } else {
            console.log('❌ 找不到已发货量显示元素');
        }
        
        // 3. 更新仪表板数据对象
        if (dashboard.data) {
            const oldShipped = dashboard.data.shippedMeters || 0;
            dashboard.data.shippedMeters = correctShippedMeters;
            dashboard.data.unshippedMeters = Math.max(0, (dashboard.data.producedMeters || 0) - correctShippedMeters);
            
            console.log(`🔄 仪表板数据更新:`);
            console.log(`  原已发货量: ${oldShipped.toFixed(1)}米`);
            console.log(`  新已发货量: ${correctShippedMeters.toFixed(1)}米`);
            console.log(`  未发货量: ${dashboard.data.unshippedMeters.toFixed(1)}米`);
        }
        
        // 4. 重写updateMetrics方法，防止被其他代码覆盖
        console.log('🔧 重写updateMetrics方法...');
        
        const originalUpdateMetrics = dashboard.updateMetrics;
        dashboard.updateMetrics = function() {
            // 调用原方法
            if (originalUpdateMetrics) {
                originalUpdateMetrics.call(this);
            }
            
            // 强制确保已发货量显示正确
            const shippedEl = document.querySelector('.metric-card.shipped .metric-value');
            if (shippedEl) {
                const customerStats = dm.calculateCustomerStats();
                let realShippedMeters = 0;
                
                customerStats.forEach(customer => {
                    if (customer.totalMeters > 0) {
                        realShippedMeters += customer.totalMeters;
                    }
                });
                
                if (parseFloat(shippedEl.textContent) !== realShippedMeters) {
                    shippedEl.textContent = realShippedMeters.toFixed(1);
                    console.log(`🔧 updateMetrics中修正显示: ${realShippedMeters.toFixed(1)}米`);
                }
            }
        };
        
        // 5. 重写updateMetricsFromDataManager方法
        console.log('🔧 重写updateMetricsFromDataManager方法...');
        
        const originalUpdateMetricsFromDataManager = dashboard.updateMetricsFromDataManager;
        dashboard.updateMetricsFromDataManager = function() {
            // 调用原方法获取其他数据
            if (originalUpdateMetricsFromDataManager) {
                originalUpdateMetricsFromDataManager.call(this);
            }
            
            // 强制使用客户发货统计的数据
            const customerStats = dm.calculateCustomerStats();
            let realShippedMeters = 0;
            
            customerStats.forEach(customer => {
                if (customer.totalMeters > 0) {
                    realShippedMeters += customer.totalMeters;
                }
            });
            
            // 覆盖发货量数据
            this.data.shippedMeters = realShippedMeters;
            this.data.unshippedMeters = Math.max(0, (this.data.producedMeters || 0) - realShippedMeters);
            
            console.log(`🔧 updateMetricsFromDataManager中强制使用客户统计: ${realShippedMeters.toFixed(1)}米`);
        };
        
        // 6. 设置定期检查，防止被其他脚本修改
        console.log('🔧 设置定期检查...');
        
        setInterval(() => {
            const shippedEl = document.querySelector('.metric-card.shipped .metric-value');
            if (shippedEl) {
                const customerStats = dm.calculateCustomerStats();
                let realShippedMeters = 0;
                
                customerStats.forEach(customer => {
                    if (customer.totalMeters > 0) {
                        realShippedMeters += customer.totalMeters;
                    }
                });
                
                const currentDisplay = parseFloat(shippedEl.textContent) || 0;
                if (Math.abs(currentDisplay - realShippedMeters) > 0.1) {
                    console.log(`🔧 定期检查发现不一致: ${currentDisplay} != ${realShippedMeters.toFixed(1)}，正在修正...`);
                    shippedEl.textContent = realShippedMeters.toFixed(1);
                }
            }
        }, 3000); // 每3秒检查一次
        
        // 7. 立即调用更新方法
        dashboard.updateMetricsFromDataManager();
        dashboard.updateMetrics();
        
        // 8. 保存数据
        dm.saveToLocalStorage();
        
        console.log('🎉 主页面已发货量显示修复完成！');
        console.log(`📊 最终显示: ${correctShippedMeters.toFixed(1)}米`);
        
        // 9. 显示成功提示
        if (dm.showNotification) {
            dm.showNotification(
                `✅ 主页面已发货量已修复！显示: ${correctShippedMeters.toFixed(1)}米`, 
                'success'
            );
        }
        
        // 10. 验证修复结果
        setTimeout(() => {
            const finalDisplay = document.querySelector('.metric-card.shipped .metric-value')?.textContent || '0';
            console.log('🔍 最终验证 - 主页面显示:', finalDisplay);
            
            if (Math.abs(parseFloat(finalDisplay) - correctShippedMeters) < 0.1) {
                console.log('🎉 修复成功！主页面显示正确');
            } else {
                console.log('⚠️ 修复后仍有问题，可能需要手动刷新页面');
            }
        }, 1000);
    }
    
    // 立即执行
    fixMainShippedDisplay();
    
    console.log('✅ 主页面已发货量修复脚本已启动');
    
})();
