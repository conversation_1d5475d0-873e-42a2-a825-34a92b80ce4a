// 强制更新已发货量显示为正确的3675米
// 解决主页面显示3831米而实际应该是3675米的问题

(function() {
    'use strict';
    
    console.log('🔧 强制更新已发货量显示...');
    
    function forceUpdateShippedDisplay() {
        if (!window.dataManager || !window.dashboard) {
            console.log('⏳ 等待组件加载...');
            setTimeout(forceUpdateShippedDisplay, 500);
            return;
        }
        
        const dm = window.dataManager;
        const dashboard = window.dashboard;
        
        // 1. 获取正确的客户统计数据
        console.log('📊 获取正确的客户统计数据...');
        const customerStats = dm.calculateCustomerStats();
        const correctShippedMeters = customerStats.reduce((sum, customer) => {
            return sum + (customer.totalMeters || 0);
        }, 0);
        
        console.log(`✅ 正确的已发货量: ${correctShippedMeters.toFixed(1)}米`);
        
        // 2. 强制更新仪表板数据对象
        console.log('🎛️ 更新仪表板数据对象...');
        if (dashboard.data) {
            const oldShipped = dashboard.data.shippedMeters || 0;
            dashboard.data.shippedMeters = correctShippedMeters;
            dashboard.data.unshippedMeters = Math.max(0, (dashboard.data.producedMeters || 0) - correctShippedMeters);
            
            console.log(`🔄 仪表板数据更新:`);
            console.log(`  原已发货量: ${oldShipped.toFixed(1)}米`);
            console.log(`  新已发货量: ${correctShippedMeters.toFixed(1)}米`);
            console.log(`  新未发货量: ${dashboard.data.unshippedMeters.toFixed(1)}米`);
        }
        
        // 3. 直接强制更新DOM元素
        console.log('🎨 强制更新DOM显示...');
        
        const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
        if (shippedElement) {
            const oldValue = shippedElement.textContent;
            shippedElement.textContent = correctShippedMeters.toFixed(1);
            
            console.log(`✅ 已发货量DOM更新: ${oldValue} -> ${correctShippedMeters.toFixed(1)}`);
            
            // 添加醒目的视觉反馈
            shippedElement.style.cssText = `
                background: linear-gradient(45deg, #10b981, #059669) !important;
                color: white !important;
                font-weight: bold !important;
                padding: 8px 12px !important;
                border-radius: 8px !important;
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4) !important;
                transition: all 0.5s ease !important;
                transform: scale(1.05) !important;
            `;
            
            // 3秒后恢复正常样式
            setTimeout(() => {
                shippedElement.style.cssText = '';
            }, 3000);
        } else {
            console.log('❌ 找不到已发货量DOM元素');
        }
        
        // 4. 同时更新未发货量显示
        const unshippedElement = document.querySelector('.metric-card.unshipped .metric-value');
        if (unshippedElement && dashboard.data) {
            const oldValue = unshippedElement.textContent;
            unshippedElement.textContent = dashboard.data.unshippedMeters.toFixed(1);
            
            console.log(`✅ 未发货量DOM更新: ${oldValue} -> ${dashboard.data.unshippedMeters.toFixed(1)}`);
            
            // 添加视觉反馈
            unshippedElement.style.cssText = `
                background: linear-gradient(45deg, #f59e0b, #d97706) !important;
                color: white !important;
                font-weight: bold !important;
                padding: 8px 12px !important;
                border-radius: 8px !important;
                box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4) !important;
                transition: all 0.5s ease !important;
            `;
            
            setTimeout(() => {
                unshippedElement.style.cssText = '';
            }, 3000);
        }
        
        // 5. 重写updateMetricsFromDataManager方法，防止再次出现不一致
        console.log('🔧 重写计算方法，防止再次不一致...');
        
        const originalMethod = dashboard.updateMetricsFromDataManager;
        dashboard.updateMetricsFromDataManager = function() {
            console.log('🔄 执行修正的updateMetricsFromDataManager...');
            
            // 调用原方法获取其他数据
            if (originalMethod) {
                originalMethod.call(this);
            }
            
            // 强制使用客户统计数据覆盖已发货量
            const customerStats = dm.calculateCustomerStats();
            const correctShipped = customerStats.reduce((sum, customer) => {
                return sum + (customer.totalMeters || 0);
            }, 0);
            
            // 覆盖计算结果
            this.data.shippedMeters = correctShipped;
            this.data.unshippedMeters = Math.max(0, (this.data.producedMeters || 0) - correctShipped);
            
            console.log(`🔧 已强制使用客户统计数据: ${correctShipped.toFixed(1)}米`);
        };
        
        // 6. 重写updateMetrics方法，确保DOM更新正确
        const originalUpdateMetrics = dashboard.updateMetrics;
        dashboard.updateMetrics = function() {
            // 调用原方法
            if (originalUpdateMetrics) {
                originalUpdateMetrics.call(this);
            }
            
            // 再次确保已发货量显示正确
            const shippedEl = document.querySelector('.metric-card.shipped .metric-value');
            if (shippedEl && this.data && this.data.shippedMeters !== undefined) {
                const currentDisplay = parseFloat(shippedEl.textContent) || 0;
                const correctValue = this.data.shippedMeters;
                
                if (Math.abs(currentDisplay - correctValue) > 0.1) {
                    shippedEl.textContent = correctValue.toFixed(1);
                    console.log(`🔧 修正DOM显示: ${currentDisplay} -> ${correctValue.toFixed(1)}`);
                }
            }
        };
        
        // 7. 立即调用更新方法
        console.log('🔄 立即调用更新方法...');
        dashboard.updateMetricsFromDataManager();
        dashboard.updateMetrics();
        
        // 8. 保存数据
        dm.saveToLocalStorage();
        
        // 9. 验证修复结果
        setTimeout(() => {
            const finalDisplay = document.querySelector('.metric-card.shipped .metric-value')?.textContent || '0';
            const expectedValue = correctShippedMeters.toFixed(1);
            
            console.log('🔍 修复结果验证:');
            console.log(`  期望值: ${expectedValue}米`);
            console.log(`  实际显示: ${finalDisplay}米`);
            
            if (finalDisplay === expectedValue) {
                console.log('🎉 修复成功！主页面与客户详情数据现在一致');
                
                if (dm.showNotification) {
                    dm.showNotification(
                        `✅ 修复成功！已发货量已更新为: ${expectedValue}米`, 
                        'success'
                    );
                }
            } else {
                console.log('⚠️ 修复后仍有差异，可能需要刷新页面');
                
                if (dm.showNotification) {
                    dm.showNotification(
                        '⚠️ 修复后仍有差异，建议刷新页面', 
                        'warning'
                    );
                }
            }
        }, 1000);
        
        console.log('🎉 强制更新完成！');
    }
    
    // 立即执行
    forceUpdateShippedDisplay();
    
    console.log('✅ 强制更新已发货量显示脚本已启动');
    
})();
