# Excel导入功能使用指南

## 🎯 功能概述

系统现已支持直接从Excel文件导入生产计划数据，特别针对"浦东机场肋条C2.xlsx"等Excel模板进行了优化。

## 📊 支持的Excel格式

### 标准格式1（推荐）
| 序列 | 型号 | 长度（mm） | 数量 |
|------|------|------------|------|
| 1 | H100 | 400 | 2 |
| 2 | H100 | 1400 | 2 |
| 3 | H100 | 1600 | 4 |

### 标准格式2（简化）
| 型号 | 长度（mm） | 数量 |
|------|------------|------|
| H100 | 400 | 2 |
| H100 | 1400 | 2 |
| H100 | 1600 | 4 |

### 支持的数据格式
- **型号**: H100, H80（大小写不敏感）
- **长度**: 200-11800mm，必须是200的倍数
- **长度单位**: 支持mm、米制自动转换
- **数量**: 正整数
- **文件格式**: .xlsx, .xls

## 🎮 使用步骤

### 第1步：准备Excel文件
1. 确保Excel文件包含型号、长度、数量三列基本数据
2. 可以包含标题行（系统会自动识别）
3. 数据从第一行或标题行下一行开始

### 第2步：导入操作
1. 点击蓝色"导入Excel"按钮
2. 选择Excel文件（.xlsx或.xls格式）
3. 等待系统解析文件内容

### 第3步：确认导入
1. 系统显示解析结果：
   - 成功解析的记录数
   - 解析失败的记录数
2. 确认导入（注意：会覆盖现有数据）
3. 导入完成后查看结果

## 🔍 数据解析规则

### 自动识别
- **标题行识别**: 自动查找包含"序列"、"型号"、"长度"、"数量"等关键字的行
- **数据起始**: 从标题行下一行或第一行开始解析数据
- **列映射**: 自动适配不同的列顺序

### 数据验证
- **型号验证**: 必须是H100或H80
- **长度验证**: 200-11800mm范围，200mm模数
- **数量验证**: 必须是正整数
- **重复合并**: 相同规格的记录会自动合并数量

### 数据清理
- **空值处理**: 自动跳过空行和无效数据
- **格式清理**: 自动移除多余空格和特殊字符
- **单位转换**: 自动识别米制单位并转换为毫米

## 📋 Excel模板示例

### 浦东机场肋条C2模板
```
序列  型号   长度（mm）  数量
1     H100   400        2
2     H100   1400       2
3     H100   1600       4
4     H100   1800       8
5     H100   2000       68
...
```

### 简化模板
```
型号   长度   数量
H100   400    2
H100   1400   2
H80    800    5
H80    1000   10
...
```

## ⚠️ 注意事项

### 数据要求
- **型号**: 仅支持H100和H80
- **长度**: 必须符合200mm模数（200, 400, 600, 800...）
- **数量**: 必须是正整数
- **区域**: 默认设为C2（可后续修改）

### 导入限制
- **文件大小**: 建议不超过10MB
- **记录数量**: 建议不超过1000条
- **文件格式**: 仅支持.xlsx和.xls格式

### 错误处理
- **格式错误**: 显示具体错误行号和原因
- **数据验证**: 跳过无效数据，继续处理有效记录
- **导入失败**: 保留原有数据，不会丢失

## 🔧 故障排除

### 常见问题

**1. 文件解析失败**
- 检查文件是否为Excel格式（.xlsx/.xls）
- 确认文件没有损坏
- 尝试用Excel重新保存文件

**2. 数据识别失败**
- 确保Excel中有型号、长度、数量三列数据
- 检查数据是否从第一行或标题行下开始
- 确认没有合并单元格

**3. 数据验证失败**
- 型号必须是H100或H80
- 长度必须是200-11800mm范围内的200倍数
- 数量必须是正整数

**4. 导入数量不对**
- 检查Excel中是否有空行
- 确认数据格式正确
- 查看控制台错误信息

### 调试方法
1. **查看控制台**: 按F12查看详细错误信息
2. **检查日志**: 导入后查看操作日志
3. **分步验证**: 先用小文件测试

## 📈 最佳实践

### Excel文件准备
1. **标准化格式**: 使用推荐的列结构
2. **数据清理**: 删除空行和无关数据
3. **格式统一**: 确保数据格式一致

### 导入操作
1. **备份数据**: 导入前先导出现有数据备份
2. **小批量测试**: 先用少量数据测试
3. **验证结果**: 导入后检查数据准确性

### 数据管理
1. **及时保存**: 导入后及时保存
2. **定期备份**: 定期导出数据备份
3. **日志查看**: 定期查看操作日志

## 🚀 快速开始

### 使用现有模板
1. 打开"浦东机场肋条C2.xlsx"文件
2. 点击系统中的"导入Excel"按钮
3. 选择该Excel文件
4. 确认导入结果

### 创建新模板
1. 创建新的Excel文件
2. 设置列标题：型号、长度（mm）、数量
3. 填入数据（H100/H80, 200-11800mm, 正整数）
4. 保存并导入到系统

## 📊 导入结果示例

### 成功导入
```
Excel解析完成！

成功解析: 19 条记录
解析失败: 0 条记录

是否要导入这些数据？
注意：导入将覆盖现有数据。
```

### 部分失败
```
Excel解析完成！

成功解析: 15 条记录
解析失败: 4 条记录

是否要导入这些数据？
注意：导入将覆盖现有数据。
```

## 🎯 功能优势

### 便捷性
- **一键导入**: 直接从Excel导入，无需手动录入
- **格式灵活**: 支持多种Excel格式
- **自动识别**: 智能识别数据结构

### 准确性
- **数据验证**: 严格的数据格式验证
- **错误提示**: 详细的错误信息和行号
- **重复处理**: 自动合并重复规格

### 安全性
- **数据备份**: 导入前可备份现有数据
- **确认机制**: 导入前需要用户确认
- **日志记录**: 完整的操作日志

---

**功能版本**: v2.3.0  
**支持格式**: .xlsx, .xls  
**最大文件**: 10MB  
**状态**: 已完成并可用 ✅
