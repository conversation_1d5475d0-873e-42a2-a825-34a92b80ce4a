<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>区域统一管理工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .section {
            margin: 30px 0;
            padding: 25px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            background: #fafafa;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .area-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .area-item {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            font-weight: 500;
            transition: all 0.2s;
        }
        .area-item:hover {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        .area-item.current {
            background: #dbeafe;
            border-color: #3b82f6;
            color: #1e40af;
        }
        .area-item.missing {
            background: #fef2f2;
            border-color: #f87171;
            color: #dc2626;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .btn-primary { background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%); color: white; }
        .btn-success { background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); color: white; }
        .btn-warning { background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%); color: white; }
        .btn-danger { background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%); color: white; }
        .log-container {
            background: #1a202c;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #374151;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid;
        }
        .alert-info { background: #dbeafe; border-color: #3b82f6; color: #1e40af; }
        .alert-warning { background: #fef3c7; border-color: #f59e0b; color: #92400e; }
        .alert-success { background: #d1fae5; border-color: #10b981; color: #065f46; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
        }
        .stat-label {
            font-size: 12px;
            color: #6b7280;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗺️ 区域统一管理工具</h1>
            <p style="color: #718096; margin: 0;">统一系统中所有区域配置，确保前后一致</p>
        </div>

        <div class="alert alert-info">
            <strong>📋 功能说明：</strong>
            <p style="margin: 10px 0 0 0;">
                此工具将检查系统中所有区域配置，以"各区域生产统计"中的区域为准，
                统一更新新增生产、生产数据管理、导出导入等所有功能的区域列表。
            </p>
        </div>

        <div class="section">
            <div class="section-title">
                📊 当前区域状态分析
            </div>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalAreas">-</div>
                    <div class="stat-label">总区域数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="activeAreas">-</div>
                    <div class="stat-label">有数据区域</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="configuredAreas">-</div>
                    <div class="stat-label">已配置区域</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="missingAreas">-</div>
                    <div class="stat-label">缺失配置</div>
                </div>
            </div>
            <button class="btn btn-primary" onclick="analyzeAreas()">🔍 分析区域状态</button>
        </div>

        <div class="section">
            <div class="section-title">
                🎯 各区域生产统计中的区域（标准）
            </div>
            <div class="area-list" id="standardAreas">
                <div class="area-item">正在检查...</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">
                ⚙️ 系统配置中的区域
            </div>
            <div class="area-list" id="configAreas">
                <div class="area-item">正在检查...</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">
                🔧 统一操作
            </div>
            <button class="btn btn-success" onclick="unifyAllAreas()">
                ✅ 统一所有区域配置
            </button>
            <button class="btn btn-warning" onclick="addNewArea()">
                ➕ 添加新区域
            </button>
            <button class="btn btn-primary" onclick="exportAreaConfig()">
                📤 导出区域配置
            </button>
            <button class="btn btn-primary" onclick="openMainSystem()">
                🏠 打开主系统
            </button>
        </div>

        <div class="section">
            <div class="section-title">
                ➕ 添加新区域
            </div>
            <div class="form-group">
                <label for="newAreaName">区域名称</label>
                <input type="text" id="newAreaName" placeholder="例如：D53F、C4、E2等" 
                       style="text-transform: uppercase;">
            </div>
            <div class="form-group">
                <label for="newAreaDescription">区域描述（可选）</label>
                <input type="text" id="newAreaDescription" placeholder="例如：D53F右区域、C4区域等">
            </div>
            <button class="btn btn-success" onclick="addAreaFromForm()">
                ✅ 添加区域
            </button>
        </div>

        <div class="section" id="logSection" style="display: none;">
            <div class="section-title">
                📝 操作日志
            </div>
            <div id="logContent" class="log-container"></div>
            <button class="btn btn-warning" onclick="clearLog()">🗑️ 清空日志</button>
        </div>
    </div>

    <script>
        let currentAreas = new Set();
        let configAreas = new Set();
        let logVisible = false;

        // 日志功能
        function log(message, type = 'info') {
            const logSection = document.getElementById('logSection');
            const logContent = document.getElementById('logContent');
            
            if (!logVisible) {
                logSection.style.display = 'block';
                logVisible = true;
            }
            
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#4299e1',
                success: '#48bb78',
                warning: '#ed8936',
                error: '#f56565'
            };
            
            const color = colors[type] || colors.info;
            const logEntry = `<div style="color: ${color}; margin: 5px 0;">[${timestamp}] ${message}</div>`;
            logContent.innerHTML += logEntry;
            logContent.scrollTop = logContent.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 分析区域状态
        function analyzeAreas() {
            log('🔍 开始分析区域状态...', 'info');
            
            // 检查本地存储中的数据
            const productionData = JSON.parse(localStorage.getItem('productionData') || '[]');
            const customAreas = JSON.parse(localStorage.getItem('customAreas') || '[]');
            
            // 从生产数据中提取实际使用的区域
            const dataAreas = new Set();
            productionData.forEach(item => {
                if (item.area) {
                    dataAreas.add(item.area);
                }
            });
            
            // 从配置中获取区域
            configAreas = new Set(customAreas);
            
            // 更新显示
            currentAreas = dataAreas;
            updateAreaDisplay('standardAreas', Array.from(dataAreas), '实际使用的区域');
            updateAreaDisplay('configAreas', customAreas, '配置中的区域');
            
            // 更新统计
            const totalAreas = new Set([...dataAreas, ...configAreas]).size;
            const activeAreas = dataAreas.size;
            const configuredAreas = configAreas.size;
            const missingAreas = Array.from(dataAreas).filter(area => !configAreas.has(area)).length;
            
            document.getElementById('totalAreas').textContent = totalAreas;
            document.getElementById('activeAreas').textContent = activeAreas;
            document.getElementById('configuredAreas').textContent = configuredAreas;
            document.getElementById('missingAreas').textContent = missingAreas;
            
            log(`✅ 分析完成：总区域${totalAreas}个，有数据${activeAreas}个，已配置${configuredAreas}个，缺失配置${missingAreas}个`, 'success');
            
            if (missingAreas > 0) {
                const missing = Array.from(dataAreas).filter(area => !configAreas.has(area));
                log(`⚠️ 缺失配置的区域：${missing.join(', ')}`, 'warning');
            }
        }

        // 更新区域显示
        function updateAreaDisplay(containerId, areas, title) {
            const container = document.getElementById(containerId);
            
            if (areas.length === 0) {
                container.innerHTML = '<div class="area-item missing">暂无区域</div>';
                return;
            }
            
            container.innerHTML = '';
            areas.sort().forEach(area => {
                const item = document.createElement('div');
                item.className = 'area-item current';
                item.textContent = area;
                item.title = `${area}区域`;
                container.appendChild(item);
            });
            
            log(`📋 ${title}：${areas.join(', ')}`, 'info');
        }

        // 统一所有区域配置
        function unifyAllAreas() {
            if (currentAreas.size === 0) {
                alert('请先分析区域状态');
                return;
            }
            
            if (!confirm(`确定要统一所有区域配置吗？\n\n这将：\n- 以实际使用的区域为准\n- 更新所有下拉选择框\n- 更新导入导出功能\n- 确保系统一致性\n\n当前将统一 ${currentAreas.size} 个区域`)) {
                return;
            }
            
            log('🔄 开始统一区域配置...', 'info');
            
            try {
                // 1. 更新本地存储中的自定义区域
                const unifiedAreas = Array.from(currentAreas).sort();
                localStorage.setItem('customAreas', JSON.stringify(unifiedAreas));
                log(`✅ 已更新本地存储：${unifiedAreas.join(', ')}`, 'success');
                
                // 2. 如果主系统已加载，更新其区域配置
                if (window.parent && window.parent.dataManager) {
                    window.parent.dataManager.customAreas = new Set(unifiedAreas);
                    if (window.parent.dataManager.updateAreaOptions) {
                        window.parent.dataManager.updateAreaOptions();
                        log('✅ 已更新主系统区域选项', 'success');
                    }
                }
                
                // 3. 重新分析状态
                setTimeout(() => {
                    analyzeAreas();
                    log('✅ 区域配置统一完成！', 'success');
                    alert('区域配置已统一完成！\n\n建议刷新主系统页面以确保所有功能使用最新的区域配置。');
                }, 500);
                
            } catch (error) {
                log(`❌ 统一配置时出错：${error.message}`, 'error');
                alert('统一配置失败：' + error.message);
            }
        }

        // 添加新区域
        function addNewArea() {
            const areaName = prompt('请输入新区域名称（例如：D53F、C4、E2等）：');
            
            if (!areaName) return;
            
            const trimmedName = areaName.trim().toUpperCase();
            
            if (!trimmedName) {
                alert('区域名称不能为空');
                return;
            }
            
            // 验证格式（字母+数字或字母+数字+字母）
            if (!/^[A-Z]\d+[A-Z]*$/.test(trimmedName)) {
                alert('区域名称格式不正确，请使用字母+数字格式（如C1、E3、D53F等）');
                return;
            }
            
            // 检查是否已存在
            if (currentAreas.has(trimmedName) || configAreas.has(trimmedName)) {
                alert(`区域 ${trimmedName} 已存在`);
                return;
            }
            
            // 添加到配置
            const customAreas = JSON.parse(localStorage.getItem('customAreas') || '[]');
            customAreas.push(trimmedName);
            customAreas.sort();
            localStorage.setItem('customAreas', JSON.stringify(customAreas));
            
            log(`✅ 已添加新区域：${trimmedName}`, 'success');
            
            // 重新分析
            analyzeAreas();
            
            alert(`成功添加新区域：${trimmedName}`);
        }

        // 从表单添加区域
        function addAreaFromForm() {
            const nameInput = document.getElementById('newAreaName');
            const descInput = document.getElementById('newAreaDescription');
            
            const areaName = nameInput.value.trim().toUpperCase();
            const description = descInput.value.trim();
            
            if (!areaName) {
                alert('请输入区域名称');
                nameInput.focus();
                return;
            }
            
            // 验证格式
            if (!/^[A-Z]\d+[A-Z]*$/.test(areaName)) {
                alert('区域名称格式不正确，请使用字母+数字格式（如C1、E3、D53F等）');
                nameInput.focus();
                return;
            }
            
            // 检查是否已存在
            if (currentAreas.has(areaName) || configAreas.has(areaName)) {
                alert(`区域 ${areaName} 已存在`);
                return;
            }
            
            // 添加到配置
            const customAreas = JSON.parse(localStorage.getItem('customAreas') || '[]');
            customAreas.push(areaName);
            customAreas.sort();
            localStorage.setItem('customAreas', JSON.stringify(customAreas));
            
            log(`✅ 已添加新区域：${areaName}${description ? ` (${description})` : ''}`, 'success');
            
            // 清空表单
            nameInput.value = '';
            descInput.value = '';
            
            // 重新分析
            analyzeAreas();
            
            alert(`成功添加新区域：${areaName}`);
        }

        // 导出区域配置
        function exportAreaConfig() {
            const config = {
                exportTime: new Date().toISOString(),
                currentAreas: Array.from(currentAreas),
                configAreas: Array.from(configAreas),
                unifiedAreas: Array.from(new Set([...currentAreas, ...configAreas])).sort()
            };
            
            const blob = new Blob([JSON.stringify(config, null, 2)], {
                type: 'application/json'
            });
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `area-config-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('📤 区域配置已导出', 'success');
        }

        // 打开主系统
        function openMainSystem() {
            window.open('index.html', '_blank');
        }

        // 清空日志
        function clearLog() {
            document.getElementById('logContent').innerHTML = '';
            log('🗑️ 日志已清空', 'info');
        }

        // 页面加载时自动分析
        window.addEventListener('DOMContentLoaded', function() {
            log('🚀 区域统一管理工具已加载', 'info');
            setTimeout(analyzeAreas, 500);
        });

        // 监听区域名称输入，自动转换为大写
        document.getElementById('newAreaName').addEventListener('input', function(e) {
            e.target.value = e.target.value.toUpperCase();
        });
    </script>
</body>
</html>
