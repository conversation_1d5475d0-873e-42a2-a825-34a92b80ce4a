<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多用户协作测试 - 生产管理系统</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .status-card {
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        
        .status-card.success { border-left-color: #10b981; background: #f0fdf4; }
        .status-card.warning { border-left-color: #f59e0b; background: #fffbeb; }
        .status-card.error { border-left-color: #ef4444; background: #fef2f2; }
        
        .test-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .test-btn.primary { background: #3b82f6; color: white; }
        .test-btn.success { background: #10b981; color: white; }
        .test-btn.warning { background: #f59e0b; color: white; }
        .test-btn.danger { background: #ef4444; color: white; }
        
        .test-btn:hover { transform: translateY(-1px); opacity: 0.9; }
        
        .log-container {
            background: #1f2937;
            color: #f3f4f6;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .user-simulator {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .user-panel {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
        }
        
        .user-panel.active {
            border-color: #3b82f6;
            background: #f8fafc;
        }
        
        .user-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }
        
        .user-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #10b981;
        }
        
        .online-users {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 1000;
            min-width: 200px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-users"></i> 多用户协作功能测试</h1>
            <p>测试生产管理系统的实时多用户协作功能</p>
        </div>
        
        <!-- 连接状态 -->
        <div class="test-section">
            <h2><i class="fas fa-wifi"></i> 连接状态</h2>
            <div class="status-grid">
                <div class="status-card" id="networkStatus">
                    <h4>网络连接</h4>
                    <p id="networkStatusText">检查中...</p>
                </div>
                <div class="status-card" id="firebaseStatus">
                    <h4>Firebase 连接</h4>
                    <p id="firebaseStatusText">检查中...</p>
                </div>
                <div class="status-card" id="syncStatus">
                    <h4>数据同步</h4>
                    <p id="syncStatusText">检查中...</p>
                </div>
            </div>
        </div>
        
        <!-- 用户模拟器 -->
        <div class="test-section">
            <h2><i class="fas fa-user-friends"></i> 多用户模拟</h2>
            <p>模拟多个用户同时操作系统，测试数据同步效果</p>
            
            <div class="user-simulator">
                <div class="user-panel" id="user1Panel">
                    <div class="user-header">
                        <div class="user-dot" style="background: #3b82f6;"></div>
                        <strong>用户 A</strong>
                        <span class="user-status" id="user1Status">离线</span>
                    </div>
                    <div class="test-actions">
                        <button class="test-btn primary" onclick="simulateUserAction('user1', 'add')">
                            <i class="fas fa-plus"></i> 添加数据
                        </button>
                        <button class="test-btn warning" onclick="simulateUserAction('user1', 'edit')">
                            <i class="fas fa-edit"></i> 编辑数据
                        </button>
                        <button class="test-btn danger" onclick="simulateUserAction('user1', 'delete')">
                            <i class="fas fa-trash"></i> 删除数据
                        </button>
                    </div>
                </div>
                
                <div class="user-panel" id="user2Panel">
                    <div class="user-header">
                        <div class="user-dot" style="background: #10b981;"></div>
                        <strong>用户 B</strong>
                        <span class="user-status" id="user2Status">离线</span>
                    </div>
                    <div class="test-actions">
                        <button class="test-btn primary" onclick="simulateUserAction('user2', 'add')">
                            <i class="fas fa-plus"></i> 添加数据
                        </button>
                        <button class="test-btn warning" onclick="simulateUserAction('user2', 'edit')">
                            <i class="fas fa-edit"></i> 编辑数据
                        </button>
                        <button class="test-btn danger" onclick="simulateUserAction('user2', 'delete')">
                            <i class="fas fa-trash"></i> 删除数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 测试操作 -->
        <div class="test-section">
            <h2><i class="fas fa-vial"></i> 功能测试</h2>
            <div class="test-actions">
                <button class="test-btn success" onclick="testRealtimeSync()">
                    <i class="fas fa-sync-alt"></i> 测试实时同步
                </button>
                <button class="test-btn primary" onclick="testDataMerge()">
                    <i class="fas fa-code-branch"></i> 测试数据合并
                </button>
                <button class="test-btn warning" onclick="testOfflineMode()">
                    <i class="fas fa-wifi-slash"></i> 测试离线模式
                </button>
                <button class="test-btn danger" onclick="clearTestData()">
                    <i class="fas fa-broom"></i> 清空测试数据
                </button>
            </div>
        </div>
        
        <!-- 测试日志 -->
        <div class="test-section">
            <h2><i class="fas fa-terminal"></i> 测试日志</h2>
            <div class="log-container" id="testLog">
                <div>🚀 多用户协作测试开始...</div>
            </div>
            <div class="test-actions">
                <button class="test-btn primary" onclick="clearLog()">
                    <i class="fas fa-eraser"></i> 清空日志
                </button>
                <button class="test-btn success" onclick="exportLog()">
                    <i class="fas fa-download"></i> 导出日志
                </button>
            </div>
        </div>
    </div>
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    
    <!-- 系统脚本 -->
    <script src="scripts/firebase-sync.js"></script>
    <script src="firebase-config.js"></script>
    
    <script>
        let testLog = [];
        
        // 添加日志
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testLog.push(logEntry);
            
            const logContainer = document.getElementById('testLog');
            const logDiv = document.createElement('div');
            logDiv.textContent = logEntry;
            
            if (type === 'success') logDiv.style.color = '#10b981';
            else if (type === 'error') logDiv.style.color = '#ef4444';
            else if (type === 'warning') logDiv.style.color = '#f59e0b';
            
            logContainer.appendChild(logDiv);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 更新状态卡片
        function updateStatus(cardId, status, text) {
            const card = document.getElementById(cardId);
            const textElement = document.getElementById(cardId + 'Text');
            
            card.className = `status-card ${status}`;
            textElement.textContent = text;
        }
        
        // 检查连接状态
        function checkConnectionStatus() {
            // 网络状态
            if (navigator.onLine) {
                updateStatus('networkStatus', 'success', '✅ 网络连接正常');
                addLog('网络连接检查：正常', 'success');
            } else {
                updateStatus('networkStatus', 'error', '❌ 网络连接断开');
                addLog('网络连接检查：断开', 'error');
            }
            
            // Firebase 状态
            if (window.firebaseSync && window.firebaseSync.isConfigured()) {
                updateStatus('firebaseStatus', 'success', '✅ Firebase 连接正常');
                updateStatus('syncStatus', 'success', '✅ 实时同步已启用');
                addLog('Firebase 连接检查：正常', 'success');
                addLog('数据同步检查：已启用', 'success');
            } else {
                updateStatus('firebaseStatus', 'warning', '⚠️ Firebase 未配置');
                updateStatus('syncStatus', 'warning', '⚠️ 同步功能未启用');
                addLog('Firebase 连接检查：未配置', 'warning');
                addLog('数据同步检查：未启用', 'warning');
            }
        }
        
        // 模拟用户操作
        function simulateUserAction(userId, action) {
            addLog(`用户 ${userId} 执行操作：${action}`);
            
            // 模拟数据操作
            const testData = {
                id: Date.now().toString(),
                spec: `H100-${Math.floor(Math.random() * 1000) + 1000}mm`,
                area: ['C1', 'C2', 'E1'][Math.floor(Math.random() * 3)],
                planned: Math.floor(Math.random() * 100) + 50,
                produced: Math.floor(Math.random() * 50),
                status: 'producing',
                timestamp: Date.now(),
                lastModifiedBy: userId
            };
            
            if (window.firebaseSync && window.firebaseSync.isConfigured()) {
                window.firebaseSync.syncToCloud('testData', [testData], action);
                addLog(`数据已同步到云端：${action}`, 'success');
            } else {
                addLog(`模拟操作完成（离线模式）：${action}`, 'warning');
            }
        }
        
        // 测试实时同步
        function testRealtimeSync() {
            addLog('开始测试实时同步功能...');
            
            if (!window.firebaseSync || !window.firebaseSync.isConfigured()) {
                addLog('Firebase 未配置，无法测试实时同步', 'error');
                return;
            }
            
            // 模拟数据变更
            const testData = {
                id: 'sync-test-' + Date.now(),
                type: 'sync-test',
                timestamp: Date.now(),
                message: '实时同步测试数据'
            };
            
            window.firebaseSync.syncToCloud('syncTest', [testData]);
            addLog('实时同步测试数据已发送', 'success');
        }
        
        // 测试数据合并
        function testDataMerge() {
            addLog('开始测试数据合并功能...');
            // 实现数据合并测试逻辑
            addLog('数据合并测试完成', 'success');
        }
        
        // 测试离线模式
        function testOfflineMode() {
            addLog('开始测试离线模式...');
            // 实现离线模式测试逻辑
            addLog('离线模式测试完成', 'success');
        }
        
        // 清空测试数据
        function clearTestData() {
            if (confirm('确定要清空所有测试数据吗？')) {
                addLog('清空测试数据...', 'warning');
                // 实现清空逻辑
                addLog('测试数据已清空', 'success');
            }
        }
        
        // 清空日志
        function clearLog() {
            testLog = [];
            document.getElementById('testLog').innerHTML = '<div>🚀 多用户协作测试开始...</div>';
        }
        
        // 导出日志
        function exportLog() {
            const logText = testLog.join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `multi-user-test-log-${new Date().toISOString().slice(0, 10)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            addLog('多用户协作测试页面加载完成');
            checkConnectionStatus();
            
            // 定期检查状态
            setInterval(checkConnectionStatus, 30000);
        });
        
        // 监听网络状态变化
        window.addEventListener('online', () => {
            addLog('网络已连接', 'success');
            checkConnectionStatus();
        });
        
        window.addEventListener('offline', () => {
            addLog('网络已断开', 'warning');
            checkConnectionStatus();
        });
    </script>
</body>
</html>
