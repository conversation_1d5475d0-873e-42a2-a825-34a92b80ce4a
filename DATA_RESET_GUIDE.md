# 数据清空和重新导入指南

## 🗑️ 数据清空功能

### 📍 清空按钮位置
在数据管理工具栏中，您会看到一个红色的"清空所有数据"按钮：
```
[新增生产] [批量编辑] [导出数据] [导入数据] [操作日志] [清空所有数据]
```

### ⚠️ 清空操作流程

1. **点击清空按钮**
   - 点击红色的"清空所有数据"按钮

2. **第一次确认**
   - 系统会显示详细的警告信息
   - 说明将要删除的内容：
     - 所有生产数据记录
     - 所有发货记录  
     - 所有操作日志
   - 点击"确定"继续，或"取消"放弃操作

3. **第二次确认（安全验证）**
   - 系统要求输入确认文字："确认清空"
   - 必须完全匹配才能执行清空操作
   - 这是为了防止误操作

4. **清空完成**
   - 所有数据被清除
   - 本地存储被清空
   - 界面显示空状态
   - 系统提示"所有数据已清空，您现在可以导入新的数据"

### 🛡️ 安全机制

- **双重确认**：防止误操作
- **文字验证**：需要输入特定文字确认
- **不可撤销警告**：明确告知操作后果
- **操作日志**：清空操作本身也会被记录

## 📥 数据导入功能

### 📋 支持的数据格式

系统支持导入JSON格式的数据文件，标准格式如下：

```json
{
  "exportTime": "2024-06-17T10:30:00.000Z",
  "data": [
    {
      "id": 1,
      "spec": "H100-1000mm",
      "area": "C3",
      "planned": 5000,
      "produced": 3200,
      "status": "producing",
      "deadline": "2024-03-15",
      "remarks": "优先生产项目",
      "shipped": 0,
      "shippingRecords": []
    }
  ]
}
```

### 📊 数据字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | 数字 | 是 | 唯一标识符 |
| spec | 字符串 | 是 | 规格型号（如H100-1000mm） |
| area | 字符串 | 是 | 工地区域 |
| planned | 数字 | 是 | 计划数量 |
| produced | 数字 | 否 | 已生产数量（默认0） |
| status | 字符串 | 否 | 状态（planned/producing/completed/shipped） |
| deadline | 字符串 | 否 | 交付日期（YYYY-MM-DD格式） |
| remarks | 字符串 | 否 | 备注信息 |
| shipped | 数字 | 否 | 已发货数量（默认0） |
| shippingRecords | 数组 | 否 | 发货记录数组 |

### 🎯 导入操作步骤

1. **准备数据文件**
   - 确保数据文件为JSON格式
   - 验证数据结构符合要求
   - 检查规格型号格式（必须为H100-XXXmm或H80-XXXmm）

2. **执行导入**
   - 点击"导入数据"按钮
   - 选择要导入的JSON文件
   - 系统会验证文件格式

3. **确认导入**
   - 系统提示"导入数据将覆盖现有数据，确定继续吗？"
   - 点击"确定"执行导入，或"取消"放弃

4. **导入完成**
   - 数据导入成功后显示提示
   - 表格自动刷新显示新数据
   - 统计数据自动更新

### ⚠️ 导入注意事项

- **数据覆盖**：导入会完全替换现有数据
- **格式验证**：不符合格式的文件会被拒绝
- **规格检查**：规格型号必须符合H100/H80-长度mm格式
- **ID唯一性**：系统会自动处理ID冲突

## 🔄 完整的数据重置流程

### 推荐步骤

1. **备份现有数据（可选）**
   ```
   点击"导出数据" → 保存当前数据作为备份
   ```

2. **清空所有数据**
   ```
   点击"清空所有数据" → 确认警告 → 输入"确认清空"
   ```

3. **准备新数据**
   ```
   准备符合格式要求的JSON数据文件
   ```

4. **导入新数据**
   ```
   点击"导入数据" → 选择文件 → 确认导入
   ```

5. **验证数据**
   ```
   检查导入的数据是否正确
   查看统计数据是否准确
   测试各项功能是否正常
   ```

## 📝 数据准备模板

### 最小数据模板
```json
{
  "exportTime": "2024-06-17T10:30:00.000Z",
  "data": [
    {
      "id": 1,
      "spec": "H100-1000mm",
      "area": "C3",
      "planned": 5000,
      "produced": 0,
      "status": "planned",
      "deadline": "",
      "remarks": "",
      "shipped": 0,
      "shippingRecords": []
    }
  ]
}
```

### 完整数据模板
```json
{
  "exportTime": "2024-06-17T10:30:00.000Z",
  "data": [
    {
      "id": 1,
      "spec": "H100-1000mm",
      "area": "C3",
      "planned": 5000,
      "produced": 3200,
      "status": "producing",
      "deadline": "2024-03-15",
      "remarks": "优先生产项目",
      "shipped": 1000,
      "shippingRecords": [
        {
          "quantity": 1000,
          "date": "2024-02-25",
          "company": "顺丰物流",
          "trackingNumber": "SF1234567890",
          "remarks": "第一批发货",
          "timestamp": "2024-02-25T10:30:00.000Z"
        }
      ]
    }
  ]
}
```

## 🎯 规格型号要求

### 支持的规格格式
- **H100系列**：H100-800mm, H100-1000mm, H100-1200mm, ..., H100-11800mm
- **H80系列**：H80-800mm, H80-1000mm, H80-1200mm, ..., H80-11800mm

### 长度规格要求
- **起始长度**：800mm
- **模数**：200mm
- **最大长度**：11800mm
- **有效长度**：800, 1000, 1200, 1400, ..., 11600, 11800

### 无效格式示例
❌ H120-1000mm（不支持H120型号）
❌ H100-6m（不支持米制单位）
❌ H100-900mm（不符合200mm模数）
❌ H100-12000mm（超过最大长度）

## 🚀 快速开始

### 立即清空并重新开始
1. 点击红色的"清空所有数据"按钮
2. 确认两次警告提示
3. 看到空白表格后，点击"导入数据"
4. 选择您准备好的JSON数据文件
5. 确认导入，开始使用新数据

### 测试数据导入
如果您想先测试导入功能，可以使用上面提供的模板创建一个测试JSON文件，然后按照导入流程操作。

---

**重要提醒**：数据清空操作不可撤销，请确保在清空前已经备份了重要数据！
