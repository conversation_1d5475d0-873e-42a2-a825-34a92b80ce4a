<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>禁用云同步工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-enabled { background: #28a745; }
        .status-disabled { background: #dc3545; }
        .status-unknown { background: #6c757d; }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            transition: all 0.2s;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn:hover { opacity: 0.9; transform: translateY(-1px); }
        .method-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .method-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 10px 0;
        }
        .alert {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 云同步控制工具</h1>
            <p>管理梯桁筋与组合肋生产管理系统的云同步功能</p>
        </div>

        <div class="status-card">
            <h3>📊 当前状态</h3>
            <div id="currentStatus">
                <span class="status-indicator status-unknown"></span>
                <span>正在检查云同步状态...</span>
            </div>
            <div id="statusDetails" style="margin-top: 10px; font-size: 13px; color: #6c757d;"></div>
        </div>

        <div class="alert alert-info">
            <strong>💡 说明：</strong>
            <ul style="margin: 8px 0 0 20px;">
                <li>禁用云同步后，系统将只使用本地存储</li>
                <li>本地数据不会丢失，可以随时重新启用云同步</li>
                <li>禁用期间，多用户协作功能将不可用</li>
                <li>重新启用后，本地数据会自动同步到云端</li>
            </ul>
        </div>

        <div class="method-card">
            <div class="method-title">🎯 快速操作</div>
            <button class="btn btn-danger" onclick="disableCloudSync()">
                🚫 立即禁用云同步
            </button>
            <button class="btn btn-success" onclick="enableCloudSync()">
                ✅ 启用云同步
            </button>
            <button class="btn btn-warning" onclick="checkStatus()">
                🔄 刷新状态
            </button>
        </div>

        <div class="method-card">
            <div class="method-title">🛠️ 高级操作</div>
            <button class="btn btn-primary" onclick="openMainSystem()">
                📱 打开主系统
            </button>
            <button class="btn btn-primary" onclick="openCloudSyncModal()">
                ⚙️ 云同步设置
            </button>
            <button class="btn btn-warning" onclick="clearCloudData()">
                🗑️ 清除云端数据
            </button>
        </div>

        <div class="method-card">
            <div class="method-title">📋 手动方法</div>
            <p>如果按钮操作无效，可以使用以下方法：</p>
            
            <h4>方法1：浏览器控制台</h4>
            <p>1. 按 F12 打开开发者工具</p>
            <p>2. 切换到 Console（控制台）标签</p>
            <p>3. 输入以下命令并按回车：</p>
            <div class="code-block">localStorage.setItem('disableFirebase', 'true'); location.reload();</div>
            
            <h4>方法2：通过主系统界面</h4>
            <p>1. 打开主系统（index.html）</p>
            <p>2. 点击右上角的云同步状态按钮</p>
            <p>3. 在弹出的窗口中找到"禁用云同步"按钮</p>
            
            <h4>方法3：修改配置文件</h4>
            <p>编辑 firebase-config.js 文件，将 disableFirebase 设置为 true：</p>
            <div class="code-block">disableFirebase: true</div>
        </div>

        <div id="logOutput" class="method-card" style="display: none;">
            <div class="method-title">📝 操作日志</div>
            <div id="logContent" class="code-block" style="max-height: 200px; overflow-y: auto;"></div>
        </div>
    </div>

    <script>
        // 日志功能
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const logContent = document.getElementById('logContent');
            
            logOutput.style.display = 'block';
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logContent.textContent += logEntry;
            logContent.scrollTop = logContent.scrollHeight;
            
            console.log(message);
        }

        // 检查当前状态
        function checkStatus() {
            log('🔍 检查云同步状态...');
            
            const isDisabled = localStorage.getItem('disableFirebase') === 'true';
            const statusElement = document.getElementById('currentStatus');
            const detailsElement = document.getElementById('statusDetails');
            
            if (isDisabled) {
                statusElement.innerHTML = '<span class="status-indicator status-disabled"></span><span>云同步已禁用（本地模式）</span>';
                detailsElement.textContent = '系统当前使用本地存储，不会连接到云端服务器';
                log('✅ 状态检查完成：云同步已禁用');
            } else {
                statusElement.innerHTML = '<span class="status-indicator status-enabled"></span><span>云同步已启用</span>';
                detailsElement.textContent = '系统将尝试连接到Firebase云端服务器进行数据同步';
                log('✅ 状态检查完成：云同步已启用');
            }
        }

        // 禁用云同步
        function disableCloudSync() {
            log('🚫 正在禁用云同步...');
            
            try {
                localStorage.setItem('disableFirebase', 'true');
                log('✅ 已设置禁用标志');
                
                // 如果在主系统中，尝试调用相关函数
                if (window.parent && window.parent.firebaseSync) {
                    if (window.parent.firebaseSync.disconnect) {
                        window.parent.firebaseSync.disconnect();
                        log('✅ 已断开Firebase连接');
                    }
                }
                
                checkStatus();
                
                if (confirm('云同步已禁用。是否要刷新页面以应用更改？')) {
                    if (window.parent && window.parent.location) {
                        window.parent.location.reload();
                    } else {
                        location.reload();
                    }
                }
                
                log('✅ 云同步禁用完成');
                
            } catch (error) {
                log('❌ 禁用云同步时出错: ' + error.message);
                alert('操作失败: ' + error.message);
            }
        }

        // 启用云同步
        function enableCloudSync() {
            log('✅ 正在启用云同步...');
            
            try {
                localStorage.removeItem('disableFirebase');
                log('✅ 已移除禁用标志');
                
                checkStatus();
                
                if (confirm('云同步已启用。是否要刷新页面以重新连接？')) {
                    if (window.parent && window.parent.location) {
                        window.parent.location.reload();
                    } else {
                        location.reload();
                    }
                }
                
                log('✅ 云同步启用完成');
                
            } catch (error) {
                log('❌ 启用云同步时出错: ' + error.message);
                alert('操作失败: ' + error.message);
            }
        }

        // 打开主系统
        function openMainSystem() {
            log('📱 打开主系统...');
            window.open('index.html', '_blank');
        }

        // 打开云同步设置
        function openCloudSyncModal() {
            log('⚙️ 尝试打开云同步设置...');
            
            // 尝试在主窗口中打开模态框
            if (window.parent && window.parent.document) {
                const modal = window.parent.document.getElementById('cloudSyncModal');
                if (modal) {
                    modal.style.display = 'block';
                    log('✅ 已打开云同步设置窗口');
                } else {
                    log('⚠️ 未找到云同步设置窗口，请在主系统中操作');
                    openMainSystem();
                }
            } else {
                log('⚠️ 请在主系统中打开云同步设置');
                openMainSystem();
            }
        }

        // 清除云端数据
        function clearCloudData() {
            if (!confirm('⚠️ 确定要清除所有云端数据吗？\n\n这将删除云端的所有生产数据、发货历史和原材料记录。\n本地数据不会受影响。')) {
                return;
            }
            
            log('🗑️ 正在清除云端数据...');
            
            // 尝试调用主系统的清除函数
            if (window.parent && window.parent.clearFirebaseData) {
                window.parent.clearFirebaseData();
                log('✅ 已调用云端数据清除功能');
            } else {
                log('⚠️ 请在主系统中执行清除操作');
                alert('请在主系统中打开控制台，输入：clearFirebaseData()');
                openMainSystem();
            }
        }

        // 页面加载时检查状态
        window.addEventListener('DOMContentLoaded', function() {
            log('🚀 云同步控制工具已加载');
            checkStatus();
        });
    </script>
</body>
</html>
