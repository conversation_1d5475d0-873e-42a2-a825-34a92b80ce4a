<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡片联动测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .btn.danger {
            background: #dc3545;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .metric-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
        }
        .metric-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .metric-unit {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 卡片联动功能测试</h1>
        
        <div class="test-section">
            <h3>📊 当前卡片数据</h3>
            <button class="btn" onclick="loadCurrentData()">加载当前数据</button>
            <button class="btn success" onclick="refreshMainPage()">刷新主页面</button>
            
            <div class="metrics-grid" id="currentMetrics">
                <!-- 当前数据将在这里显示 -->
            </div>
            
            <div class="result info" id="currentDataResult">
                点击"加载当前数据"查看主页面的卡片数值
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔄 联动测试</h3>
            <button class="btn" onclick="testDataUpdate()">测试数据更新</button>
            <button class="btn warning" onclick="testForceRefresh()">强制刷新</button>
            <button class="btn danger" onclick="testEventTrigger()">触发更新事件</button>
            
            <div class="result" id="linkageTestResult">
                选择一个测试操作
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎯 数据一致性检查</h3>
            <button class="btn" onclick="checkDataConsistency()">检查数据一致性</button>
            <button class="btn" onclick="compareCalculations()">对比计算结果</button>
            
            <div class="result" id="consistencyResult">
                点击检查数据一致性
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔧 修复操作</h3>
            <button class="btn success" onclick="fixCardLinkage()">修复卡片联动</button>
            <button class="btn warning" onclick="resetAllCards()">重置所有卡片</button>
            
            <div class="result" id="fixResult">
                选择修复操作
            </div>
        </div>
    </div>

    <script>
        // 获取主窗口引用
        function getMainWindow() {
            if (window.opener) {
                return window.opener;
            } else {
                // 如果是在同一个窗口打开的，尝试获取父窗口
                return window.parent !== window ? window.parent : window;
            }
        }
        
        // 加载当前数据
        function loadCurrentData() {
            const mainWindow = getMainWindow();
            const resultDiv = document.getElementById('currentDataResult');
            const metricsDiv = document.getElementById('currentMetrics');
            
            try {
                const mainDoc = mainWindow.document;
                
                // 获取卡片数据
                const cards = [
                    { name: '总需求量', selector: '.metric-card.total .metric-value', unit: '米' },
                    { name: '已生产量', selector: '.metric-card.produced .metric-value', unit: '米' },
                    { name: '待生产量', selector: '.metric-card.pending .metric-value', unit: '米' },
                    { name: '生产进度', selector: '.metric-card.efficiency .metric-value', unit: '%' },
                    { name: '已发货量', selector: '.metric-card.shipped .metric-value', unit: '米' },
                    { name: '未发货量', selector: '.metric-card.unshipped .metric-value', unit: '米' },
                    { name: '原材料采购', selector: '.metric-card.material .metric-value', unit: '吨' },
                    { name: '库存状态', selector: '.metric-card.inventory .metric-value', unit: '' }
                ];
                
                let metricsHtml = '';
                let dataLog = '当前卡片数据:\n';
                
                cards.forEach(card => {
                    const element = mainDoc.querySelector(card.selector);
                    const value = element ? element.textContent.trim() : '未找到';
                    
                    metricsHtml += `
                        <div class="metric-card">
                            <h4>${card.name}</h4>
                            <div class="metric-value">${value}</div>
                            <div class="metric-unit">${card.unit}</div>
                        </div>
                    `;
                    
                    dataLog += `${card.name}: ${value} ${card.unit}\n`;
                });
                
                metricsDiv.innerHTML = metricsHtml;
                resultDiv.textContent = dataLog;
                resultDiv.className = 'result success';
                
            } catch (error) {
                resultDiv.textContent = `❌ 加载失败: ${error.message}`;
                resultDiv.className = 'result error';
                metricsDiv.innerHTML = '<div class="metric-card">加载失败</div>';
            }
        }
        
        // 刷新主页面
        function refreshMainPage() {
            const mainWindow = getMainWindow();
            const resultDiv = document.getElementById('currentDataResult');
            
            try {
                if (mainWindow.dashboard && typeof mainWindow.dashboard.refreshData === 'function') {
                    mainWindow.dashboard.refreshData();
                    resultDiv.textContent = '✅ 主页面刷新命令已发送';
                    resultDiv.className = 'result success';
                    
                    // 延迟重新加载数据
                    setTimeout(loadCurrentData, 2000);
                } else {
                    resultDiv.textContent = '❌ 无法找到主页面的刷新方法';
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 刷新失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 测试数据更新
        function testDataUpdate() {
            const mainWindow = getMainWindow();
            const resultDiv = document.getElementById('linkageTestResult');
            
            try {
                if (mainWindow.dataManager && typeof mainWindow.dataManager.updateStats === 'function') {
                    mainWindow.dataManager.updateStats();
                    resultDiv.textContent = '✅ dataManager.updateStats() 已调用';
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = '❌ 无法找到 dataManager.updateStats 方法';
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 测试强制刷新
        function testForceRefresh() {
            const mainWindow = getMainWindow();
            const resultDiv = document.getElementById('linkageTestResult');
            
            try {
                if (mainWindow.dashboard && typeof mainWindow.dashboard.updateMetricsFromDataManager === 'function') {
                    mainWindow.dashboard.updateMetricsFromDataManager();
                    resultDiv.textContent = '✅ dashboard.updateMetricsFromDataManager() 已调用';
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = '❌ 无法找到 dashboard.updateMetricsFromDataManager 方法';
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 强制刷新失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 触发更新事件
        function testEventTrigger() {
            const mainWindow = getMainWindow();
            const resultDiv = document.getElementById('linkageTestResult');
            
            try {
                const event = new CustomEvent('dataUpdated', {
                    detail: { source: 'test', timestamp: new Date() }
                });
                mainWindow.document.dispatchEvent(event);
                
                resultDiv.textContent = '✅ dataUpdated 事件已触发';
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ 事件触发失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 检查数据一致性
        function checkDataConsistency() {
            const mainWindow = getMainWindow();
            const resultDiv = document.getElementById('consistencyResult');
            
            try {
                let report = '数据一致性检查报告:\n\n';
                
                // 检查数据源
                const hasDataManager = !!mainWindow.dataManager;
                const hasDataCore = !!mainWindow.dataCore;
                const hasDashboard = !!mainWindow.dashboard;
                
                report += `数据源状态:\n`;
                report += `- dataManager: ${hasDataManager ? '✅' : '❌'}\n`;
                report += `- dataCore: ${hasDataCore ? '✅' : '❌'}\n`;
                report += `- dashboard: ${hasDashboard ? '✅' : '❌'}\n\n`;
                
                // 检查数据数量
                if (hasDataManager) {
                    const dataCount = mainWindow.dataManager.data?.length || 0;
                    const shippingCount = mainWindow.dataManager.shippingHistory?.length || 0;
                    const materialCount = mainWindow.dataManager.materialPurchases?.length || 0;
                    
                    report += `dataManager 数据量:\n`;
                    report += `- 生产数据: ${dataCount} 条\n`;
                    report += `- 发货历史: ${shippingCount} 条\n`;
                    report += `- 原材料: ${materialCount} 条\n\n`;
                }
                
                if (hasDataCore) {
                    const dataCount = mainWindow.dataCore.data?.length || 0;
                    const shippingCount = mainWindow.dataCore.shippingHistory?.length || 0;
                    const materialCount = mainWindow.dataCore.materialPurchases?.length || 0;
                    
                    report += `dataCore 数据量:\n`;
                    report += `- 生产数据: ${dataCount} 条\n`;
                    report += `- 发货历史: ${shippingCount} 条\n`;
                    report += `- 原材料: ${materialCount} 条\n\n`;
                }
                
                resultDiv.textContent = report;
                resultDiv.className = 'result info';
                
            } catch (error) {
                resultDiv.textContent = `❌ 检查失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 对比计算结果
        function compareCalculations() {
            const mainWindow = getMainWindow();
            const resultDiv = document.getElementById('consistencyResult');
            
            try {
                let report = '计算结果对比:\n\n';
                
                if (mainWindow.dashboard && mainWindow.dashboard.data) {
                    const dashboardData = mainWindow.dashboard.data;
                    
                    report += `Dashboard 内部数据:\n`;
                    report += `- 总需求量: ${(dashboardData.totalDemandMeters || 0).toFixed(1)} 米\n`;
                    report += `- 已生产量: ${(dashboardData.producedMeters || 0).toFixed(1)} 米\n`;
                    report += `- 待生产量: ${(dashboardData.pendingMeters || 0).toFixed(1)} 米\n`;
                    report += `- 已发货量: ${(dashboardData.shippedMeters || 0).toFixed(1)} 米\n`;
                    report += `- 未发货量: ${(dashboardData.unshippedMeters || 0).toFixed(1)} 米\n`;
                    report += `- 完成率: ${(dashboardData.completionRate || 0).toFixed(1)}%\n\n`;
                }
                
                // 获取页面显示值
                const mainDoc = mainWindow.document;
                const totalElement = mainDoc.querySelector('.metric-card.total .metric-value');
                const producedElement = mainDoc.querySelector('.metric-card.produced .metric-value');
                const pendingElement = mainDoc.querySelector('.metric-card.pending .metric-value');
                const shippedElement = mainDoc.querySelector('.metric-card.shipped .metric-value');
                const unshippedElement = mainDoc.querySelector('.metric-card.unshipped .metric-value');
                const efficiencyElement = mainDoc.querySelector('.metric-card.efficiency .metric-value');
                
                report += `页面显示值:\n`;
                report += `- 总需求量: ${totalElement ? totalElement.textContent : '未找到'}\n`;
                report += `- 已生产量: ${producedElement ? producedElement.textContent : '未找到'}\n`;
                report += `- 待生产量: ${pendingElement ? pendingElement.textContent : '未找到'}\n`;
                report += `- 已发货量: ${shippedElement ? shippedElement.textContent : '未找到'}\n`;
                report += `- 未发货量: ${unshippedElement ? unshippedElement.textContent : '未找到'}\n`;
                report += `- 生产进度: ${efficiencyElement ? efficiencyElement.textContent : '未找到'}\n`;
                
                resultDiv.textContent = report;
                resultDiv.className = 'result info';
                
            } catch (error) {
                resultDiv.textContent = `❌ 对比失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 修复卡片联动
        function fixCardLinkage() {
            const mainWindow = getMainWindow();
            const resultDiv = document.getElementById('fixResult');
            
            try {
                // 触发修复脚本
                const event = new CustomEvent('dataUpdated', {
                    detail: { source: 'fix', timestamp: new Date() }
                });
                mainWindow.document.dispatchEvent(event);
                
                // 强制更新
                if (mainWindow.dataManager && typeof mainWindow.dataManager.updateStats === 'function') {
                    mainWindow.dataManager.updateStats();
                }
                
                if (mainWindow.dashboard && typeof mainWindow.dashboard.updateMetricsFromDataManager === 'function') {
                    mainWindow.dashboard.updateMetricsFromDataManager();
                }
                
                resultDiv.textContent = '✅ 卡片联动修复操作已执行';
                resultDiv.className = 'result success';
                
                // 延迟重新加载数据验证
                setTimeout(() => {
                    loadCurrentData();
                    resultDiv.textContent += '\n✅ 数据已重新加载，请检查卡片是否正常显示';
                }, 2000);
                
            } catch (error) {
                resultDiv.textContent = `❌ 修复失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 重置所有卡片
        function resetAllCards() {
            const mainWindow = getMainWindow();
            const resultDiv = document.getElementById('fixResult');
            
            try {
                // 重置dashboard数据
                if (mainWindow.dashboard) {
                    mainWindow.dashboard.data = {
                        totalDemandMeters: 0,
                        producedMeters: 0,
                        pendingMeters: 0,
                        shippedMeters: 0,
                        unshippedMeters: 0,
                        materialTons: 0,
                        completionRate: 0
                    };
                    
                    // 强制重新计算
                    if (typeof mainWindow.dashboard.updateMetricsFromDataManager === 'function') {
                        mainWindow.dashboard.updateMetricsFromDataManager();
                    }
                }
                
                resultDiv.textContent = '✅ 所有卡片已重置并重新计算';
                resultDiv.className = 'result success';
                
                setTimeout(loadCurrentData, 1000);
                
            } catch (error) {
                resultDiv.textContent = `❌ 重置失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 页面加载完成后自动加载当前数据
        window.addEventListener('load', () => {
            setTimeout(loadCurrentData, 1000);
        });
    </script>
</body>
</html>
