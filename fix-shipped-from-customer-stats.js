// 从客户发货统计中重新计算已发货量的脚本

(function() {
    'use strict';
    
    console.log('🔧 开始从客户发货统计重新计算已发货量...');
    
    // 等待组件加载
    function waitForComponents() {
        return new Promise((resolve) => {
            const checkComponents = () => {
                if (window.dataManager && window.dashboard) {
                    resolve({ dataManager: window.dataManager, dashboard: window.dashboard });
                } else {
                    setTimeout(checkComponents, 100);
                }
            };
            checkComponents();
        });
    }
    
    // 从客户发货统计重新计算已发货量
    async function recalculateShippedFromCustomerStats() {
        try {
            const { dataManager, dashboard } = await waitForComponents();
            console.log('✅ 组件已加载');
            
            // 1. 获取客户发货统计数据
            console.log('📊 获取客户发货统计数据...');
            const customerStats = dataManager.calculateCustomerStats();
            console.log('客户统计数据:', customerStats);
            
            // 2. 计算总发货量
            let totalShippedMeters = 0;
            let totalShippedQuantity = 0;
            let customerCount = 0;
            
            customerStats.forEach(customer => {
                // 统计所有有发货量的客户，不管是否为预定义客户
                if (customer.totalMeters > 0) {
                    totalShippedMeters += customer.totalMeters;
                    totalShippedQuantity += customer.totalQuantity;
                    customerCount++;
                    console.log(`✅ 客户 ${customer.customerName}: ${customer.totalMeters.toFixed(1)}米, ${customer.totalQuantity}根 ${customer.isPredefined ? '(预定义)' : '(发货记录)'}`);
                }
            });
            
            console.log(`📈 客户发货统计汇总:`);
            console.log(`  有效客户数: ${customerCount}`);
            console.log(`  总发货量: ${totalShippedMeters.toFixed(1)}米`);
            console.log(`  总发货根数: ${totalShippedQuantity}根`);
            
            // 3. 更新仪表板数据
            if (dashboard.data) {
                const oldShipped = dashboard.data.shippedMeters || 0;
                dashboard.data.shippedMeters = totalShippedMeters;
                dashboard.data.unshippedMeters = Math.max(0, (dashboard.data.producedMeters || 0) - totalShippedMeters);
                
                console.log(`🔄 仪表板数据更新:`);
                console.log(`  原已发货量: ${oldShipped.toFixed(1)}米`);
                console.log(`  新已发货量: ${totalShippedMeters.toFixed(1)}米`);
                console.log(`  未发货量: ${dashboard.data.unshippedMeters.toFixed(1)}米`);
            }
            
            // 4. 同步更新生产数据中的shipped字段
            console.log('🔄 同步更新生产数据中的shipped字段...');
            
            // 先重置所有shipped字段
            dataManager.data.forEach(item => {
                item.shipped = 0;
            });
            
            // 从发货历史重新计算shipped字段
            if (dataManager.shippingHistory && dataManager.shippingHistory.length > 0) {
                dataManager.shippingHistory.forEach(record => {
                    if (record.items && Array.isArray(record.items)) {
                        record.items.forEach(shippedItem => {
                            const dataItem = dataManager.data.find(item => 
                                item.spec === shippedItem.spec && item.area === shippedItem.area
                            );
                            if (dataItem) {
                                dataItem.shipped = (dataItem.shipped || 0) + (shippedItem.quantity || 0);
                            }
                        });
                    }
                });
                console.log('✅ 已从发货历史同步shipped字段');
            } else {
                console.log('⚠️ 没有发货历史记录');
            }
            
            // 5. 直接更新DOM元素
            console.log('🎨 更新界面显示...');
            
            const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
            if (shippedElement) {
                // 使用动画更新数值
                animateNumber(shippedElement, totalShippedMeters, 1);
                console.log('✅ 已发货量显示已更新');
            }
            
            const unshippedElement = document.querySelector('.metric-card.unshipped .metric-value');
            if (unshippedElement && dashboard.data) {
                animateNumber(unshippedElement, dashboard.data.unshippedMeters, 1);
                console.log('✅ 未发货量显示已更新');
            }
            
            // 6. 更新客户发货统计总计显示
            const totalShippedMetersSpan = document.getElementById('totalShippedMeters');
            if (totalShippedMetersSpan) {
                totalShippedMetersSpan.textContent = dataManager.formatNumber(totalShippedMeters.toFixed(1)) + ' 米';
                console.log('✅ 客户统计总计已更新');
            }
            
            // 7. 保存数据
            dataManager.saveToLocalStorage();
            
            // 8. 强制刷新仪表板
            setTimeout(() => {
                dashboard.updateMetricsFromDataManager();
                dashboard.updateMetrics();
            }, 500);
            
            console.log('🎉 从客户发货统计重新计算已发货量完成！');
            
            // 9. 显示成功提示
            if (dataManager.showNotification) {
                dataManager.showNotification(
                    `✅ 已发货量已更新！从${customerCount}个客户统计得出: ${totalShippedMeters.toFixed(1)}米`, 
                    'success'
                );
            }
            
            // 10. 验证结果
            setTimeout(() => {
                const finalShipped = document.querySelector('.metric-card.shipped .metric-value')?.textContent || '0';
                console.log('🔍 最终验证 - 已发货量显示:', finalShipped);
                
                if (parseFloat(finalShipped) > 0) {
                    console.log('🎉 修复成功！已发货量不再为0');
                } else {
                    console.log('⚠️ 修复后仍为0，可能需要检查客户发货数据');
                }
            }, 1000);
            
        } catch (error) {
            console.error('❌ 从客户发货统计重新计算已发货量时出错:', error);
        }
    }
    
    // 数字动画函数
    function animateNumber(element, targetValue, decimals = 0) {
        const startValue = parseFloat(element.textContent) || 0;
        const difference = targetValue - startValue;
        const duration = 1000; // 1秒动画
        const steps = 60; // 60帧
        const stepValue = difference / steps;
        let currentStep = 0;
        
        const timer = setInterval(() => {
            currentStep++;
            const currentValue = startValue + (stepValue * currentStep);
            
            if (currentStep >= steps) {
                element.textContent = targetValue.toFixed(decimals);
                clearInterval(timer);
            } else {
                element.textContent = currentValue.toFixed(decimals);
            }
        }, duration / steps);
    }
    
    // 检查客户发货数据
    function checkCustomerShippingData() {
        if (!window.dataManager) {
            console.log('❌ DataManager不存在');
            return;
        }
        
        console.log('🔍 检查客户发货数据...');
        
        const customerStats = window.dataManager.calculateCustomerStats();
        console.log('📊 客户发货统计:');
        
        let totalMeters = 0;
        let validCustomers = 0;
        
        customerStats.forEach((customer, index) => {
            if (customer.totalMeters > 0) {
                validCustomers++;
                totalMeters += customer.totalMeters;
                console.log(`  ${index + 1}. ${customer.customerName}: ${customer.totalMeters.toFixed(1)}米 (${customer.totalQuantity}根) ${customer.isPredefined ? '[预定义]' : '[发货记录]'}`);
            } else {
                console.log(`  ${index + 1}. ${customer.customerName}: 无发货记录 ${customer.isPredefined ? '[预定义]' : '[其他]'}`);
            }
        });
        
        console.log(`📈 汇总: ${validCustomers}个有效客户，总计${totalMeters.toFixed(1)}米`);
        
        return {
            totalCustomers: customerStats.length,
            validCustomers: validCustomers,
            totalMeters: totalMeters,
            customerStats: customerStats
        };
    }
    
    // 强制从客户统计更新发货量
    function forceUpdateFromCustomerStats() {
        console.log('🔄 强制从客户统计更新发货量...');
        
        const checkResult = checkCustomerShippingData();
        if (checkResult && checkResult.totalMeters > 0) {
            recalculateShippedFromCustomerStats();
        } else {
            console.log('⚠️ 没有有效的客户发货数据');
            if (window.dataManager && window.dataManager.showNotification) {
                window.dataManager.showNotification('没有找到有效的客户发货数据', 'warning');
            }
        }
    }
    
    // 添加全局方法
    window.recalculateShippedFromCustomerStats = recalculateShippedFromCustomerStats;
    window.checkCustomerShippingData = checkCustomerShippingData;
    window.forceUpdateFromCustomerStats = forceUpdateFromCustomerStats;
    
    // 页面加载完成后自动执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                console.log('🚀 自动执行客户发货统计修复...');
                recalculateShippedFromCustomerStats();
            }, 4000); // 延迟4秒确保所有组件加载完成
        });
    } else {
        setTimeout(() => {
            console.log('🚀 自动执行客户发货统计修复...');
            recalculateShippedFromCustomerStats();
        }, 4000);
    }
    
    console.log('🚀 客户发货统计修复脚本已加载');
    console.log('💡 可用命令:');
    console.log('  - checkCustomerShippingData() - 检查客户发货数据');
    console.log('  - forceUpdateFromCustomerStats() - 强制从客户统计更新');
    console.log('  - recalculateShippedFromCustomerStats() - 重新计算发货量');
    
})();
