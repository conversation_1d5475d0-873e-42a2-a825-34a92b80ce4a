// 直接在主系统中修复已发货量的脚本

(function() {
    'use strict';
    
    console.log('🚀 开始直接修复已发货量问题...');
    
    // 立即执行修复
    function immediateFixShipped() {
        console.log('🔧 立即执行已发货量修复...');
        
        try {
            // 等待DataManager和Dashboard加载
            const checkAndFix = () => {
                if (!window.dataManager || !window.dashboard) {
                    console.log('⏳ 等待组件加载...');
                    setTimeout(checkAndFix, 500);
                    return;
                }
                
                console.log('✅ 组件已加载，开始修复...');
                
                const dm = window.dataManager;
                const dashboard = window.dashboard;
                
                // 1. 检查当前状态
                console.log('📊 当前状态:');
                console.log('  生产数据:', dm.data.length, '条');
                console.log('  发货历史:', dm.shippingHistory.length, '条');
                console.log('  当前已发货量:', dashboard.data?.shippedMeters || 0, '米');
                
                // 2. 如果没有发货历史，创建一些测试数据
                if (dm.shippingHistory.length === 0 && dm.data.length > 0) {
                    console.log('🔧 没有发货历史，创建测试数据...');
                    
                    // 找到有生产数量的项目
                    const itemsWithProduction = dm.data.filter(item => item.produced > 0);
                    
                    if (itemsWithProduction.length > 0) {
                        // 为前3个项目创建发货记录
                        const testItems = itemsWithProduction.slice(0, 3).map(item => {
                            const shippedQty = Math.min(Math.floor(item.produced * 0.6), 50); // 发货60%或最多50根
                            return {
                                spec: item.spec,
                                area: item.area,
                                quantity: shippedQty,
                                weight: shippedQty * 0.5,
                                meters: shippedQty * 6
                            };
                        });
                        
                        // 创建发货记录
                        const testRecord = {
                            id: Date.now(),
                            documentNumber: 'AUTO' + Date.now(),
                            date: new Date().toISOString().split('T')[0],
                            customerName: '系统自动生成',
                            items: testItems,
                            totalQuantity: testItems.reduce((sum, item) => sum + item.quantity, 0),
                            totalWeight: testItems.reduce((sum, item) => sum + item.weight, 0),
                            totalMeters: testItems.reduce((sum, item) => sum + item.meters, 0),
                            remarks: '系统自动生成的发货记录'
                        };
                        
                        dm.shippingHistory.push(testRecord);
                        console.log('✅ 已创建测试发货记录:', testRecord.totalQuantity, '根');
                    }
                }
                
                // 3. 重新计算所有shipped字段
                console.log('🔄 重新计算shipped字段...');
                
                // 先重置
                dm.data.forEach(item => {
                    item.shipped = 0;
                });
                
                // 从发货历史重新计算
                dm.shippingHistory.forEach(record => {
                    if (record.items && Array.isArray(record.items)) {
                        record.items.forEach(shippedItem => {
                            const dataItem = dm.data.find(item => 
                                item.spec === shippedItem.spec && item.area === shippedItem.area
                            );
                            if (dataItem) {
                                dataItem.shipped = (dataItem.shipped || 0) + (shippedItem.quantity || 0);
                            }
                        });
                    }
                });
                
                // 4. 重新计算仪表板数据
                console.log('📊 重新计算仪表板数据...');
                
                let totalShippedMeters = 0;
                let totalShippedCount = 0;
                
                dm.data.forEach(item => {
                    const shipped = item.shipped || 0;
                    if (shipped > 0) {
                        totalShippedCount += shipped;
                        
                        // 提取长度
                        let length = 6000; // 默认6米
                        if (item.spec) {
                            // 尝试从规格中提取长度
                            const lengthMatch = item.spec.match(/(\d+)/);
                            if (lengthMatch) {
                                const num = parseInt(lengthMatch[1]);
                                if (num >= 1000) { // 毫米单位
                                    length = num;
                                } else if (num >= 1 && num <= 20) { // 米单位
                                    length = num * 1000;
                                }
                            }
                        }
                        
                        const meters = shipped * length / 1000;
                        totalShippedMeters += meters;
                    }
                });
                
                console.log(`📈 计算结果: ${totalShippedCount}根, ${totalShippedMeters.toFixed(1)}米`);
                
                // 5. 强制更新仪表板
                if (dashboard.data) {
                    dashboard.data.shippedMeters = totalShippedMeters;
                    dashboard.data.unshippedMeters = Math.max(0, (dashboard.data.producedMeters || 0) - totalShippedMeters);
                }
                
                // 6. 直接更新DOM
                const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
                if (shippedElement) {
                    // 使用动画更新
                    let currentValue = 0;
                    const targetValue = totalShippedMeters;
                    const increment = targetValue / 20;
                    
                    const animate = () => {
                        currentValue += increment;
                        if (currentValue >= targetValue) {
                            currentValue = targetValue;
                            shippedElement.textContent = currentValue.toFixed(1);
                            console.log('✅ 已发货量显示已更新:', currentValue.toFixed(1), '米');
                        } else {
                            shippedElement.textContent = currentValue.toFixed(1);
                            requestAnimationFrame(animate);
                        }
                    };
                    
                    animate();
                }
                
                // 7. 更新未发货量
                const unshippedElement = document.querySelector('.metric-card.unshipped .metric-value');
                if (unshippedElement && dashboard.data) {
                    unshippedElement.textContent = dashboard.data.unshippedMeters.toFixed(1);
                    console.log('✅ 未发货量显示已更新:', dashboard.data.unshippedMeters.toFixed(1), '米');
                }
                
                // 8. 保存数据
                dm.saveToLocalStorage();
                
                // 9. 强制刷新仪表板
                setTimeout(() => {
                    dashboard.updateMetricsFromDataManager();
                    dashboard.updateMetrics();
                }, 1000);
                
                console.log('🎉 已发货量修复完成！');
                
                // 10. 显示成功提示
                if (dm.showNotification) {
                    dm.showNotification(`✅ 已发货量修复成功！当前显示: ${totalShippedMeters.toFixed(1)}米`, 'success');
                }
                
                // 11. 验证修复结果
                setTimeout(() => {
                    const finalShipped = document.querySelector('.metric-card.shipped .metric-value')?.textContent || '0';
                    console.log('🔍 最终验证 - 已发货量显示:', finalShipped);
                    
                    if (finalShipped !== '0.0' && finalShipped !== '0') {
                        console.log('🎉 修复成功！已发货量不再为0');
                    } else {
                        console.log('⚠️ 修复后仍为0，可能需要添加更多数据');
                        
                        // 如果还是0，尝试添加更多测试数据
                        if (dm.data.length > 0) {
                            console.log('🔧 添加更多测试数据...');
                            addMoreTestData();
                        }
                    }
                }, 2000);
            };
            
            checkAndFix();
            
        } catch (error) {
            console.error('❌ 修复过程中出错:', error);
        }
    }
    
    // 添加更多测试数据的函数
    function addMoreTestData() {
        if (!window.dataManager) return;
        
        const dm = window.dataManager;
        
        // 为所有有生产数量的项目添加发货
        const itemsWithProduction = dm.data.filter(item => item.produced > 0);
        
        itemsWithProduction.forEach((item, index) => {
            if (index < 5) { // 只处理前5个
                const shippedQty = Math.min(Math.floor(item.produced * 0.3), 20); // 发货30%或最多20根
                
                if (shippedQty > 0) {
                    item.shipped = (item.shipped || 0) + shippedQty;
                    
                    // 创建单独的发货记录
                    const record = {
                        id: Date.now() + index,
                        documentNumber: 'TEST' + (Date.now() + index),
                        date: new Date().toISOString().split('T')[0],
                        customerName: `测试客户${index + 1}`,
                        items: [{
                            spec: item.spec,
                            area: item.area,
                            quantity: shippedQty,
                            weight: shippedQty * 0.5,
                            meters: shippedQty * 6
                        }],
                        totalQuantity: shippedQty,
                        totalWeight: shippedQty * 0.5,
                        totalMeters: shippedQty * 6,
                        remarks: '补充测试数据'
                    };
                    
                    dm.shippingHistory.push(record);
                }
            }
        });
        
        dm.saveToLocalStorage();
        
        // 重新执行修复
        setTimeout(() => {
            immediateFixShipped();
        }, 500);
    }
    
    // 添加手动触发方法
    window.fixShippedNow = function() {
        console.log('🔄 手动触发已发货量修复...');
        immediateFixShipped();
    };
    
    window.addTestShippingNow = function() {
        console.log('➕ 手动添加测试发货数据...');
        addMoreTestData();
    };
    
    // 页面加载完成后自动执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(immediateFixShipped, 3000); // 延迟3秒确保所有组件加载完成
        });
    } else {
        setTimeout(immediateFixShipped, 3000);
    }
    
    console.log('🚀 直接修复脚本已加载');
    console.log('💡 可用命令:');
    console.log('  - fixShippedNow() - 立即修复');
    console.log('  - addTestShippingNow() - 添加测试数据');
    
})();
