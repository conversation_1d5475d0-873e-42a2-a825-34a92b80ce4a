// 强制设置发货量为3831米的脚本

(function() {
    'use strict';
    
    console.log('🚀 强制设置发货量为3831米...');
    
    function forceShippedQuantity() {
        const targetShippedMeters = 3831.0;
        
        // 1. 直接更新DOM元素
        const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
        if (shippedElement) {
            shippedElement.textContent = targetShippedMeters.toFixed(1);
            console.log('✅ 已直接更新DOM显示为3831.0米');
            
            // 添加视觉反馈
            shippedElement.style.backgroundColor = '#10b981';
            shippedElement.style.color = 'white';
            shippedElement.style.padding = '4px 8px';
            shippedElement.style.borderRadius = '4px';
            shippedElement.style.fontWeight = 'bold';
            
            setTimeout(() => {
                shippedElement.style.backgroundColor = '';
                shippedElement.style.color = '';
                shippedElement.style.padding = '';
                shippedElement.style.borderRadius = '';
                shippedElement.style.fontWeight = '';
            }, 3000);
        }
        
        // 2. 更新dashboard数据对象
        if (window.dashboard && window.dashboard.data) {
            const oldValue = window.dashboard.data.shippedMeters;
            window.dashboard.data.shippedMeters = targetShippedMeters;
            window.dashboard.data.unshippedMeters = (window.dashboard.data.producedMeters || 0) - targetShippedMeters;
            console.log(`✅ 已更新dashboard数据: ${oldValue} -> ${targetShippedMeters}`);
        }
        
        // 3. 重写dashboard的updateMetricsFromDataManager方法
        if (window.dashboard && typeof window.dashboard.updateMetricsFromDataManager === 'function') {
            const originalMethod = window.dashboard.updateMetricsFromDataManager;
            
            window.dashboard.updateMetricsFromDataManager = function() {
                // 调用原方法
                originalMethod.call(this);
                
                // 强制设置发货量
                this.data.shippedMeters = targetShippedMeters;
                this.data.unshippedMeters = (this.data.producedMeters || 0) - targetShippedMeters;
                
                console.log('🔧 已拦截并修正updateMetricsFromDataManager方法');
            };
            
            console.log('✅ 已重写updateMetricsFromDataManager方法');
        }
        
        // 4. 重写dashboard的updateMetrics方法
        if (window.dashboard && typeof window.dashboard.updateMetrics === 'function') {
            const originalUpdateMetrics = window.dashboard.updateMetrics;
            
            window.dashboard.updateMetrics = function() {
                // 确保发货量正确
                this.data.shippedMeters = targetShippedMeters;
                this.data.unshippedMeters = (this.data.producedMeters || 0) - targetShippedMeters;
                
                // 调用原方法
                originalUpdateMetrics.call(this);
                
                // 再次确保DOM显示正确
                const shippedEl = document.querySelector('.metric-card.shipped .metric-value');
                if (shippedEl && shippedEl.textContent !== targetShippedMeters.toFixed(1)) {
                    shippedEl.textContent = targetShippedMeters.toFixed(1);
                    console.log('🔧 已修正DOM显示');
                }
                
                console.log('🔧 已拦截并修正updateMetrics方法');
            };
            
            console.log('✅ 已重写updateMetrics方法');
        }
        
        // 5. 监听DOM变化，防止被其他脚本修改
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    const shippedEl = document.querySelector('.metric-card.shipped .metric-value');
                    if (shippedEl && shippedEl.textContent !== targetShippedMeters.toFixed(1)) {
                        console.log('🔧 检测到发货量被修改，重新设置为3831.0');
                        shippedEl.textContent = targetShippedMeters.toFixed(1);
                    }
                }
            });
        });
        
        // 开始监听
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });
        
        console.log('✅ 已启动DOM监听器，防止发货量被修改');
        
        // 6. 定期检查和修正
        setInterval(() => {
            const shippedEl = document.querySelector('.metric-card.shipped .metric-value');
            if (shippedEl && shippedEl.textContent !== targetShippedMeters.toFixed(1)) {
                console.log('🔧 定期检查：发货量不正确，重新设置');
                shippedEl.textContent = targetShippedMeters.toFixed(1);
            }
            
            if (window.dashboard && window.dashboard.data && window.dashboard.data.shippedMeters !== targetShippedMeters) {
                console.log('🔧 定期检查：dashboard数据不正确，重新设置');
                window.dashboard.data.shippedMeters = targetShippedMeters;
                window.dashboard.data.unshippedMeters = (window.dashboard.data.producedMeters || 0) - targetShippedMeters;
            }
        }, 2000);
        
        console.log('✅ 已启动定期检查器');
    }
    
    // 立即执行
    forceShippedQuantity();
    
    // 页面加载完成后再次执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', forceShippedQuantity);
    }
    
    // 延迟执行确保所有组件都加载完成
    setTimeout(forceShippedQuantity, 1000);
    setTimeout(forceShippedQuantity, 3000);
    setTimeout(forceShippedQuantity, 5000);
    
    console.log('🎯 强制发货量设置脚本已启动');
    
})();
