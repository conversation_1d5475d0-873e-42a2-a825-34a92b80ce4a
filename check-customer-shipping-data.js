// 检查客户发货数据状态
// 诊断为什么客户统计返回0

(function() {
    'use strict';
    
    console.log('🔍 检查客户发货数据状态...');
    
    function checkCustomerShippingData() {
        if (!window.dataManager) {
            console.log('⏳ 等待dataManager加载...');
            setTimeout(checkCustomerShippingData, 500);
            return;
        }
        
        const dm = window.dataManager;
        
        console.log('='.repeat(60));
        console.log('🔍 客户发货数据诊断报告');
        console.log('='.repeat(60));
        
        // 1. 检查customerShippingData
        console.log('\n📊 1. 检查customerShippingData:');
        console.log('-'.repeat(40));
        
        if (dm.customerShippingData) {
            console.log(`✅ customerShippingData存在，包含 ${dm.customerShippingData.length} 条记录`);
            
            if (dm.customerShippingData.length > 0) {
                console.log('前5条记录:');
                dm.customerShippingData.slice(0, 5).forEach((record, index) => {
                    console.log(`  ${index + 1}. 客户: ${record.customerName}, 数量: ${record.quantity}根, 重量: ${record.weight}kg, 米数: ${record.meters}米`);
                });
                
                // 统计总计
                const totalRecords = dm.customerShippingData.length;
                const totalQuantity = dm.customerShippingData.reduce((sum, record) => sum + (record.quantity || 0), 0);
                const totalWeight = dm.customerShippingData.reduce((sum, record) => sum + (record.weight || 0), 0);
                const totalMeters = dm.customerShippingData.reduce((sum, record) => sum + (record.meters || 0), 0);
                
                console.log(`📈 汇总统计:`);
                console.log(`  总记录数: ${totalRecords}`);
                console.log(`  总数量: ${totalQuantity}根`);
                console.log(`  总重量: ${totalWeight.toFixed(1)}kg`);
                console.log(`  总米数: ${totalMeters.toFixed(1)}米`);
            } else {
                console.log('⚠️ customerShippingData为空数组');
            }
        } else {
            console.log('❌ customerShippingData不存在');
        }
        
        // 2. 检查calculateCustomerStats方法
        console.log('\n🧮 2. 检查calculateCustomerStats方法:');
        console.log('-'.repeat(40));
        
        if (typeof dm.calculateCustomerStats === 'function') {
            console.log('✅ calculateCustomerStats方法存在');
            
            try {
                const customerStats = dm.calculateCustomerStats();
                console.log(`✅ 方法执行成功，返回 ${customerStats.length} 个客户统计`);
                
                if (customerStats.length > 0) {
                    console.log('客户统计详情:');
                    customerStats.forEach(customer => {
                        console.log(`  ${customer.customerName}: ${customer.totalMeters.toFixed(1)}米, ${customer.totalQuantity}根, ${customer.totalWeight.toFixed(1)}kg`);
                    });
                    
                    const totalMetersFromStats = customerStats.reduce((sum, customer) => sum + (customer.totalMeters || 0), 0);
                    console.log(`📈 统计总米数: ${totalMetersFromStats.toFixed(1)}米`);
                } else {
                    console.log('⚠️ calculateCustomerStats返回空数组');
                }
            } catch (error) {
                console.error('❌ calculateCustomerStats执行失败:', error);
            }
        } else {
            console.log('❌ calculateCustomerStats方法不存在');
        }
        
        // 3. 检查localStorage中的客户发货数据
        console.log('\n💾 3. 检查localStorage中的客户发货数据:');
        console.log('-'.repeat(40));
        
        try {
            const storedShippingData = localStorage.getItem('customerShippingData');
            if (storedShippingData) {
                const parsedData = JSON.parse(storedShippingData);
                console.log(`✅ localStorage中有客户发货数据，包含 ${parsedData.length} 条记录`);
                
                if (parsedData.length > 0) {
                    const totalMetersInStorage = parsedData.reduce((sum, record) => sum + (record.meters || 0), 0);
                    console.log(`📈 localStorage中总米数: ${totalMetersInStorage.toFixed(1)}米`);
                }
            } else {
                console.log('❌ localStorage中没有customerShippingData');
            }
        } catch (error) {
            console.error('❌ 读取localStorage失败:', error);
        }
        
        // 4. 检查是否有历史数据
        console.log('\n📚 4. 检查历史数据:');
        console.log('-'.repeat(40));
        
        // 检查可能的历史数据键
        const historyKeys = [
            'customerShippingHistory',
            'shippingHistory',
            'customerData',
            'shippingData'
        ];
        
        historyKeys.forEach(key => {
            try {
                const data = localStorage.getItem(key);
                if (data) {
                    const parsed = JSON.parse(data);
                    if (Array.isArray(parsed) && parsed.length > 0) {
                        console.log(`✅ 找到历史数据 ${key}: ${parsed.length} 条记录`);
                    }
                }
            } catch (error) {
                // 忽略解析错误
            }
        });
        
        // 5. 提供修复建议
        console.log('\n🔧 5. 修复建议:');
        console.log('-'.repeat(40));
        
        if (!dm.customerShippingData || dm.customerShippingData.length === 0) {
            console.log('❌ 客户发货数据缺失，建议:');
            console.log('1. 检查是否有客户发货记录');
            console.log('2. 尝试重新加载数据');
            console.log('3. 检查数据导入是否正确');
            
            // 尝试恢复数据
            console.log('\n🔄 尝试恢复客户发货数据...');
            
            // 检查是否有备份数据
            const backupKeys = Object.keys(localStorage).filter(key => 
                key.includes('shipping') || key.includes('customer')
            );
            
            if (backupKeys.length > 0) {
                console.log('找到可能的备份数据键:', backupKeys);
                
                // 尝试从最可能的键恢复数据
                for (const key of backupKeys) {
                    try {
                        const data = JSON.parse(localStorage.getItem(key));
                        if (Array.isArray(data) && data.length > 0 && data[0].customerName) {
                            console.log(`🔄 尝试从 ${key} 恢复数据...`);
                            dm.customerShippingData = data;
                            dm.saveToLocalStorage();
                            console.log(`✅ 已从 ${key} 恢复 ${data.length} 条客户发货记录`);
                            break;
                        }
                    } catch (error) {
                        // 继续尝试下一个
                    }
                }
            }
        } else {
            console.log('✅ 客户发货数据正常');
        }
        
        // 6. 最终验证
        console.log('\n🔍 6. 最终验证:');
        console.log('-'.repeat(40));
        
        setTimeout(() => {
            if (typeof dm.calculateCustomerStats === 'function') {
                try {
                    const finalStats = dm.calculateCustomerStats();
                    const finalTotal = finalStats.reduce((sum, customer) => sum + (customer.totalMeters || 0), 0);
                    
                    console.log(`🎯 最终客户统计结果: ${finalTotal.toFixed(1)}米`);
                    
                    if (finalTotal > 0) {
                        console.log('🎉 客户发货数据恢复成功！');
                        
                        // 触发主页面更新
                        if (window.dashboard && typeof window.dashboard.updateMetricsFromDataManager === 'function') {
                            window.dashboard.updateMetricsFromDataManager();
                            window.dashboard.updateMetrics();
                            console.log('🔄 已触发主页面数据更新');
                        }
                        
                        if (dm.showNotification) {
                            dm.showNotification(
                                `✅ 客户发货数据恢复成功！总发货量: ${finalTotal.toFixed(1)}米`, 
                                'success'
                            );
                        }
                    } else {
                        console.log('⚠️ 客户发货数据仍为0，可能需要手动添加发货记录');
                        
                        if (dm.showNotification) {
                            dm.showNotification(
                                '⚠️ 客户发货数据为空，请添加发货记录', 
                                'warning'
                            );
                        }
                    }
                } catch (error) {
                    console.error('❌ 最终验证失败:', error);
                }
            }
        }, 1000);
        
        console.log('\n' + '='.repeat(60));
        console.log('🔍 客户发货数据诊断完成');
        console.log('='.repeat(60));
    }
    
    // 立即执行
    checkCustomerShippingData();
    
    console.log('✅ 客户发货数据检查脚本已启动');
    
})();
