# 梯桁筋与组合肋生产管理系统 - 局域网部署指南

## 🎯 部署目标
让团队成员在局域网内通过浏览器直接访问和使用生产管理系统。

## 📋 部署方案对比

| 方案 | 难度 | 稳定性 | 适用场景 |
|------|------|--------|----------|
| NAS Web服务 | ⭐⭐ | ⭐⭐⭐⭐⭐ | 有群晖/威联通等NAS |
| 网盘直接访问 | ⭐ | ⭐⭐⭐ | 支持Web的网盘 |
| 电脑HTTP服务 | ⭐⭐ | ⭐⭐⭐ | 有一台常开电脑 |
| 路由器USB | ⭐⭐⭐ | ⭐⭐⭐⭐ | 高端路由器 |

## 🚀 推荐方案

### 方案1：群晖/威联通NAS部署

#### **群晖NAS (Synology):**
1. 打开 `控制面板` → `终端机和SNMP` → 启用SSH
2. 打开 `套件中心` → 安装 `Web Station`
3. 将项目文件夹复制到 `/volume1/web/` 目录
4. 访问地址：`http://群晖IP/项目文件夹名/`

#### **威联通NAS (QNAP):**
1. 打开 `控制台` → `应用服务` → `Web服务器` → 启用
2. 将项目文件夹复制到 `/share/Web/` 目录
3. 访问地址：`http://威联通IP/项目文件夹名/`

### 方案2：Windows电脑HTTP服务

#### **步骤：**
1. 将项目文件夹放在一台常开的Windows电脑上
2. 双击运行 `start-server.bat`
3. 记录显示的IP地址（如：http://*************:8080）
4. 团队成员通过此地址访问

#### **优点：**
- 部署简单，一键启动
- 自动显示访问地址
- 支持所有设备访问

### 方案3：Mac/Linux电脑HTTP服务

#### **步骤：**
1. 打开终端，进入项目目录
2. 运行：`chmod +x start-server.sh && ./start-server.sh`
3. 或直接运行：`python3 -m http.server 8080`
4. 团队成员通过显示的地址访问

## 📱 团队使用方式

### **电脑访问：**
- 打开浏览器，输入：`http://服务器IP:端口/`
- 推荐使用Chrome、Firefox、Safari、Edge

### **手机/平板访问：**
- 连接同一WiFi网络
- 打开浏览器，输入相同地址
- 可添加到主屏幕，像APP一样使用

## 💾 数据管理策略

### **个人数据模式（推荐）：**
- 每个人的数据存储在各自浏览器中
- 通过Excel导入导出进行数据共享
- 数据安全，不会相互覆盖

### **共享数据模式：**
- 可以启用Firebase云同步
- 所有人共享同一套数据
- 需要网络连接到Firebase

## 🔧 部署检查清单

### **部署前准备：**
- [ ] 确认所有团队成员在同一局域网
- [ ] 选择一台稳定的服务器设备
- [ ] 测试服务器设备的网络连通性
- [ ] 准备项目文件（完整文件夹）

### **部署后测试：**
- [ ] 服务器本机能正常访问
- [ ] 其他电脑能正常访问
- [ ] 手机能正常访问
- [ ] 所有功能正常工作
- [ ] Excel导入导出功能正常

## 🛠️ 常见问题解决

### **无法访问？**
1. 检查防火墙设置，允许对应端口
2. 确认服务器和客户端在同一网络
3. 尝试关闭Windows防火墙测试
4. 检查路由器是否有访问限制

### **页面显示异常？**
1. 清除浏览器缓存
2. 尝试无痕模式访问
3. 检查是否有广告拦截插件干扰
4. 更换浏览器测试

### **数据丢失？**
1. 定期导出Excel备份
2. 检查浏览器存储空间
3. 避免清除浏览器数据
4. 建议每天备份重要数据

## 📞 技术支持

### **自助诊断命令：**
在浏览器控制台运行：
```javascript
// 检查系统状态
checkData()

// 备份数据
backupData()

// 查看版本信息
console.log('系统版本:', document.title)
```

### **日志查看：**
- 按F12打开开发者工具
- 查看Console标签页的错误信息
- 截图发送给技术支持

## 🎉 部署完成

部署成功后，团队成员可以：
- 📊 实时查看生产数据和统计
- 📝 录入生产和发货信息
- 📈 查看各种分析图表
- 📱 在手机上随时查看数据
- 💾 导入导出Excel数据

---

**注意：** 此系统为纯前端应用，无需数据库，部署简单，维护方便。
