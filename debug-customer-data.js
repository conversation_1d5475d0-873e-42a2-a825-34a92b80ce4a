// 调试客户发货数据的脚本

(function() {
    'use strict';
    
    console.log('🔍 开始调试客户发货数据...');
    
    function debugCustomerData() {
        if (!window.dataManager) {
            console.log('❌ DataManager不存在，等待加载...');
            setTimeout(debugCustomerData, 1000);
            return;
        }
        
        console.log('✅ DataManager已加载');
        
        // 1. 检查calculateCustomerStats方法
        if (typeof window.dataManager.calculateCustomerStats === 'function') {
            console.log('✅ calculateCustomerStats方法存在');
            
            try {
                const customerStats = window.dataManager.calculateCustomerStats();
                console.log('📊 客户统计结果:', customerStats);
                
                let totalMeters = 0;
                customerStats.forEach(customer => {
                    console.log(`客户: ${customer.customerName}, 发货量: ${customer.totalMeters}米`);
                    totalMeters += customer.totalMeters || 0;
                });
                
                console.log(`📈 客户统计总发货量: ${totalMeters.toFixed(1)}米`);
                
                // 2. 检查发货历史记录
                if (window.dataManager.shippingHistory) {
                    console.log('📦 发货历史记录数量:', window.dataManager.shippingHistory.length);
                    
                    let historyTotal = 0;
                    window.dataManager.shippingHistory.forEach(record => {
                        console.log('发货记录:', record);
                        if (record.items) {
                            record.items.forEach(item => {
                                const meters = item.quantity * item.length / 1000;
                                historyTotal += meters;
                            });
                        }
                    });
                    console.log(`📈 发货历史总计: ${historyTotal.toFixed(1)}米`);
                } else {
                    console.log('❌ 没有发货历史记录');
                }
                
                // 3. 强制更新dashboard
                if (window.dashboard) {
                    console.log('🔄 强制更新dashboard...');
                    window.dashboard.data.shippedMeters = totalMeters;
                    window.dashboard.updateMetrics();
                    
                    // 直接更新DOM
                    const shippedElement = document.querySelector('.metric-card.shipped .metric-value');
                    if (shippedElement) {
                        shippedElement.textContent = totalMeters.toFixed(1);
                        shippedElement.style.backgroundColor = '#10b981';
                        shippedElement.style.color = 'white';
                        shippedElement.style.padding = '4px 8px';
                        shippedElement.style.borderRadius = '4px';
                        
                        setTimeout(() => {
                            shippedElement.style.backgroundColor = '';
                            shippedElement.style.color = '';
                            shippedElement.style.padding = '';
                            shippedElement.style.borderRadius = '';
                        }, 3000);
                    }
                    
                    console.log(`✅ 已强制更新发货量为: ${totalMeters.toFixed(1)}米`);
                }
                
            } catch (error) {
                console.error('❌ 调用calculateCustomerStats时出错:', error);
            }
        } else {
            console.log('❌ calculateCustomerStats方法不存在');
        }
        
        // 4. 检查生产数据中的shipped字段
        console.log('🏭 检查生产数据中的shipped字段:');
        let productionShipped = 0;
        let shippedCount = 0;
        
        if (window.dataManager.data) {
            window.dataManager.data.forEach((item, index) => {
                if (item.shipped && item.shipped > 0) {
                    shippedCount++;
                    const length = window.dataManager.extractLengthFromSpec ? 
                        window.dataManager.extractLengthFromSpec(item.spec) : 6000;
                    const meters = item.shipped * length / 1000;
                    productionShipped += meters;
                    
                    if (index < 5) {
                        console.log(`${index + 1}. ${item.spec}: ${item.shipped}根 × ${length}mm = ${meters.toFixed(1)}米`);
                    }
                }
            });
            
            console.log(`📦 生产数据发货统计: ${shippedCount}条记录, ${productionShipped.toFixed(1)}米`);
        }
    }
    
    // 立即执行
    debugCustomerData();
    
    // 也在页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', debugCustomerData);
    }
    
    // 延迟执行确保所有组件都加载完成
    setTimeout(debugCustomerData, 2000);
    setTimeout(debugCustomerData, 5000);
    
})();
