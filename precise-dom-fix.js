// 精确DOM修复脚本 - 查找并修复所有可能的发货量显示元素

(function() {
    'use strict';
    
    console.log('🎯 精确DOM修复 - 查找所有51.8显示元素...');
    
    function preciseDOMFix() {
        const correctShippedMeters = 3831;
        
        console.log(`🔧 开始精确修复发货量为: ${correctShippedMeters}米`);
        
        // 1. 查找所有包含51.8的元素
        console.log('🔍 查找所有包含51.8的元素...');
        
        const allElements = document.querySelectorAll('*');
        const elementsWithValue = [];
        
        allElements.forEach(el => {
            const text = el.textContent?.trim();
            if (text === '51.8' || text === '51.8米' || text.includes('51.8')) {
                // 检查是否是叶子节点（没有子元素包含文本）
                const hasTextChildren = Array.from(el.children).some(child => 
                    child.textContent?.includes('51.8')
                );
                
                if (!hasTextChildren) {
                    elementsWithValue.push({
                        element: el,
                        text: text,
                        className: el.className,
                        tagName: el.tagName,
                        id: el.id,
                        parent: el.parentElement?.className || 'no-parent'
                    });
                    
                    console.log(`找到元素: ${el.tagName}.${el.className} - "${text}"`);
                }
            }
        });
        
        console.log(`📊 找到 ${elementsWithValue.length} 个包含51.8的元素`);
        
        // 2. 更新所有找到的元素
        if (elementsWithValue.length > 0) {
            elementsWithValue.forEach((item, index) => {
                const { element, text } = item;
                
                console.log(`更新元素 ${index + 1}: "${text}" -> "${correctShippedMeters.toFixed(1)}"`);
                
                // 更新文本内容
                if (text === '51.8') {
                    element.textContent = correctShippedMeters.toFixed(1);
                } else if (text === '51.8米') {
                    element.textContent = `${correctShippedMeters.toFixed(1)}米`;
                } else {
                    // 替换文本中的51.8部分
                    element.textContent = text.replace('51.8', correctShippedMeters.toFixed(1));
                }
                
                // 添加强烈的视觉效果
                element.style.cssText = `
                    background: linear-gradient(45deg, #10b981, #059669) !important;
                    color: white !important;
                    font-weight: bold !important;
                    font-size: 1.3em !important;
                    padding: 10px 15px !important;
                    border-radius: 10px !important;
                    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.5) !important;
                    transform: scale(1.1) !important;
                    transition: all 0.5s ease !important;
                    border: 3px solid #059669 !important;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
                `;
                
                console.log(`✅ 元素 ${index + 1} 已更新并添加视觉效果`);
            });
        } else {
            console.log('❌ 没有找到包含51.8的元素');
            
            // 尝试其他可能的选择器
            const alternativeSelectors = [
                '.metric-value',
                '[data-metric="shipped"]',
                '.shipped .value',
                '.dashboard-metric .number',
                '.stat-number',
                '.counter'
            ];
            
            console.log('🔍 尝试其他选择器...');
            alternativeSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    const text = el.textContent?.trim();
                    console.log(`${selector}: "${text}"`);
                    
                    if (text && (text.includes('51') || parseFloat(text) === 51.8)) {
                        console.log(`✅ 通过选择器 ${selector} 找到元素，更新中...`);
                        el.textContent = correctShippedMeters.toFixed(1);
                        
                        el.style.cssText = `
                            background: #10b981 !important;
                            color: white !important;
                            font-weight: bold !important;
                            padding: 8px 12px !important;
                            border-radius: 8px !important;
                        `;
                    }
                });
            });
        }
        
        // 3. 特殊处理：直接通过文本内容查找
        console.log('🔍 通过文本内容直接查找...');
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );
        
        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            if (node.textContent.trim() === '51.8') {
                textNodes.push(node);
            }
        }
        
        console.log(`找到 ${textNodes.length} 个文本节点包含51.8`);
        textNodes.forEach((textNode, index) => {
            console.log(`更新文本节点 ${index + 1}: "${textNode.textContent}" -> "${correctShippedMeters.toFixed(1)}"`);
            textNode.textContent = correctShippedMeters.toFixed(1);
            
            // 为父元素添加样式
            if (textNode.parentElement) {
                textNode.parentElement.style.cssText = `
                    background: #10b981 !important;
                    color: white !important;
                    font-weight: bold !important;
                    padding: 8px 12px !important;
                    border-radius: 8px !important;
                `;
            }
        });
        
        // 4. 更新仪表板数据
        if (window.dashboard && window.dashboard.data) {
            window.dashboard.data.shippedMeters = correctShippedMeters;
            window.dashboard.data.unshippedMeters = (window.dashboard.data.producedMeters || 73664.2) - correctShippedMeters;
            console.log('✅ 仪表板数据已更新');
        }
        
        // 5. 保存数据
        if (window.dataManager) {
            window.dataManager.saveToLocalStorage();
            console.log('✅ 数据已保存');
        }
        
        // 6. 创建确认提示
        createConfirmationMessage(correctShippedMeters);
        
        // 7. 最终验证
        setTimeout(() => {
            console.log('🔍 最终验证...');
            const finalCheck = document.querySelectorAll('*');
            let found = false;
            
            finalCheck.forEach(el => {
                const text = el.textContent?.trim();
                if (text === correctShippedMeters.toFixed(1) || text === `${correctShippedMeters.toFixed(1)}米`) {
                    console.log(`✅ 验证成功: 找到显示 ${text} 的元素`);
                    found = true;
                }
                if (text === '51.8' || text === '51.8米') {
                    console.log(`⚠️ 仍有元素显示51.8: ${el.tagName}.${el.className}`);
                }
            });
            
            if (found) {
                console.log('🎉 修复成功！');
            } else {
                console.log('❌ 修复可能失败，需要手动检查');
            }
        }, 2000);
        
        console.log('🎉 精确DOM修复完成！');
    }
    
    function createConfirmationMessage(shippedMeters) {
        // 创建浮动确认消息
        const message = document.createElement('div');
        message.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
            padding: 20px 30px;
            border-radius: 15px;
            font-size: 18px;
            font-weight: bold;
            z-index: 10000;
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.4);
            animation: slideDown 0.5s ease;
        `;
        
        message.innerHTML = `
            <div style="text-align: center;">
                <div style="font-size: 24px; margin-bottom: 5px;">🎯</div>
                <div>发货量已强制更新为</div>
                <div style="font-size: 24px; margin-top: 5px;">${shippedMeters.toFixed(1)} 米</div>
            </div>
        `;
        
        document.body.appendChild(message);
        
        // 添加动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                to { transform: translateX(-50%) translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        // 5秒后消失
        setTimeout(() => {
            message.style.opacity = '0';
            message.style.transition = 'opacity 0.5s ease';
            setTimeout(() => message.remove(), 500);
        }, 5000);
    }
    
    // 添加全局方法
    window.preciseDOMFix = preciseDOMFix;
    
    // 立即执行
    setTimeout(() => {
        console.log('🚀 自动执行精确DOM修复...');
        preciseDOMFix();
    }, 3000);
    
    console.log('🚀 精确DOM修复脚本已加载');
    console.log('💡 可用命令: preciseDOMFix()');
    
})();
