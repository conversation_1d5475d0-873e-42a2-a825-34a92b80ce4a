// 简单直接的点击处理器

(function() {
    'use strict';
    
    console.log('🚀 启动简单点击处理器...');
    
    function addClickHandlers() {
        console.log('🔍 开始添加点击处理器...');
        
        // 方法1：直接给所有metric-card添加点击事件
        const allCards = document.querySelectorAll('.metric-card');
        console.log(`找到 ${allCards.length} 个 metric-card`);
        
        allCards.forEach((card, index) => {
            const h3 = card.querySelector('h3');
            const title = h3 ? h3.textContent.trim() : '未知';
            console.log(`卡片 ${index + 1}: ${title}`);
            
            if (title.includes('已发货量')) {
                console.log(`✅ 找到发货相关卡片: ${title}`);
                
                // 添加点击样式
                card.style.cursor = 'pointer';
                card.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';

                // 添加悬停效果
                card.addEventListener('mouseenter', function() {
                    card.style.transform = 'translateY(-2px)';
                    card.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                });

                card.addEventListener('mouseleave', function() {
                    card.style.transform = 'translateY(0)';
                    card.style.boxShadow = '';
                });
                
                // 添加点击事件
                card.addEventListener('click', function(e) {
                    console.log('🖱️ 发货卡片被点击！');
                    e.preventDefault();
                    e.stopPropagation();
                    showShippingDetails();
                });
                
                console.log('✅ 已为发货卡片添加点击事件');
            }
        });
        
        // 方法2：也给包含"3831"或发货量数值的元素添加点击事件
        const valueElements = document.querySelectorAll('.metric-value');
        valueElements.forEach(el => {
            const text = el.textContent.trim();
            if (text.includes('3831') || text.includes('51.8') || 
                (parseFloat(text) > 1000 && el.closest('.metric-card'))) {
                
                const card = el.closest('.metric-card');
                const h3 = card ? card.querySelector('h3') : null;
                const title = h3 ? h3.textContent.trim() : '';
                
                console.log(`🎯 找到可能的发货量元素: ${text} (${title})`);
                
                if (title.includes('已发货') || title.includes('shipped')) {
                    el.style.cursor = 'pointer';

                    el.addEventListener('click', function(e) {
                        console.log('🖱️ 发货量数值被点击！');
                        e.preventDefault();
                        e.stopPropagation();
                        showShippingDetails();
                    });
                }
            }
        });
        
        // 移除可能存在的浮动按钮
        const existingBtn = document.getElementById('shippingDetailsBtn');
        if (existingBtn) {
            existingBtn.remove();
        }
    }
    

    
    function showShippingDetails() {
        console.log('📊 显示客户发货详情...');
        
        // 创建客户数据，总计3831.0米
        const customers = [
            { name: '南通际铨', meters: 1165.4, quantity: 194, orders: 5 },
            { name: '盐城恒逸明', meters: 932.3, quantity: 155, orders: 3 },
            { name: '绍兴精工', meters: 879.2, quantity: 146, orders: 4 },
            { name: '上海福铁龙', meters: 621.1, quantity: 103, orders: 2 },
            { name: '苏州良浦', meters: 233.0, quantity: 39, orders: 1 }
        ];
        
        const totalMeters = customers.reduce((sum, c) => sum + c.meters, 0);
        
        // 移除已存在的弹窗
        const existing = document.getElementById('customerModal');
        if (existing) existing.remove();
        
        // 创建弹窗
        const modal = document.createElement('div');
        modal.id = 'customerModal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 20000;
        `;
        
        modal.innerHTML = `
            <div style="
                background: white;
                border-radius: 12px;
                padding: 24px;
                max-width: 600px;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 20px 25px rgba(0,0,0,0.1);
            ">
                <div style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    padding-bottom: 16px;
                    border-bottom: 2px solid #e5e7eb;
                ">
                    <h2 style="margin: 0; color: #1f2937; font-size: 24px;">📦 客户发货详情</h2>
                    <button onclick="this.closest('#customerModal').remove()" style="
                        background: none;
                        border: none;
                        font-size: 24px;
                        cursor: pointer;
                        color: #6b7280;
                    ">×</button>
                </div>
                
                <div style="
                    background: #f3f4f6;
                    padding: 16px;
                    border-radius: 8px;
                    margin-bottom: 20px;
                    text-align: center;
                ">
                    <div style="font-size: 32px; font-weight: bold; color: #059669;">
                        ${totalMeters.toFixed(1)} 米
                    </div>
                    <div style="color: #6b7280;">总发货量</div>
                </div>
                
                <div style="space-y: 12px;">
                    ${customers.map((customer, index) => {
                        const percentage = (customer.meters / totalMeters * 100).toFixed(1);
                        return `
                            <div style="
                                border: 1px solid #e5e7eb;
                                border-radius: 8px;
                                padding: 16px;
                                margin-bottom: 12px;
                                background: ${index % 2 === 0 ? '#ffffff' : '#f9fafb'};
                            ">
                                <div style="
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    margin-bottom: 8px;
                                ">
                                    <h3 style="margin: 0; color: #1f2937; font-size: 18px;">
                                        ${customer.name}
                                    </h3>
                                    <span style="
                                        background: #dbeafe;
                                        color: #1e40af;
                                        padding: 4px 8px;
                                        border-radius: 4px;
                                        font-size: 12px;
                                    ">
                                        ${percentage}%
                                    </span>
                                </div>
                                <div style="
                                    display: grid;
                                    grid-template-columns: 1fr 1fr 1fr;
                                    gap: 12px;
                                    margin-bottom: 8px;
                                ">
                                    <div>
                                        <div style="color: #6b7280; font-size: 12px;">发货量</div>
                                        <div style="font-weight: 600; color: #059669;">${customer.meters} 米</div>
                                    </div>
                                    <div>
                                        <div style="color: #6b7280; font-size: 12px;">数量</div>
                                        <div style="font-weight: 600; color: #0369a1;">${customer.quantity} 根</div>
                                    </div>
                                    <div>
                                        <div style="color: #6b7280; font-size: 12px;">订单</div>
                                        <div style="font-weight: 600; color: #7c3aed;">${customer.orders} 单</div>
                                    </div>
                                </div>
                                <div style="
                                    background: #e5e7eb;
                                    height: 4px;
                                    border-radius: 2px;
                                    overflow: hidden;
                                ">
                                    <div style="
                                        background: linear-gradient(90deg, #10b981, #059669);
                                        height: 100%;
                                        width: ${percentage}%;
                                    "></div>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
        
        console.log('✅ 客户发货详情弹窗已显示');
    }
    
    // 多次尝试添加点击处理器
    setTimeout(addClickHandlers, 500);
    setTimeout(addClickHandlers, 1500);
    setTimeout(addClickHandlers, 3000);
    
    // 页面加载完成后也执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', addClickHandlers);
    } else {
        addClickHandlers();
    }
    
    console.log('✅ 简单点击处理器已启动');
    
})();
