<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速修复已发货量</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn:hover { opacity: 0.9; transform: translateY(-2px); }
        .status {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .warning { border-color: #ffc107; background: #fff3cd; }
        .big-number {
            font-size: 48px;
            font-weight: bold;
            color: #2c3e50;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 快速修复已发货量</h1>
        <p>一键解决已发货量显示为0的问题</p>

        <div class="big-number" id="currentShipped">检查中...</div>
        <div>当前已发货量 (米)</div>

        <div style="margin: 30px 0;">
            <button class="btn btn-primary" onclick="checkCurrentStatus()">🔍 检查当前状态</button>
            <button class="btn btn-success" onclick="createTestData()">➕ 创建测试发货</button>
            <button class="btn btn-warning" onclick="forceFixNow()">🔧 立即修复</button>
        </div>

        <div style="margin: 20px 0;">
            <button class="btn btn-primary" onclick="openMainSystem()">🏠 返回主系统</button>
        </div>

        <div class="status" id="statusDisplay">
            等待操作...
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('statusDisplay');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            console.log(message);
        }

        function updateShippedDisplay(value) {
            const display = document.getElementById('currentShipped');
            display.textContent = value;
        }

        function checkCurrentStatus() {
            updateStatus('正在检查当前状态...', 'info');
            
            try {
                // 检查主窗口
                if (window.opener && window.opener.dataManager) {
                    const dm = window.opener.dataManager;
                    const dashboard = window.opener.dashboard;
                    
                    updateStatus(`✅ 连接成功<br>
                        📊 生产数据: ${dm.data.length} 条<br>
                        📦 发货历史: ${dm.shippingHistory.length} 条<br>
                        📈 仪表板已发货: ${dashboard?.data?.shippedMeters?.toFixed(1) || 0} 米`, 'success');
                    
                    updateShippedDisplay(dashboard?.data?.shippedMeters?.toFixed(1) || '0.0');
                    
                } else {
                    updateStatus('❌ 无法连接到主系统，请从主系统打开此页面', 'error');
                    updateShippedDisplay('N/A');
                }
            } catch (error) {
                updateStatus(`❌ 检查状态失败: ${error.message}`, 'error');
                updateShippedDisplay('错误');
            }
        }

        function createTestData() {
            updateStatus('正在创建测试发货数据...', 'warning');
            
            try {
                if (!window.opener || !window.opener.dataManager) {
                    updateStatus('❌ 无法连接到主系统', 'error');
                    return;
                }
                
                const dm = window.opener.dataManager;
                
                // 找到有生产数量的项目
                const itemsWithProduction = dm.data.filter(item => item.produced > 0);
                
                if (itemsWithProduction.length === 0) {
                    updateStatus('❌ 没有已生产的项目，无法创建发货数据', 'error');
                    return;
                }
                
                // 选择第一个项目
                const item = itemsWithProduction[0];
                const shippedQuantity = Math.min(20, Math.floor(item.produced * 0.5)); // 发货50%或20根
                
                if (shippedQuantity <= 0) {
                    updateStatus('❌ 生产数量不足，无法创建发货数据', 'error');
                    return;
                }
                
                // 更新shipped字段
                item.shipped = (item.shipped || 0) + shippedQuantity;
                
                // 创建发货历史记录
                const shippingRecord = {
                    id: Date.now(),
                    documentNumber: 'QUICK' + Date.now(),
                    date: new Date().toISOString().split('T')[0],
                    customerName: '快速修复测试客户',
                    items: [{
                        spec: item.spec,
                        area: item.area,
                        quantity: shippedQuantity,
                        weight: shippedQuantity * 0.6, // 假设每根0.6kg
                        meters: shippedQuantity * 6 // 假设每根6米
                    }],
                    totalQuantity: shippedQuantity,
                    totalWeight: shippedQuantity * 0.6,
                    totalMeters: shippedQuantity * 6,
                    remarks: '快速修复工具创建的测试数据'
                };
                
                dm.shippingHistory.push(shippingRecord);
                dm.saveToLocalStorage();
                dm.updateStats();
                
                updateStatus(`✅ 成功创建测试发货数据<br>
                    📦 规格: ${item.spec}<br>
                    📍 区域: ${item.area}<br>
                    📊 数量: ${shippedQuantity} 根<br>
                    📏 长度: ${shippedQuantity * 6} 米`, 'success');
                
                // 延迟检查结果
                setTimeout(() => {
                    checkCurrentStatus();
                }, 1000);
                
            } catch (error) {
                updateStatus(`❌ 创建测试数据失败: ${error.message}`, 'error');
            }
        }

        function forceFixNow() {
            updateStatus('正在执行强制修复...', 'warning');
            
            try {
                if (!window.opener || !window.opener.dataManager) {
                    updateStatus('❌ 无法连接到主系统', 'error');
                    return;
                }
                
                const dm = window.opener.dataManager;
                const dashboard = window.opener.dashboard;
                
                // 1. 确保所有数据项都有shipped字段
                dm.data.forEach(item => {
                    if (typeof item.shipped === 'undefined') {
                        item.shipped = 0;
                    }
                });
                
                // 2. 从发货历史重新计算shipped字段
                dm.data.forEach(item => {
                    item.shipped = 0;
                });
                
                dm.shippingHistory.forEach(record => {
                    if (record.items && Array.isArray(record.items)) {
                        record.items.forEach(shippedItem => {
                            const dataItem = dm.data.find(item => 
                                item.spec === shippedItem.spec && item.area === shippedItem.area
                            );
                            if (dataItem) {
                                dataItem.shipped = (dataItem.shipped || 0) + (shippedItem.quantity || 0);
                            }
                        });
                    }
                });
                
                // 3. 重新计算仪表板数据
                let totalShippedMeters = 0;
                dm.data.forEach(item => {
                    const shipped = item.shipped || 0;
                    if (shipped > 0) {
                        // 简单的长度提取
                        let length = 6000; // 默认6米
                        if (item.spec && item.spec.includes('6000')) {
                            length = 6000;
                        } else if (item.spec && item.spec.includes('12000')) {
                            length = 12000;
                        }
                        
                        totalShippedMeters += shipped * length / 1000;
                    }
                });
                
                // 4. 直接更新仪表板
                if (dashboard && dashboard.data) {
                    dashboard.data.shippedMeters = totalShippedMeters;
                    dashboard.data.unshippedMeters = (dashboard.data.producedMeters || 0) - totalShippedMeters;
                }
                
                // 5. 强制更新显示
                dashboard.updateMetricsFromDataManager();
                dashboard.updateMetrics();
                
                // 6. 保存数据
                dm.saveToLocalStorage();
                
                updateStatus(`✅ 强制修复完成<br>
                    📊 重新计算了 ${dm.data.length} 条数据<br>
                    📦 处理了 ${dm.shippingHistory.length} 条发货记录<br>
                    📈 已发货量: ${totalShippedMeters.toFixed(1)} 米`, 'success');
                
                // 延迟检查结果
                setTimeout(() => {
                    checkCurrentStatus();
                }, 1000);
                
            } catch (error) {
                updateStatus(`❌ 强制修复失败: ${error.message}`, 'error');
            }
        }

        function openMainSystem() {
            if (window.opener) {
                window.opener.focus();
                window.close();
            } else {
                window.open('index.html', '_blank');
            }
        }

        // 页面加载时自动检查状态
        window.addEventListener('DOMContentLoaded', function() {
            updateStatus('快速修复工具已加载，正在检查状态...', 'info');
            
            setTimeout(() => {
                checkCurrentStatus();
            }, 500);
        });
    </script>
</body>
</html>
