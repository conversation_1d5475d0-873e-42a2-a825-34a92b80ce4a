// 修复已发货量显示问题的脚本

(function() {
    'use strict';
    
    console.log('🔧 开始修复已发货量显示问题...');
    
    // 等待DataManager加载完成
    function waitForDataManager() {
        return new Promise((resolve) => {
            const checkDataManager = () => {
                if (window.dataManager) {
                    resolve(window.dataManager);
                } else {
                    setTimeout(checkDataManager, 100);
                }
            };
            checkDataManager();
        });
    }
    
    // 修复已发货量显示
    async function fixShippedQuantity() {
        try {
            const dataManager = await waitForDataManager();
            console.log('✅ DataManager已加载');
            
            // 1. 检查数据中的shipped字段
            console.log('🔍 检查数据中的shipped字段...');
            let totalShipped = 0;
            let recordsWithShipped = 0;
            
            dataManager.data.forEach((item, index) => {
                if (item.shipped && item.shipped > 0) {
                    recordsWithShipped++;
                    totalShipped += item.shipped;
                    if (index < 5) { // 显示前5条有发货记录的数据
                        console.log(`✅ 第${index + 1}条有发货: ${item.spec} (${item.area}) - 已发货: ${item.shipped}根`);
                    }
                } else {
                    // 初始化shipped字段
                    if (typeof item.shipped === 'undefined') {
                        item.shipped = 0;
                    }
                }
            });
            
            console.log(`📊 发货统计: ${recordsWithShipped} 条记录有发货数据，总计 ${totalShipped} 根`);
            
            // 2. 检查发货历史记录
            console.log('🔍 检查发货历史记录...');
            if (dataManager.shippingHistory && dataManager.shippingHistory.length > 0) {
                console.log(`📦 发货历史记录: ${dataManager.shippingHistory.length} 条`);
                
                // 重新计算shipped字段
                console.log('🔄 重新计算shipped字段...');
                
                // 先重置所有shipped字段
                dataManager.data.forEach(item => {
                    item.shipped = 0;
                });
                
                // 从发货历史重新计算
                dataManager.shippingHistory.forEach(record => {
                    if (record.items && Array.isArray(record.items)) {
                        record.items.forEach(shippedItem => {
                            // 查找对应的数据项
                            const dataItem = dataManager.data.find(item => 
                                item.spec === shippedItem.spec && item.area === shippedItem.area
                            );
                            
                            if (dataItem) {
                                dataItem.shipped = (dataItem.shipped || 0) + (shippedItem.quantity || 0);
                            }
                        });
                    }
                });
                
                // 重新统计
                totalShipped = 0;
                recordsWithShipped = 0;
                dataManager.data.forEach(item => {
                    if (item.shipped && item.shipped > 0) {
                        recordsWithShipped++;
                        totalShipped += item.shipped;
                    }
                });
                
                console.log(`✅ 重新计算完成: ${recordsWithShipped} 条记录有发货数据，总计 ${totalShipped} 根`);
                
                // 保存数据
                dataManager.saveToLocalStorage();
                
            } else {
                console.log('⚠️ 没有发货历史记录');
            }
            
            // 3. 修复仪表板计算方法
            if (window.dashboard) {
                console.log('🔧 修复仪表板计算方法...');
                
                // 备份原始方法
                const originalUpdateMetrics = window.dashboard.updateMetricsFromDataManager;
                
                // 增强计算方法
                window.dashboard.updateMetricsFromDataManager = function() {
                    console.log('🔄 执行增强的updateMetricsFromDataManager...');
                    
                    if (!window.dataManager || !window.dataManager.data) {
                        console.error('❌ DataManager或数据不存在');
                        return;
                    }
                    
                    const data = window.dataManager.data;
                    console.log(`📊 处理 ${data.length} 条数据`);
                    
                    // 重新计算发货量
                    let shippedMeters = 0;
                    let shippedCount = 0;
                    
                    data.forEach((item, index) => {
                        const length = this.extractLengthFromSpec(item.spec);
                        const shipped = parseInt(item.shipped) || 0;
                        
                        if (shipped > 0) {
                            const meters = shipped * length / 1000;
                            shippedMeters += meters;
                            shippedCount++;
                            
                            if (index < 3) {
                                console.log(`✅ 发货计算 ${item.spec}: ${shipped}根 × ${length}mm = ${meters.toFixed(1)}米`);
                            }
                        }
                    });
                    
                    console.log(`📦 发货统计: ${shippedCount} 条记录，总计 ${shippedMeters.toFixed(1)} 米`);
                    
                    // 调用原始方法
                    if (originalUpdateMetrics) {
                        originalUpdateMetrics.call(this);
                    }
                    
                    // 确保发货量正确设置
                    this.data.shippedMeters = shippedMeters;
                    
                    // 重新计算未发货量
                    this.data.unshippedMeters = (this.data.producedMeters || 0) - shippedMeters;
                    
                    console.log('✅ 发货量计算完成:', {
                        shippedMeters: shippedMeters.toFixed(1),
                        unshippedMeters: this.data.unshippedMeters.toFixed(1)
                    });
                };
                
                // 立即重新计算
                window.dashboard.updateMetricsFromDataManager();
                window.dashboard.updateMetrics();
                
            } else {
                console.log('⚠️ Dashboard不存在');
            }
            
            // 4. 添加调试方法
            dataManager.debugShippedQuantity = function() {
                console.log('=== 已发货量调试信息 ===');
                
                let totalShipped = 0;
                let recordsWithShipped = 0;
                
                console.log('📊 数据记录中的发货情况:');
                this.data.forEach((item, index) => {
                    if (item.shipped && item.shipped > 0) {
                        recordsWithShipped++;
                        totalShipped += item.shipped;
                        console.log(`  ${index + 1}. ${item.spec} (${item.area}): ${item.shipped}根`);
                    }
                });
                
                console.log(`总计: ${recordsWithShipped} 条记录，${totalShipped} 根`);
                
                console.log('📦 发货历史记录:');
                if (this.shippingHistory && this.shippingHistory.length > 0) {
                    this.shippingHistory.forEach((record, index) => {
                        const totalQuantity = record.items ? record.items.reduce((sum, item) => sum + (item.quantity || 0), 0) : 0;
                        console.log(`  ${index + 1}. ${record.customerName} (${record.date}): ${totalQuantity}根`);
                    });
                } else {
                    console.log('  无发货历史记录');
                }
                
                // 检查仪表板数据
                if (window.dashboard && window.dashboard.data) {
                    console.log('📈 仪表板数据:');
                    console.log(`  已发货量: ${window.dashboard.data.shippedMeters || 0} 米`);
                    console.log(`  未发货量: ${window.dashboard.data.unshippedMeters || 0} 米`);
                }
                
                return {
                    recordsWithShipped,
                    totalShipped,
                    historyRecords: this.shippingHistory ? this.shippingHistory.length : 0,
                    dashboardShipped: window.dashboard?.data?.shippedMeters || 0
                };
            };
            
            // 5. 添加强制刷新方法
            dataManager.forceRefreshShipped = function() {
                console.log('🔄 强制刷新已发货量...');
                
                // 重新计算shipped字段
                this.data.forEach(item => {
                    item.shipped = 0;
                });
                
                // 从发货历史重新计算
                if (this.shippingHistory && this.shippingHistory.length > 0) {
                    this.shippingHistory.forEach(record => {
                        if (record.items && Array.isArray(record.items)) {
                            record.items.forEach(shippedItem => {
                                const dataItem = this.data.find(item => 
                                    item.spec === shippedItem.spec && item.area === shippedItem.area
                                );
                                
                                if (dataItem) {
                                    dataItem.shipped = (dataItem.shipped || 0) + (shippedItem.quantity || 0);
                                }
                            });
                        }
                    });
                }
                
                // 保存并更新显示
                this.saveToLocalStorage();
                this.updateStats();
                
                console.log('✅ 强制刷新完成');
            };
            
            // 6. 添加测试发货数据方法
            dataManager.addTestShippingData = function() {
                if (this.data.length === 0) {
                    console.log('❌ 没有生产数据，无法添加测试发货');
                    return null;
                }
                
                // 找一个有生产数量的项目
                const itemWithProduction = this.data.find(item => item.produced > 0);
                if (!itemWithProduction) {
                    console.log('❌ 没有已生产的项目，无法添加测试发货');
                    return null;
                }
                
                // 添加测试发货
                const testQuantity = Math.min(10, itemWithProduction.produced);
                itemWithProduction.shipped = (itemWithProduction.shipped || 0) + testQuantity;
                
                // 添加到发货历史
                const testRecord = {
                    id: Date.now() + Math.random(),
                    documentNumber: 'TEST' + Date.now(),
                    date: new Date().toISOString().split('T')[0],
                    customerName: '测试客户',
                    items: [{
                        spec: itemWithProduction.spec,
                        area: itemWithProduction.area,
                        quantity: testQuantity,
                        weight: testQuantity * 0.5, // 假设重量
                        meters: testQuantity * 6 // 假设长度
                    }],
                    totalQuantity: testQuantity,
                    totalWeight: testQuantity * 0.5,
                    totalMeters: testQuantity * 6,
                    remarks: '测试发货数据 - 可删除'
                };
                
                this.shippingHistory.push(testRecord);
                this.saveToLocalStorage();
                this.updateStats();
                
                console.log('✅ 已添加测试发货数据:', testRecord);
                return testRecord;
            };
            
            console.log('✅ 已发货量修复完成');
            
            // 立即刷新显示
            if (window.dashboard) {
                window.dashboard.updateMetricsFromDataManager();
                window.dashboard.updateMetrics();
            }
            
            return dataManager;
            
        } catch (error) {
            console.error('❌ 修复已发货量时出错:', error);
            throw error;
        }
    }
    
    // 页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixShippedQuantity);
    } else {
        fixShippedQuantity();
    }
    
    // 导出到全局作用域，方便调试
    window.fixShippedQuantity = fixShippedQuantity;
    
})();

// 添加全局调试函数
window.debugShipped = function() {
    if (window.dataManager && window.dataManager.debugShippedQuantity) {
        return window.dataManager.debugShippedQuantity();
    } else {
        console.log('DataManager或调试方法不存在');
        return null;
    }
};

window.forceRefreshShipped = function() {
    if (window.dataManager && window.dataManager.forceRefreshShipped) {
        window.dataManager.forceRefreshShipped();
    } else {
        console.log('DataManager或强制刷新方法不存在');
    }
};

window.addTestShipping = function() {
    if (window.dataManager && window.dataManager.addTestShippingData) {
        return window.dataManager.addTestShippingData();
    } else {
        console.log('DataManager或添加测试数据方法不存在');
        return null;
    }
};

console.log('🚀 已发货量修复脚本已加载');
console.log('💡 可用的调试命令:');
console.log('  - debugShipped() - 查看发货调试信息');
console.log('  - forceRefreshShipped() - 强制刷新发货量');
console.log('  - addTestShipping() - 添加测试发货数据');
